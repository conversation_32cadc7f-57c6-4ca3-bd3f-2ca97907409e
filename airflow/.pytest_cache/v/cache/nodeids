["tests/aetion/adip/airflow/integrations/google/test_google_drive_reader.py::test_are_equal[data specification - amgen - flatiron_all - 20250701-data specification - amgen - flatiron_all - 20250701.xlsx]", "tests/aetion/adip/airflow/integrations/google/test_google_drive_reader.py::test_are_equal[data specification - amgen - flatiron_all - 20250701-data specification - amgen - flatiron_all - 20250701]", "tests/aetion/adip/airflow/integrations/google/test_google_drive_reader.py::test_are_equal[data specification - amgen - flatiron_all - 20250701.xlsx-data specification - amgen - flatiron_all - 20250701.xlsx]", "tests/aetion/adip/airflow/integrations/google/test_google_drive_reader.py::test_are_equal[data specification - amgen - flatiron_all - 20250701.xlsx-data specification - amgen - flatiron_all - 20250701]", "tests/aetion/adip/airflow/integrations/google/test_google_drive_reader.py::test_are_equal[data specification - amgen - flatiron_all-data specification - amgen - flatiron_all]", "tests/aetion/adip/airflow/integrations/google/test_google_drive_reader.py::test_are_equal[data specification - amgen - flatiron_all-data specification - amgen - flatiron_alls]", "tests/aetion/adip/airflow/integrations/google/test_google_drive_reader.py::test_google_drive_reader", "tests/aetion/adip/airflow/integrations/google/test_google_drive_reader.py::test_integration_google_drive_reader_read_file_successfully", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_cat_binary_data", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_cat_non_binary_chinese", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_client_property", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_copy_basic", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_copy_basic_directory", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_exists", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_get_recursive", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_get_single_file[changed_file_name]", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_get_single_file[file]", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_get_single_file[target_folder]", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_hook_property", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_info", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_info_not_found", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_init", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_listdir", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_open_binary_modes", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_open_not_implemented", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_open_read_mode", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_open_write_mode", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_parse_s3_path_invalid", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_parse_s3_path_valid[bucket/key]", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_parse_s3_path_valid[s3://]", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_parse_s3_path_valid[s3a://]", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_put_bytesio", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_put_file_path", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_put_non_valid_data[None]", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_put_non_valid_data[bool]", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_put_non_valid_data[float]", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_put_non_valid_data[int]", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_read_file_content_local", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_read_file_content_s3", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_rm_recursive", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_rm_recursive_but_no_file_found_to_delete", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_rm_single_file", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_split_path[123]", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_split_path[None]", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_split_path_invalid", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_upload_dir_parallel", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_upload_directory", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_upload_file", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_all", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_basic_functionality", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_empty_directory", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_error_handling_continues", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_invalid_path_format", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_maxdepth_one", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_maxdepth_unlimited", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_maxdepth_zero", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_nonexistent_path", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_recursive_traversal", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_s3_root_path", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_trailing_slash_handling", "tests/aetion/adip/airflow/integrations/s3/test_s3_support.py::TestAetionS3FileSystem::test_walk_tuple_structure_compatibility", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_copy_dictionaries_filesystem_error", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_copy_dictionaries_success", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_copy_dictionaries_with_custom_parallelism", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_copy_dictionaries_with_multiple_threads", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_execute_success", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_fs_property_lazy_loading", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_init_with_all_parameters", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_init_with_minimal_parameters", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_resolve_enums_both_files_exist", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_resolve_enums_neither_exists", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_resolve_enums_only_new_exists", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_resolve_enums_only_previous_exists", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_resolve_enums_overlapping_codes_keeps_previous", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_resolve_enums_overlapping_enum_names_different_codes", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_resolve_enums_preserves_order", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_resolve_enums_with_empty_files", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_resolve_enums_with_malformed_csv", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_resolve_enums_with_unicode_characters", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_template_ext", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_template_fields", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_upload_enums_complex_structure", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_upload_enums_empty_dict", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_upload_enums_filesystem_error", "tests/aetion/adip/airflow/operators/test_copy_enums_and_dictionaries.py::TestCopyEnumsAndDictionaries::test_upload_enums_success", "tests/aetion/adip/airflow/operators/test_copy_spec.py::test_copy_spec_from_gdrive_to_s3", "tests/aetion/adip/airflow/operators/test_copy_spec.py::test_copy_spec_from_s3_to_s3", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_catalog_file_read_error", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_complex_glob_patterns", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_empty_file_inventory_validation", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_execute[client0-dataset0]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_execute[client0-dataset1]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_execute[client0-missing_table]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_execute_error", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_execute_large_file_inventory", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_expected_files_with_skip_loading_table_groups", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_expected_files_with_skip_loading_tables", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_expected_files_with_table_groups_filtering", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_file_options_inheritance_from_default", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_file_options_with_no_default_options", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_file_validator", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_fs_property_lazy_initialization", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_get_catalog_from_s3[client0-dataset0-expected_default_options0-expected_table_options0]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_get_catalog_from_s3[client0-dataset1-expected_default_options1-expected_table_options1]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_get_catalog_from_s3[client0-missing_table-expected_default_options2-expected_table_options2]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_inventory_files_property_processing", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_inventory_files_property_should_be_relative_paths_to_raw_data_path", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_inventory_summary[inventory0-yaml_catalog0-expected_output0]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_inventory_summary[inventory1-yaml_catalog1-expected_output1]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_inventory_summary[inventory2-yaml_catalog2-expected_output2]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_inventory_summary[inventory3-yaml_catalog3-expected_output3]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_inventory_summary[inventory4-yaml_catalog4-expected_output4]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_inventory_summary[inventory5-yaml_catalog5-expected_output5]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_inventory_summary[inventory6-yaml_catalog6-expected_output6]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_inventory_summary[inventory7-yaml_catalog7-expected_output7]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_lookup_table_functionality", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_lookup_table_inventory_summary", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_validate[expected_table_globs0-ignored_globs0-s3_files0-file_options0-lookup_tables0]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_validate[expected_table_globs1-ignored_globs1-s3_files1-file_options1-lookup_tables1]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_validate[expected_table_globs2-ignored_globs2-s3_files2-file_options2-lookup_tables2]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_validate[expected_table_globs3-ignored_globs3-s3_files3-file_options3-lookup_tables3]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_validate[expected_table_globs4-ignored_globs4-s3_files4-file_options4-lookup_tables4]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_validate[expected_table_globs5-ignored_globs5-s3_files5-file_options5-lookup_tables5]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_validate[expected_table_globs6-ignored_globs6-s3_files6-file_options6-lookup_tables6]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_validate_errors[expected_table_globs0-ignored_globs0-s3_files0-file_options0-lookup_tables0-Found files in s3://test-bucket/resources/raw/ that do not have an associated table: \\\\['bcd1.csv', 'bcd2.csv'\\\\]. Please add them to be ignored or as lookup_tables in the raw data catalog]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_validate_errors[expected_table_globs1-ignored_globs1-s3_files1-file_options1-lookup_tables1-The following tables had 0 files matching in s3://test-bucket/resources/raw/: \\\\['bcd\\\\*.csv'\\\\]. Please check the raw data catalog]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_validate_errors[expected_table_globs2-ignored_globs2-s3_files2-file_options2-lookup_tables2-The following files were found to match multiple tables in s3://test-bucket/resources/raw/: {'ambiguous.csv': \\\\['ambiguous\\\\*.csv', 'ambiguou\\\\*.csv'\\\\]}. Please correct the ambiguity.]", "tests/aetion/adip/airflow/operators/test_file_validator.py::test_validate_errors[expected_table_globs3-ignored_globs3-s3_files3-file_options3-lookup_tables3-The following lookup tables were defined in the raw data catalog but not found in s3://test-bucket/resources/raw/: \\\\['med.csv'\\\\]. Please check the raw data catalog]", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_check_git_version", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_check_git_version_ignore_non_zip_like_files", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_check_git_version_missing_git_version_file", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_check_revision_must_be_numeric[non-numeric_revision]", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_check_revision_must_be_numeric[none]", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_check_revision_success", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_copy_ama_artifacts", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_copy_artifacts", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_copy_artifacts_from_git_to_s3_missing_artifact", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_copy_artifacts_from_git_to_s3_success", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_copy_artifacts_missing_in_global", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_copy_git_artifacts_config_copied", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_copy_git_artifacts_nothing_is_copied", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_copy_git_artifacts_rdc_copied", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_copy_prophecy_artifacts", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_download_ndc_lookup_file_artifact_not_found", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_download_ndc_lookup_file_custom_git_branch", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_download_ndc_lookup_file_filesystem_upload_error", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_download_ndc_lookup_file_git_clone_failure", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_download_ndc_lookup_file_git_hook_context_manager_error", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_download_ndc_lookup_file_load_ndc_mapping_error", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_download_ndc_lookup_file_logging_behavior", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_download_ndc_lookup_file_path_already_has_metadata_store_prefix", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_download_ndc_lookup_file_path_with_metadata_store_prefix", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_download_ndc_lookup_file_skipped_when_no_git_meta_repo", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_download_ndc_lookup_file_success", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_download_ndc_lookup_file_temporary_directory_error", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_execute_success[none]", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_execute_success[numeric_revision]", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_init_with_all_parameters", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_load_ndc_mapping_file_name_case_sensitive_match", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_load_ndc_mapping_file_name_csv_parsing_with_quotes", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_load_ndc_mapping_file_name_empty_csv", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_load_ndc_mapping_file_name_exact_whitespace_match", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_load_ndc_mapping_file_name_file_not_found", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_load_ndc_mapping_file_name_malformed_csv_extra_columns", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_load_ndc_mapping_file_name_malformed_csv_insufficient_columns", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_load_ndc_mapping_file_name_missing_ndc_entry", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_load_ndc_mapping_file_name_multiple_ndc_entries", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_load_ndc_mapping_file_name_permission_error", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_load_ndc_mapping_file_name_success", "tests/aetion/adip/airflow/operators/test_publish_artifacts.py::TestArtifactsPublisher::test_load_ndc_mapping_file_name_whitespace_in_data", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_adapt_spark_conf_does_nothing_when_disabled", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_adapt_spark_conf_updates_conf", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_combine_prefer_child_values_when_child_enabled[False]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_combine_prefer_child_values_when_child_enabled[True]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_combine_when_child_disabled_by_parent_enabled", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_combine_with_none", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_feature_is_disabled_by_default", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_generate_python_path_env[None-None--None]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_generate_python_path_env[not-python-None--None]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_generate_python_path_env[python-args2--expected2]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_generate_python_path_env[python-args3--expected3]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_generate_python_path_env[python-args4--expected4]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_generate_python_path_env[python-args5-adip-spark-prophecy:sometag-expected5]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_generate_python_path_env[python-args6--None]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_generate_python_path_env_disabled_returns_none", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_resolve_docker_image[False-foo:bar-None-None-foo:bar]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_resolve_docker_image[True-global-adip-spark-prophecy:tag-None-None-global-adip-spark-prophecy:tag]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_resolve_docker_image[True-global-adip-spark-prophecy:tag-child-prophecy-image:tag-None-child-prophecy-image:tag]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_resolve_docker_image[True-global-adip-spark:tag-whatever-None-global-adip-spark:tag]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_resolve_docker_image[True-global-adip-spark:tag-whatever-child-adip-spark:tag-child-adip-spark:tag]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_resolve_docker_image[True-some-image:tag-None-None-some-image:tag]", "tests/aetion/adip/config/test_isolated_python_deps_feature.py::test_resolve_docker_image[True-some-image:tag-whatever-spark:tag-whatever-prophecy:tag-some-image:tag]", "tests/aetion/adip/config/test_kryo_feature.py::test_adapt_spark_conf__adds_classes_to_register", "tests/aetion/adip/config/test_kryo_feature.py::test_adapt_spark_conf__does_not_modify_when_disabled", "tests/aetion/adip/config/test_kryo_feature.py::test_adapt_spark_conf__does_not_modify_when_main_class_is_none", "tests/aetion/adip/config/test_kryo_feature.py::test_adapt_spark_conf__does_not_modify_when_serializer_is_not_kryo", "tests/aetion/adip/config/test_kryo_feature.py::test_classes_to_register", "tests/aetion/adip/config/test_kryo_feature.py::test_enable", "tests/aetion/adip/config/test_spark_monitoring.py::test_creation_from_dict", "tests/aetion/adip/config/test_spark_monitoring.py::test_default_enabled_field", "tests/aetion/adip/config/test_spark_monitoring.py::test_default_spark_conf_field", "tests/aetion/adip/config/test_spark_monitoring.py::test_effective_spark_conf_disabled", "tests/aetion/adip/config/test_spark_monitoring.py::test_effective_spark_conf_enabled", "tests/aetion/adip/config/test_spark_monitoring.py::test_global_spark_conf_props_take_precedence_over_defaults", "tests/aetion/adip/config/test_spark_monitoring.py::test_pipeline_feat_is_disabled_when_global_feat_is_disabled", "tests/aetion/adip/config/test_spark_monitoring.py::test_pipeline_spark_conf_takes_precedent", "tests/operators/test_automatically_update_enums.py::test_check_file_exists_with_s3_uri_not_found", "tests/operators/test_automatically_update_enums.py::test_check_file_exists_with_s3_uri_success", "tests/operators/test_automatically_update_enums.py::test_execute_invokes_clone_and_validate", "tests/operators/test_automatically_update_enums.py::test_resolve_transformation_files_default", "tests/operators/test_automatically_update_enums.py::test_resolve_transformation_files_not_found", "tests/operators/test_automatically_update_enums.py::test_resolve_transformation_files_override", "tests/operators/test_automatically_update_enums.py::test_validate_enums_equal_hashes", "tests/operators/test_automatically_update_enums.py::test_validate_enums_hash_mismatch_branch_update", "tests/operators/test_automatically_update_enums.py::test_validate_enums_hash_mismatch_pr_created", "tests/operators/test_default_fail_callbacks.py::test_failure_callback_with_built_params[create_comparison_report_callback-*Comparison Report URL*-flatiron_testing_pre_full_only_partitioned/201906/comparison_report/]", "tests/operators/test_default_fail_callbacks.py::test_failure_callback_with_built_params[full_shard_validation_callback-*Full Shard Report URL*-flatiron_testing_pre_full_only_partitioned/201906/full-shard/shard/REPORT/]", "tests/operators/test_default_fail_callbacks.py::test_failure_callback_with_built_params[generate_full_enums_and_dictionaries_callback-*Missing Enums URL*-flatiron_testing_pre_full_only_partitioned/201906/full-shard/enums/]", "tests/operators/test_default_fail_callbacks.py::test_failure_callback_with_built_params[single_shard_validation_callback-*Single Shard Report URL*-flatiron_testing_pre_full_only_partitioned/201906/single-shard/shard/REPORT/]", "tests/operators/test_default_fail_callbacks.py::test_failure_callback_with_built_params[stopgap_check_raw_data_inventory_callback-*Raw Inventory Summary URL*-raw-inventory/inventory_summary.csv]", "tests/operators/test_default_fail_callbacks.py::test_failure_callback_with_built_params[table_level_report_callback-*Raw Data Reports URL*-flatiron_testing_pre_full_only_partitioned/201906/raw-data-reports/]", "tests/operators/test_publish_coding_systems.py::TestCodingSystemDataclasses::test_coding_system_attribute", "tests/operators/test_publish_coding_systems.py::TestCodingSystemDataclasses::test_coding_system_base_resource", "tests/operators/test_publish_coding_systems.py::TestCodingSystemDataclasses::test_coding_system_instance_params", "tests/operators/test_publish_coding_systems.py::TestCodingSystemDataclasses::test_coding_system_instance_resource", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_compress_file", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_copy_coding_systems_to_s3", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_copy_coding_systems_to_s3_file_not_found", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_create_base_resource_file", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_create_base_resource_file_no_metadata_folder", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_create_github_pr_no_token", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_create_github_pr_success", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_create_instance_resource_file", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_create_instance_resource_file_no_metadata_folder", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_create_instance_resource_file_with_custom_code_column", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_csv_file_not_found_in_s3", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_load_raw_data_catalog_failure", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_load_raw_data_catalog_success", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_operator_initialization", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_operator_initialization_without_github_token", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_publish_coding_systems_all_failures", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_publish_coding_systems_no_coding_systems", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_publish_coding_systems_no_successful_skip_registry", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_publish_coding_systems_partial_failure", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_publish_coding_systems_registry_update_only_successful", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_publish_coding_systems_with_coding_systems", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_read_csv_headers", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_update_existing_coding_system_new_revision", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_update_existing_coding_system_overwrite_same_revision", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_update_instance_resource_file", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_validate_csv_columns", "tests/operators/test_publish_coding_systems.py::TestPublishCodingSystems::test_validate_csv_columns_invalid", "tests/operators/test_slack.py::test_send_with_exception[None--False]", "tests/operators/test_slack.py::test_send_with_exception[None--True]", "tests/operators/test_slack.py::test_send_with_exception[None-None-False]", "tests/operators/test_slack.py::test_send_with_exception[None-None-True]", "tests/operators/test_slack.py::test_send_with_exception[None-clientA.aetion.com/upload/test/rev1-False]", "tests/operators/test_slack.py::test_send_with_exception[None-clientA.aetion.com/upload/test/rev1-True]", "tests/operators/test_slack.py::test_send_with_exception[None-clientA.aetion.com/upload/test/rev1/-False]", "tests/operators/test_slack.py::test_send_with_exception[None-clientA.aetion.com/upload/test/rev1/-True]", "tests/operators/test_slack.py::test_send_with_exception[catalog_overrides1--False]", "tests/operators/test_slack.py::test_send_with_exception[catalog_overrides1--True]", "tests/operators/test_slack.py::test_send_with_exception[catalog_overrides1-None-False]", "tests/operators/test_slack.py::test_send_with_exception[catalog_overrides1-None-True]", "tests/operators/test_slack.py::test_send_with_exception[catalog_overrides1-clientA.aetion.com/upload/test/rev1-False]", "tests/operators/test_slack.py::test_send_with_exception[catalog_overrides1-clientA.aetion.com/upload/test/rev1-True]", "tests/operators/test_slack.py::test_send_with_exception[catalog_overrides1-clientA.aetion.com/upload/test/rev1/-False]", "tests/operators/test_slack.py::test_send_with_exception[catalog_overrides1-clientA.aetion.com/upload/test/rev1/-True]", "tests/test_adip_config_loader_params.py::test_airflow_config_loader_params_all_values", "tests/test_adip_config_loader_params.py::test_airflow_config_loader_params_defaults", "tests/test_adip_config_loader_params.py::test_get_pipelines_config_path[adip0]", "tests/test_adip_config_loader_params.py::test_get_pipelines_config_path[adip1]", "tests/test_adip_config_loader_params.py::test_get_pipelines_config_path[adip2]", "tests/test_adip_config_loader_params.py::test_get_pipelines_config_path[cqa]", "tests/test_adip_config_loader_params.py::test_get_pipelines_config_path[prod]", "tests/test_adm_prep.py::test_execute", "tests/test_airflow_dag.py::test_contain_tasks[flatiron_testing-task_ordering0]", "tests/test_airflow_dag.py::test_contain_tasks[flatiron_testing_adm_pipeline-task_ordering11]", "tests/test_airflow_dag.py::test_contain_tasks[flatiron_testing_dynamic_flat-task_ordering5]", "tests/test_airflow_dag.py::test_contain_tasks[flatiron_testing_pre_full_only_partitioned-task_ordering2]", "tests/test_airflow_dag.py::test_contain_tasks[flatiron_testing_pre_partitioned-task_ordering1]", "tests/test_airflow_dag.py::test_contain_tasks[flatiron_testing_preprocessed-task_ordering8]", "tests/test_airflow_dag.py::test_contain_tasks[flatiron_testing_preprocessed_skip_single-task_ordering9]", "tests/test_airflow_dag.py::test_contain_tasks[flatiron_testing_skip_single-task_ordering3]", "tests/test_airflow_dag.py::test_contain_tasks[flatiron_testing_skip_single_and_generate_sample-task_ordering7]", "tests/test_airflow_dag.py::test_contain_tasks[flatiron_testing_smart_sample-task_ordering4]", "tests/test_airflow_dag.py::test_contain_tasks[flatiron_testing_super_dbc-task_ordering6]", "tests/test_airflow_dag.py::test_contain_tasks[flatiron_testing_with_generate_sample-task_ordering10]", "tests/test_airflow_dag.py::test_dag_tags_for_k8s[flatiron_testing_spark_conf_k8s]", "tests/test_airflow_dag.py::test_publish_artifacts_task_is_first[flatiron_testing]", "tests/test_airflow_dag.py::test_publish_artifacts_task_is_first[flatiron_testing_adm_pipeline]", "tests/test_airflow_dag.py::test_publish_artifacts_task_is_first[flatiron_testing_dynamic_flat]", "tests/test_airflow_dag.py::test_publish_artifacts_task_is_first[flatiron_testing_pre_partitioned]", "tests/test_airflow_dag.py::test_publish_artifacts_task_is_first[flatiron_testing_skip_single]", "tests/test_airflow_dag.py::test_publish_artifacts_task_is_first[flatiron_testing_smart_sample]", "tests/test_airflow_dag.py::test_spark_conf_keys_prefixed_with_spark[flatiron_testing_spark_conf_k8s]", "tests/test_airflow_dag.py::test_task_count[flatiron_testing-45]", "tests/test_airflow_dag.py::test_task_count[flatiron_testing_adm_pipeline-51]", "tests/test_airflow_dag.py::test_task_count[flatiron_testing_dynamic_flat-45]", "tests/test_airflow_dag.py::test_task_count[flatiron_testing_pre_partitioned-30]", "tests/test_airflow_dag.py::test_task_count[flatiron_testing_skip_single-36]", "tests/test_airflow_dag.py::test_task_count[flatiron_testing_skip_single_and_generate_sample-37]", "tests/test_airflow_dag.py::test_task_count[flatiron_testing_smart_sample-45]", "tests/test_airflow_dag.py::test_task_count[flatiron_testing_super_dbc-28]", "tests/test_airflow_dag.py::test_task_count[flatiron_testing_with_generate_sample-46]", "tests/test_airflow_dag.py::test_task_ordering[flatiron_testing]", "tests/test_airflow_dag.py::test_task_ordering_when_global_skip_validate_single_shard_formats", "tests/test_airflow_dag.py::test_task_types_in_dag_for_k8s[flatiron_testing_spark_conf_k8s]", "tests/test_automated_ingestion_pipeline_builder.py::TestCreateDAGs::test_creates_a_dag_per_config_file", "tests/test_automated_ingestion_pipeline_builder.py::TestCreateDAGs::test_does_not_create_dag_for_invalid_paths", "tests/test_automated_ingestion_pipeline_builder.py::TestCreateDAGs::test_does_nothing_if_no_configs_exist", "tests/test_automated_ingestion_pipeline_builder.py::TestParseConfigFilePrefix::test_handles_trailing_slash", "tests/test_automated_ingestion_pipeline_builder.py::TestParseConfigFilePrefix::test_parse_client_and_dataset", "tests/test_automated_ingestion_pipeline_builder.py::TestParseConfigFilePrefix::test_return_none_for_invalid_prefix[]", "tests/test_automated_ingestion_pipeline_builder.py::TestParseConfigFilePrefix::test_return_none_for_invalid_prefix[pipeline_configs/client/dataset/20230101/too-far-nested/config.yaml]", "tests/test_automated_ingestion_pipeline_builder.py::TestParseConfigFilePrefix::test_return_none_for_invalid_prefix[pipeline_configs/not-a-valid-config.yaml]", "tests/test_automated_ingestion_pipeline_builder.py::TestParseConfigFilePrefix::test_return_none_for_invalid_prefix[s3://test_bucket/config.yaml]", "tests/test_automated_ingestion_pipeline_builder.py::test_get_pipeline_config_from_git", "tests/test_aws.py::test_copy_files_parallel", "tests/test_aws.py::test_copy_with_pattern_parallel", "tests/test_aws_util.py::test_remove_s3_scheme[no_s3_scheme-no_s3_scheme]", "tests/test_aws_util.py::test_remove_s3_scheme[s3://adip-dev/pipeline_configs/config.yaml-adip-dev/pipeline_configs/config.yaml]", "tests/test_aws_util.py::test_remove_s3_scheme[s3a://adip-dev/pipeline_configs/config.yaml-adip-dev/pipeline_configs/config.yaml]", "tests/test_aws_util.py::test_remove_s3_scheme[s3a://s3://test-s3://test]", "tests/test_aws_util.py::test_remove_s3_scheme[s3n://adip-dev/pipeline_configs/config.yaml-adip-dev/pipeline_configs/config.yaml]", "tests/test_bootstrap.py::test_create_mismatching_pipelines", "tests/test_bootstrap.py::test_create_misnamed_pipeline", "tests/test_bootstrap.py::test_create_pipeline", "tests/test_bootstrap.py::test_get_dummy_pipeline_configs", "tests/test_bootstrap.py::test_get_empty_pipeline_configs", "tests/test_bootstrap.py::test_get_mismatching_pipelines", "tests/test_bootstrap.py::test_get_misnamed_pipeline_configs", "tests/test_celeborn_feature.py::test_celeborn_feature__combine__excluded_tasks", "tests/test_celeborn_feature.py::test_celeborn_feature__combine__pipeline_spark_conf_has_precedence", "tests/test_celeborn_feature.py::test_celeborn_feature__effective_spark_conf", "tests/test_celeborn_feature.py::test_celeborn_feature__excluded_tasks", "tests/test_celeborn_feature.py::test_celeborn_feature__factory_when_empty_settings", "tests/test_celeborn_feature.py::test_celeborn_feature__factory_when_endpoints_are_comma_separated_list", "tests/test_celeborn_feature.py::test_celeborn_feature__factory_when_endpoints_are_empty_list", "tests/test_celeborn_feature.py::test_celeborn_feature__factory_when_endpoints_are_in_a_list", "tests/test_celeborn_feature.py::test_celeborn_feature__merge__enable[both disabled]", "tests/test_celeborn_feature.py::test_celeborn_feature__merge__enable[both enabled]", "tests/test_celeborn_feature.py::test_celeborn_feature__merge__enable[global disabled]", "tests/test_celeborn_feature.py::test_celeborn_feature__merge__enable[pipeline disabled]", "tests/test_celeborn_feature.py::test_celeborn_feature__merge_global_and_pipeline_features", "tests/test_celeborn_feature.py::test_celeborn_feature__merge_use_global_endpoints_when_pipeline_endpoints_are_missing", "tests/test_celeborn_feature.py::test_celeborn_feature__no_spark_conf_is_added[disabled]", "tests/test_celeborn_feature.py::test_celeborn_feature__no_spark_conf_is_added[enabled]", "tests/test_celeborn_feature.py::test_celeborn_feature_is_disable_by_default", "tests/test_celeborn_feature.py::test_celeborn_feature_is_effectively_enable", "tests/test_cluster_config.py::test_get_prophecy_libraries", "tests/test_cluster_config.py::test_get_task_submit_params_adm", "tests/test_cluster_config.py::test_get_task_submit_params_adm_k8s", "tests/test_cluster_config.py::test_pyspark_memory_overhead", "tests/test_cluster_config.py::test_pyspark_memory_overhead_factor", "tests/test_cluster_config.py::test_spark_event_logging_support__adm_full_job_cluster[create_full_adm_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__adm_full_job_cluster[full_adm_schema_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__adm_full_job_cluster[full_allowed_values_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__adm_full_job_cluster[full_foreign_key_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__adm_full_job_cluster[full_relational_model_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__adm_full_job_cluster[full_source_to_target_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__adm_single_job_cluster[create_single_adm_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__adm_single_job_cluster[single_adm_schema_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__adm_single_job_cluster[single_allowed_values_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__adm_single_job_cluster[single_foreign_key_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__adm_single_job_cluster[single_relational_model_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__adm_single_job_cluster[single_source_to_target_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[create_comparison_report]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[create_full_adm_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[create_full_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[create_full_shard]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[create_metadata]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[create_raw_data_exploration]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[create_single_adm_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[create_single_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[create_single_shard]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[create_smart_sample]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[dynamic_gdr_configuration]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[filter_patient]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[full_adm_schema_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[full_allowed_values_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[full_foreign_key_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[full_relational_model_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[full_source_to_target_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[generate_crosswalk]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[generate_full_enums_and_dictionaries]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[generate_full_flat_tables]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[generate_full_patient]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[generate_full_shard_expected_counts]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[generate_sampled_data]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[single_adm_schema_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[single_allowed_values_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[single_foreign_key_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[single_relational_model_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[single_source_to_target_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[table_level_report_comparison]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[unarchiver]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[validate_ndc_linkage]", "tests/test_cluster_config.py::test_spark_event_logging_support__full_job_cluster[validate_single_shard_data_formats]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[create_comparison_report]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[create_full_adm_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[create_full_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[create_full_shard]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[create_metadata]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[create_raw_data_exploration]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[create_single_adm_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[create_single_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[create_single_shard]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[create_smart_sample]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[dynamic_gdr_configuration]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[filter_patient]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[full_adm_schema_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[full_allowed_values_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[full_foreign_key_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[full_relational_model_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[full_source_to_target_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[generate_crosswalk]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[generate_full_enums_and_dictionaries]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[generate_full_flat_tables]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[generate_full_patient]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[generate_full_shard_expected_counts]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[generate_sampled_data]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[single_adm_schema_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[single_allowed_values_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[single_foreign_key_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[single_relational_model_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[single_source_to_target_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[table_level_report_comparison]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[unarchiver]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[validate_ndc_linkage]", "tests/test_cluster_config.py::test_spark_event_logging_support__globally_enabled[validate_single_shard_data_formats]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[create_comparison_report]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[create_full_adm_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[create_full_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[create_full_shard]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[create_metadata]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[create_raw_data_exploration]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[create_single_adm_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[create_single_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[create_single_shard]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[create_smart_sample]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[dynamic_gdr_configuration]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[filter_patient]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[full_adm_schema_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[full_allowed_values_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[full_foreign_key_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[full_relational_model_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[full_source_to_target_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[generate_crosswalk]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[generate_full_enums_and_dictionaries]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[generate_full_flat_tables]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[generate_full_patient]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[generate_full_shard_expected_counts]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[generate_sampled_data]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[single_adm_schema_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[single_allowed_values_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[single_foreign_key_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[single_relational_model_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[single_source_to_target_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[table_level_report_comparison]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[unarchiver]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[validate_ndc_linkage]", "tests/test_cluster_config.py::test_spark_event_logging_support__not_enabled[validate_single_shard_data_formats]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[create_comparison_report]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[create_full_adm_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[create_full_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[create_full_shard]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[create_metadata]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[create_raw_data_exploration]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[create_single_adm_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[create_single_partition]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[create_single_shard]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[create_smart_sample]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[dynamic_gdr_configuration]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[filter_patient]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[full_adm_schema_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[full_allowed_values_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[full_foreign_key_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[full_relational_model_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[full_source_to_target_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[generate_crosswalk]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[generate_full_enums_and_dictionaries]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[generate_full_flat_tables]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[generate_full_patient]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[generate_full_shard_expected_counts]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[generate_sampled_data]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[single_adm_schema_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[single_allowed_values_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[single_foreign_key_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[single_relational_model_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[single_source_to_target_validation]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[table_level_report_comparison]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[unarchiver]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[validate_ndc_linkage]", "tests/test_cluster_config.py::test_spark_event_logging_support__only_unarchiver_enabled[validate_single_shard_data_formats]", "tests/test_cluster_config.py::test_spark_memory_overhead", "tests/test_cluster_config.py::test_spark_memory_overhead_factor", "tests/test_commit_metadata.py::test_execute[adip1.app.dev.aetion.com/upload/synpuf_subset/20240307]", "tests/test_commit_metadata.py::test_execute[adip1.app.dev.aetion.com/upload/synpuf_subset/30000000]", "tests/test_compare_previous_full_shard_counts.py::test_error[fail-20240725-AirflowException-cannot find the message-raw.log]", "tests/test_compare_previous_full_shard_counts.py::test_error[fail-20240925-AirflowException-]", "tests/test_compare_previous_full_shard_counts.py::test_error[fail-20241025-AirflowException-]", "tests/test_compare_previous_full_shard_counts.py::test_validate[success-20240625-False]", "tests/test_compare_previous_full_shard_counts.py::test_validate[success-20240725-True]", "tests/test_compare_previous_full_shard_counts.py::test_validate[success-20240825-True]", "tests/test_compare_previous_full_shard_counts.py::test_validate[success-20240925-True]", "tests/test_create_deployment_package.py::test_execute", "tests/test_create_deployment_package.py::test_get_branches[deploy_config0-expected_branch_metas0]", "tests/test_create_deployment_package.py::test_get_branches[deploy_config1-expected_branch_metas1]", "tests/test_create_deployment_package.py::test_get_branches[deploy_config2-expected_branch_metas2]", "tests/test_create_deployment_package.py::test_get_package_files[branch0-expected_shard0]", "tests/test_create_deployment_package.py::test_get_package_files[branch1-expected_shard1]", "tests/test_create_deployment_package.py::test_validate_attribute_prefix_vs_dataset_name[test1-False]", "tests/test_create_deployment_package.py::test_validate_attribute_prefix_vs_dataset_name[test2-True]", "tests/test_dagbag.py::test_dag_imports", "tests/test_dds.py::test_notify", "tests/test_delete_full_flat_tables.py::test_case_insensitive_table_names", "tests/test_delete_full_flat_tables.py::test_get_files_to_delete[false-flat_tables_to_regenerate2-expected2]", "tests/test_delete_full_flat_tables.py::test_get_files_to_delete[false-flat_tables_to_regenerate3-expected3]", "tests/test_delete_full_flat_tables.py::test_get_files_to_delete[true-flat_tables_to_regenerate0-expected0]", "tests/test_delete_full_flat_tables.py::test_get_files_to_delete[true-flat_tables_to_regenerate1-expected1]", "tests/test_delete_full_flat_tables.py::test_no_errors_when_files_dont_exist[false-flat_tables_to_regenerate1]", "tests/test_delete_full_flat_tables.py::test_no_errors_when_files_dont_exist[false-flat_tables_to_regenerate2]", "tests/test_delete_full_flat_tables.py::test_no_errors_when_files_dont_exist[true-flat_tables_to_regenerate0]", "tests/test_deploy_common.py::test_shard_metadata_json_for_eventsDiscardedOutsideGDR[False]", "tests/test_deploy_common.py::test_shard_metadata_json_for_eventsDiscardedOutsideGDR[None]", "tests/test_deploy_common.py::test_shard_metadata_json_for_eventsDiscardedOutsideGDR[True]", "tests/test_deploy_package_build.py::test_constant_name[dataset_7-instance_a-20220101-None]", "tests/test_deploy_package_build.py::test_constant_name[dataset_7-instance_a-20220102-DATASET_7 at 20220102 does not match DaTaSeT_7 at 20220101 in ds.json]", "tests/test_deploy_package_build.py::test_constant_name[dataset_7-instance_a-20220103-None]", "tests/test_deploy_package_build.py::test_constant_name[dataset_8-instance_b-20220224-dataset_8 at 20220224 does not match dAtAsEt_8 at 20220212 in aetionpkg.cs.json]", "tests/test_deploy_package_build.py::test_constant_name[dataset_8-instance_b-20220229-dAtAsEt_8 at 20220229 does not match dataset_8 at 20220224 in aetionpkg.schema.json]", "tests/test_deploy_package_build.py::test_execute[20220101-20220101-s3_transform_dir0-test_dbc-None-7dc9eea9-5d0f-3495-90ee-b5f7a7d524a9-2-9-0.05]", "tests/test_deploy_package_build.py::test_execute[20220103-20220103-s3_transform_dir1-test_dbc-None-7dc9eea9-5d0f-3495-2bc3-0797a7d524a9-3-7-0.1]", "tests/test_deploy_package_build.py::test_find_prev_revision[dataset1-20210601-20210501]", "tests/test_deploy_package_build.py::test_find_prev_revision[dataset2-2021050101-20210501]", "tests/test_deploy_package_build.py::test_find_prev_revision[dataset2-20210601-20210501]", "tests/test_deploy_package_build.py::test_find_prev_revision[dataset3-client20210601-client20210501]", "tests/test_deploy_package_build.py::test_find_prev_revision[dataset4-20210601-20210401_v2]", "tests/test_deploy_package_build.py::test_find_prev_revision[dataset5-20210601-2021050101]", "tests/test_deploy_package_build.py::test_generate_report_template", "tests/test_deploy_package_build.py::test_get_branches", "tests/test_deploy_package_build.py::test_get_package_files[branch0-expected_shard0]", "tests/test_deploy_package_build.py::test_get_package_files[branch1-expected_shard1]", "tests/test_deploy_package_build.py::test_validate_attribute_prefix_vs_dataset_name[test1-False]", "tests/test_deploy_package_build.py::test_validate_attribute_prefix_vs_dataset_name[test2-True]", "tests/test_deploy_package_build.py::test_validate_if_files_are_in_master[20220101-20220101-s3_transform_dir0-test_dbc-transform_path_override-False]", "tests/test_deploy_package_build.py::test_validate_if_files_are_in_master[20220101-20220101-s3_transform_dir1-test_dbc--True]", "tests/test_deploy_package_build.py::test_validate_if_files_are_in_master[20220101-20220101-s3_transform_dir2-wrong_path--True]", "tests/test_deploy_package_build.py::test_validate_if_files_are_in_master[20220101-20220101-s3_transform_dir3-transform_path_override--True]", "tests/test_deploy_package_build.py::test_validate_shard_package_values[s3://mock.local/runtime/dataset_7/instance_a/20220103/-251-0.05-None-test-qa]", "tests/test_deploy_package_build.py::test_validate_shard_package_values[s3://mock.local/runtime/dataset_7/instance_a/20220103/-251-0.5-None-wrong_dir-qa]", "tests/test_deploy_package_build.py::test_validate_shard_package_values[s3://mock.local/runtime/dataset_7/instance_a/20220103/-251-0.5-sampleShardPct-test-qa]", "tests/test_deploy_package_build.py::test_validate_shard_package_values[s3://mock.local/runtime/dataset_7/instance_a/20220103/-300-0.05-sampleShardNum-test-qa]", "tests/test_deploy_package_build.py::test_validate_shard_package_values[s3://mock.local/runtime/wrong_dataset_7/instance_a/20220103/-251-0.05-runtimeDir-test-qa]", "tests/test_ds_validator.py::test_error[adip1.app.dev.aetion.com/upload/changed_datatype/20240307-AssertionError-.*The dataType of MARKETSCAN_TINY/PATIENT/AGE attribute has changed.*]", "tests/test_ds_validator.py::test_error[adip1.app.dev.aetion.com/upload/changed_values/20240307-AssertionError-.*The order of values of MARKETSCAN_TINY/PATIENT/SEX attribute has changed.*]", "tests/test_ds_validator.py::test_error[adip1.app.dev.aetion.com/upload/dropped_attributes/20240307-AssertionError-.*The following attributes have been dropped in the current revision.*]", "tests/test_ds_validator.py::test_error[adip1.app.dev.aetion.com/upload/dropped_values/20240307-AssertionError-.*The following values of MARKETSCAN_TINY/PATIENT/SEX attribute have been dropped.*]", "tests/test_ds_validator.py::test_error[adip1.app.dev.aetion.com/upload/error_500/20240307-Exception-.*An error occurred \\\\(500\\\\) when calling the download_file operation: Unknown.*]", "tests/test_ds_validator.py::test_error[adip1.app.dev.etion.com/upload/synpuf_subset/20240307-ValueError-.*Cannot parse s3 url.*]", "tests/test_ds_validator.py::test_execute[adip1.app.dev.aetion.com/upload/skip_single_shard/20240307]", "tests/test_ds_validator.py::test_execute[adip1.app.dev.aetion.com/upload/synpuf_subset/20211018]", "tests/test_ds_validator.py::test_execute[adip1.app.dev.aetion.com/upload/synpuf_subset/20240307]", "tests/test_ds_validator.py::test_execute[adip1.app.dev.aetion.com/upload/synpuf_subset/20250408]", "tests/test_etl_cuts_params.py::test_copy_spec", "tests/test_etl_cuts_params.py::test_create_comparison_report", "tests/test_etl_cuts_params.py::test_create_full_partition", "tests/test_etl_cuts_params.py::test_create_full_shard", "tests/test_etl_cuts_params.py::test_create_metadata", "tests/test_etl_cuts_params.py::test_create_raw_data_exploration", "tests/test_etl_cuts_params.py::test_create_single_partition", "tests/test_etl_cuts_params.py::test_create_single_patient", "tests/test_etl_cuts_params.py::test_create_single_shard", "tests/test_etl_cuts_params.py::test_create_smart_sample", "tests/test_etl_cuts_params.py::test_file_validator", "tests/test_etl_cuts_params.py::test_full_validation_prep", "tests/test_etl_cuts_params.py::test_gdr_configuration", "tests/test_etl_cuts_params.py::test_generate_crosswalk", "tests/test_etl_cuts_params.py::test_generate_full_enums_and_dictionaries", "tests/test_etl_cuts_params.py::test_generate_full_flat_tables", "tests/test_etl_cuts_params.py::test_generate_full_patient", "tests/test_etl_cuts_params.py::test_patient_prep", "tests/test_etl_cuts_params.py::test_profile_single_shard", "tests/test_etl_cuts_params.py::test_publish_artifacts", "tests/test_etl_cuts_params.py::test_rde_configuration_prep", "tests/test_etl_cuts_params.py::test_table_level_report_comparison", "tests/test_etl_cuts_params.py::test_unarchiver", "tests/test_etl_cuts_params.py::test_validate_full_shard", "tests/test_etl_cuts_params.py::test_validate_full_shard_with_dedup_enabled", "tests/test_etl_cuts_params.py::test_validate_single_shard", "tests/test_etl_cuts_params.py::test_validation_prep", "tests/test_etl_params.py::test_copy_spec", "tests/test_etl_params.py::test_create_comparison_report", "tests/test_etl_params.py::test_create_full_partition", "tests/test_etl_params.py::test_create_full_partition_with_catalog_overrides", "tests/test_etl_params.py::test_create_full_preprocessed_tables", "tests/test_etl_params.py::test_create_full_shard", "tests/test_etl_params.py::test_create_lookup_csv_files", "tests/test_etl_params.py::test_create_metadata", "tests/test_etl_params.py::test_create_metadata_with_catalog_overrides", "tests/test_etl_params.py::test_create_raw_data_exploration", "tests/test_etl_params.py::test_create_single_partition", "tests/test_etl_params.py::test_create_single_partition_with_catalog_overrides", "tests/test_etl_params.py::test_create_single_patient", "tests/test_etl_params.py::test_create_single_preprocessed_tables", "tests/test_etl_params.py::test_create_single_shard", "tests/test_etl_params.py::test_create_smart_sample", "tests/test_etl_params.py::test_create_smart_sample_with_catalog_overrides", "tests/test_etl_params.py::test_file_validator", "tests/test_etl_params.py::test_full_validation_prep", "tests/test_etl_params.py::test_gdr_configuration", "tests/test_etl_params.py::test_generate_crosswalk", "tests/test_etl_params.py::test_generate_full_enums_and_dictionaries", "tests/test_etl_params.py::test_generate_full_flat_tables", "tests/test_etl_params.py::test_generate_full_flat_tables_with_catalog_overrides", "tests/test_etl_params.py::test_generate_full_patient", "tests/test_etl_params.py::test_generate_full_shard_validation_tables", "tests/test_etl_params.py::test_generate_sampled_data", "tests/test_etl_params.py::test_generate_single_shard_validation_tables", "tests/test_etl_params.py::test_get_callback_default_params", "tests/test_etl_params.py::test_patient_prep", "tests/test_etl_params.py::test_preprocess_prep", "tests/test_etl_params.py::test_profile_single_shard", "tests/test_etl_params.py::test_publish_artifacts", "tests/test_etl_params.py::test_publish_coding_systems", "tests/test_etl_params.py::test_rde_configuration_prep", "tests/test_etl_params.py::test_table_level_report_comparison", "tests/test_etl_params.py::test_table_level_report_comparison_with_catalog_overrides", "tests/test_etl_params.py::test_unarchiver", "tests/test_etl_params.py::test_validate_full_shard", "tests/test_etl_params.py::test_validate_full_shard_with_dedup_enabled", "tests/test_etl_params.py::test_validate_single_shard", "tests/test_etl_params.py::test_validation_prep", "tests/test_etl_pre_partitioned_full_only_params.py::test_create_comparison_report", "tests/test_etl_pre_partitioned_full_only_params.py::test_create_full_shard", "tests/test_etl_pre_partitioned_full_only_params.py::test_create_metadata", "tests/test_etl_pre_partitioned_full_only_params.py::test_create_metadata_with_catalog_overrides", "tests/test_etl_pre_partitioned_full_only_params.py::test_create_single_partition", "tests/test_etl_pre_partitioned_full_only_params.py::test_create_single_patient", "tests/test_etl_pre_partitioned_full_only_params.py::test_create_single_shard", "tests/test_etl_pre_partitioned_full_only_params.py::test_create_smart_sample", "tests/test_etl_pre_partitioned_full_only_params.py::test_full_validation_prep", "tests/test_etl_pre_partitioned_full_only_params.py::test_gdr_configuration", "tests/test_etl_pre_partitioned_full_only_params.py::test_generate_crosswalk", "tests/test_etl_pre_partitioned_full_only_params.py::test_generate_full_enums_and_dictionaries", "tests/test_etl_pre_partitioned_full_only_params.py::test_generate_full_flat_tables", "tests/test_etl_pre_partitioned_full_only_params.py::test_generate_full_patient", "tests/test_etl_pre_partitioned_full_only_params.py::test_patient_prep", "tests/test_etl_pre_partitioned_full_only_params.py::test_profile_single_shard", "tests/test_etl_pre_partitioned_full_only_params.py::test_publish_artifacts", "tests/test_etl_pre_partitioned_full_only_params.py::test_table_level_report_comparison", "tests/test_etl_pre_partitioned_full_only_params.py::test_validate_full_shard", "tests/test_etl_pre_partitioned_full_only_params.py::test_validate_full_shard_with_dedup_enabled", "tests/test_etl_pre_partitioned_full_only_params.py::test_validate_single_shard", "tests/test_etl_pre_partitioned_full_only_params.py::test_validation_prep", "tests/test_etl_pre_partitioned_params.py::test_copy_spec", "tests/test_etl_pre_partitioned_params.py::test_create_full_shard", "tests/test_etl_pre_partitioned_params.py::test_create_single_patient", "tests/test_etl_pre_partitioned_params.py::test_create_single_shard", "tests/test_etl_pre_partitioned_params.py::test_full_validation_prep", "tests/test_etl_pre_partitioned_params.py::test_gdr_configuration", "tests/test_etl_pre_partitioned_params.py::test_generate_crosswalk", "tests/test_etl_pre_partitioned_params.py::test_generate_full_enums_and_dictionaries", "tests/test_etl_pre_partitioned_params.py::test_generate_full_flat_tables", "tests/test_etl_pre_partitioned_params.py::test_generate_full_patient", "tests/test_etl_pre_partitioned_params.py::test_patient_prep", "tests/test_etl_pre_partitioned_params.py::test_profile_single_shard", "tests/test_etl_pre_partitioned_params.py::test_publish_artifacts", "tests/test_etl_pre_partitioned_params.py::test_unarchiver", "tests/test_etl_pre_partitioned_params.py::test_validate_full_shard", "tests/test_etl_pre_partitioned_params.py::test_validate_full_shard_with_dedup_enabled", "tests/test_etl_pre_partitioned_params.py::test_validate_single_shard", "tests/test_etl_pre_partitioned_params.py::test_validation_prep", "tests/test_gdr_configuration.py::test_compare_against_bad_specs[spec0]", "tests/test_gdr_configuration.py::test_compare_against_bad_specs[spec1]", "tests/test_gdr_configuration.py::test_compare_against_prev_revision[1990-01-01-2022-06-01-1990-01-01-2022-05-01-Shrinking GDR range]", "tests/test_gdr_configuration.py::test_compare_against_prev_revision[1990-01-01-2022-06-01-1990-01-01-2022-06-01-Unchanged GDR range]", "tests/test_gdr_configuration.py::test_compare_against_prev_revision[1990-01-01-2022-06-01-1990-01-01-2023-06-01-None]", "tests/test_gdr_configuration.py::test_compare_against_spec[1900-01-01-2022-03-30-spec6-DoubtfulGDRUpdateException]", "tests/test_gdr_configuration.py::test_compare_against_spec[1900-01-01-2022-03-31-spec4-None]", "tests/test_gdr_configuration.py::test_compare_against_spec[1900-01-02-2022-03-31-spec5-DoubtfulGDRUpdateException]", "tests/test_gdr_configuration.py::test_compare_against_spec[1990-01-01-2023-06-01-None-None]", "tests/test_gdr_configuration.py::test_compare_against_spec[2007-01-01-2022-03-30-spec2-DoubtfulGDRUpdateException]", "tests/test_gdr_configuration.py::test_compare_against_spec[2007-01-01-2022-03-31-spec1-None]", "tests/test_gdr_configuration.py::test_compare_against_spec[2007-01-02-2022-03-31-spec3-DoubtfulGDRUpdateException]", "tests/test_gdr_configuration.py::test_extract_gdr_dates[hive0-expected0]", "tests/test_gdr_configuration.py::test_gdr_validation_errors[20200101-lilly-flatiron-{}-{}-gdr-template_lilly-flatiron-20200101.sql-Please provide start date and end date for flatiron in the format 'yyyy-MM-dd']", "tests/test_gdr_configuration.py::test_hive_vars[20200101-amgen-marketscan-{ \"GDR_END_DATE\": \"2020-12-21\", \"GDR_START_DATE\": \"1990-01-01\", \"MAX_DATE\": \"9999-01-01\", \"LAB_START_DATE\": \"2000-12-13\", \"LAB_END_DATE\": \"2010-12-12\", \"EV_START_DATE\": \"2000-01-23\", \"EV_END_DATE\": \"2005-01-23\" }-{ \"GDR_END_DATE\": \"2020-12-21\", \"GDR_START_DATE\": \"1990-01-01\" }-gdr-template_amgen-marketscan-20200101.sql-gdr_amgen-marketscan-20200101.sql]", "tests/test_gdr_configuration.py::test_hive_vars[20200101-lilly-flatiron-{ \"GDR_END_DATE\": \"2020-12-18\", \"GDR_START_DATE\": \"1990-01-01\", \"MAX_DATE\": \"9999-01-01\" }-{ \"GDR_END_DATE\": \"2020-12-18\", \"GDR_START_DATE\": \"1990-01-01\" }-gdr-template_lilly-flatiron-20200101.sql-gdr_lilly-flatiron-20200101.sql]", "tests/test_gdr_configuration.py::test_hive_vars[20200101-lilly-flatiron-{ \"GDR_END_DATE\": \"2020-12-18\", \"GDR_START_DATE\": \"1990-01-01\", \"MAX_DATE\": \"9999-01-01\" }-{ \"GDR_END_DATE\": \"2020-12-18\", \"GDR_START_DATE\": \"1990-01-01\" }-gdr-template_lilly-flatiron-20200101_comments.sql-gdr_lilly-flatiron-20200101.sql]", "tests/test_gdr_configuration.py::test_hive_vars[20200102-lilly-flatiron-{ \"GDR_END_DATE\": \"2020-12-19\", \"GDR_START_DATE\": \"1990-01-02\", \"MAX_DATE\": \"9999-01-02\" }-{ \"GDR_END_DATE\": \"2020-12-19\", \"GDR_START_DATE\": \"1990-01-02\"}-gdr-template_lilly-flatiron-20200102.sql-gdr_lilly-flatiron-20200102.sql]", "tests/test_gdr_configuration.py::test_hive_vars[20200103-lilly-flatiron-{ \"GDR_END_DATE\": \"2020-12-18\", \"GDR_START_DATE\": \"1990-01-03\", \"MAX_DATE\": \"9999-01-01\",\"LAB_START_DATE\": \"2010-01-01\", \"LAB_END_DATE\": \"2020-01-01\" }-{ \"GDR_END_DATE\": \"2020-12-18\", \"GDR_START_DATE\": \"1990-01-03\" }-gdr-template_lilly-flatiron-20200103.sql-gdr_lilly-flatiron-20200103.sql]", "tests/test_gdr_configuration.py::test_hive_vars[20210105-lilly-flatiron-{ \"GDR_END_DATE\": \"2020-12-18\", \"GDR_START_DATE\": \"1990-01-01\" }-{ \"GDR_END_DATE\": \"2020-12-18\", \"GDR_START_DATE\": \"1990-01-01\" }-gdr-template_lilly-flatiron-20210105.sql-gdr_lilly-flatiron-20210105.sql]", "tests/test_gdr_configuration.py::test_hive_vars_errors[20200101-lilly-flatiron-{ \"GDR_START_DATE\": \"2000-12-31\" }-{ \"GDR_START_DATE\": \"2000-12-31\" }-gdr-template_lilly-flatiron-20200101.sql-cannot find associated value for GDR_END_DATE, MAX_DATE. Please add a value to the template or provide one.]", "tests/test_gdr_configuration.py::test_hive_vars_no_template[20210106-lilly-flatiron-{ \"GDR_START_DATE\": \"1990-01-03\", \"GDR_END_DATE\": \"2030-12-30\", \"MAX_DATE\": \"9999-01-01\",\"LAB_START_DATE\": \"2010-01-01\", \"LAB_END_DATE\": \"2030-01-01\" }-{ \"GDR_END_DATE\": \"2020-12-18\", \"GDR_START_DATE\": \"1990-01-03\" }-gdr_lilly-flatiron-20210106.sql]", "tests/test_gdr_configuration.py::test_no_prev_revision[1990-01-01-2022-06-01-True-False]", "tests/test_gdr_configuration.py::test_no_prev_revision[1990-01-01-2023-06-01-False-<PERSON>alse]", "tests/test_gdr_configuration.py::test_parse_gdr_dates[gdr-template_lilly-flatiron-20200101_comments.sql-expected3]", "tests/test_gdr_configuration.py::test_parse_gdr_dates[gdr_lilly-flatiron-20200101.sql-expected0]", "tests/test_gdr_configuration.py::test_parse_gdr_dates[gdr_lilly-flatiron-20200103.sql-expected1]", "tests/test_gdr_configuration.py::test_parse_gdr_dates[gdr_lilly-flatiron-20210106.sql-expected2]", "tests/test_git_hook.py::test_http_auth_mangling[-https://user:<EMAIL>-https://user:<EMAIL>]", "tests/test_git_hook.py::test_http_auth_mangling[-https://<EMAIL>-https://<EMAIL>]", "tests/test_git_hook.py::test_http_auth_mangling[None-https://user:@some.where-https://user:@some.where]", "tests/test_git_hook.py::test_http_auth_mangling[None-https://user:<EMAIL>-https://user:<EMAIL>]", "tests/test_git_hook.py::test_http_auth_mangling[some_key-http://<EMAIL>-http://user:<EMAIL>]", "tests/test_git_hook.py::test_http_auth_mangling[some_key-https://:@some.where-https://:<EMAIL>]", "tests/test_git_hook.py::test_http_auth_mangling[some_key-https://@some.where-https://:<EMAIL>]", "tests/test_git_hook.py::test_http_auth_mangling[some_key-https://user:@some.where-https://user:<EMAIL>]", "tests/test_git_hook.py::test_http_auth_mangling[some_key-https://<EMAIL>-https://user:<EMAIL>]", "tests/test_git_hook.py::test_http_auth_mangling[some_key-https://<EMAIL>/folder/project.git-https://user:<EMAIL>/folder/project.git]", "tests/test_git_hook.py::test_http_auth_mangling[some_key-https://<EMAIL>/user/project.git-https://user:<EMAIL>/user/project.git]", "tests/test_git_hook.py::test_http_auth_mangling[some_other_key-https://user:<EMAIL>-https://user:<EMAIL>]", "tests/test_hv_data_load_dag.py::test_contain_tasks[healthverity_dag-task_ordering0]", "tests/test_hv_data_load_dag.py::test_task_count[healthverity_dag-10]", "tests/test_hv_data_load_dag.py::test_task_ordering[healthverity_dag]", "tests/test_hv_filter_dag.py::test_contain_tasks[healthverity_filter_patient_dag-task_ordering0]", "tests/test_hv_filter_dag.py::test_contain_tasks[healthverity_filter_patient_dag_with_sampled_data-task_ordering1]", "tests/test_hv_filter_dag.py::test_contain_user_defined_macros[healthverity_filter_patient_dag]", "tests/test_hv_filter_dag.py::test_contain_user_defined_macros[healthverity_filter_patient_dag_with_sampled_data]", "tests/test_hv_filter_dag.py::test_task_count[healthverity_filter_patient_dag-10]", "tests/test_hv_filter_dag.py::test_task_ordering[healthverity_filter_patient_dag]", "tests/test_log4j2_feature.py::test__combine__effective_settings_is_global_settings_when_missing_in_pipeline_config", "tests/test_log4j2_feature.py::test__combine__effective_settings_is_pipeline_settings_if_defined", "tests/test_log4j2_feature.py::test__combine__effective_settings_is_the_hardcoded_one", "tests/test_log4j2_feature.py::test__combine__enabled_when_any_enabled[Both False]", "tests/test_log4j2_feature.py::test__combine__enabled_when_any_enabled[Both True]", "tests/test_log4j2_feature.py::test__combine__enabled_when_any_enabled[Global True]", "tests/test_log4j2_feature.py::test__combine__enabled_when_any_enabled[Pipeline True]", "tests/test_log4j2_feature.py::test__creation", "tests/test_log4j2_feature.py::test__deserialize", "tests/test_log4j2_feature.py::test__effective_settings__are_the_hardcoded_one_by_default", "tests/test_log4j2_feature.py::test_default_is_enabled", "tests/test_log4j2_feature.py::test_default_settings_is_none", "tests/test_log4j2_feature.py::test_hardcoded_log4j2_settings", "tests/test_params_all.py::test_cuts_common_config", "tests/test_params_all.py::test_cuts_dag_config", "tests/test_params_all.py::test_cuts_global_config", "tests/test_params_all.py::test_cuts_pipeline_config", "tests/test_params_all.py::test_nocuts_common_config", "tests/test_params_all.py::test_nocuts_dag_config", "tests/test_params_all.py::test_nocuts_global_config", "tests/test_params_all.py::test_nocuts_pipeline_config", "tests/test_params_cluster.py::test_full_job_submit_params", "tests/test_params_cluster.py::test_full_patient_job_submit_params", "tests/test_params_cluster.py::test_full_shard_job_submit_params", "tests/test_params_cluster.py::test_full_task_job_submit_params", "tests/test_params_cluster.py::test_gen_xwalk_task_job_submit_libraries", "tests/test_params_cluster.py::test_job_submit_libraries", "tests/test_params_cluster.py::test_job_submit_params", "tests/test_params_cluster.py::test_mem_bound_full_task_job_submit_params", "tests/test_params_cluster.py::test_memory_bound_job_submit_params", "tests/test_params_cluster.py::test_memory_bound_job_submit_params_graviton", "tests/test_params_cluster.py::test_memory_bound_job_submit_params_non_graviton", "tests/test_params_cluster.py::test_normal_task_job_submit_params", "tests/test_params_cluster.py::test_rvf_task_job_submit_libraries", "tests/test_params_cluster.py::test_simple_full_task_job_submit_params", "tests/test_params_cluster.py::test_simple_full_task_with_c_class_job_submit_params", "tests/test_params_cluster.py::test_single_node_task_job_submit_params", "tests/test_params_cluster.py::test_unarchiver_submit_params", "tests/test_params_cluster.py::test_unarchiver_submit_params_with_override", "tests/test_params_cluster_k8s.py::test_cluster_config_validation_with_no_architecture", "tests/test_params_cluster_k8s.py::test_cluster_config_validation_with_no_architecture_no_disk", "tests/test_params_cluster_k8s.py::test_cluster_config_validation_with_no_architecture_no_disk2", "tests/test_params_cluster_k8s.py::test_cluster_config_validation_with_no_driver_conf_and_autoscale", "tests/test_params_cluster_k8s.py::test_cluster_config_validation_with_no_num_workers", "tests/test_params_cluster_k8s.py::test_cluster_config_validation_with_no_worker_conf", "tests/test_params_cluster_k8s.py::test_cluster_config_validation_with_node_selector_labels", "tests/test_params_cluster_k8s.py::test_default_availablity_zone", "tests/test_params_cluster_k8s.py::test_default_availablity_zone_override", "tests/test_params_cluster_k8s.py::test_full_job_submit_hadoop_conf", "tests/test_params_cluster_k8s.py::test_full_job_submit_spark_conf", "tests/test_params_cluster_k8s.py::test_full_patient_job_submit_hadoop_conf", "tests/test_params_cluster_k8s.py::test_full_patient_job_submit_spark_conf", "tests/test_params_cluster_k8s.py::test_full_shard_job_submit_hadoop_conf", "tests/test_params_cluster_k8s.py::test_full_shard_job_submit_spark_conf", "tests/test_params_cluster_k8s.py::test_full_task_job_submit_params", "tests/test_params_cluster_k8s.py::test_gen_simple_worker_overrides_with_simple_args", "tests/test_params_cluster_k8s.py::test_get_memory_bound_overrides_with_simple_args", "tests/test_params_cluster_k8s.py::test_job_submit_hadoop_conf", "tests/test_params_cluster_k8s.py::test_job_submit_libraries", "tests/test_params_cluster_k8s.py::test_job_submit_spark_conf", "tests/test_params_cluster_k8s.py::test_mem_bound_full_task_job_submit_params", "tests/test_params_cluster_k8s.py::test_memory_bound_job_submit_params", "tests/test_params_cluster_k8s.py::test_memory_bound_job_submit_params_graviton", "tests/test_params_cluster_k8s.py::test_memory_bound_job_submit_params_non_graviton", "tests/test_params_cluster_k8s.py::test_normal_task_job_submit_cluster_config", "tests/test_params_cluster_k8s.py::test_simple_full_task_job_submit_params", "tests/test_params_cluster_k8s.py::test_single_node_tasks_cluster_config", "tests/test_patient_prep.py::test_copy_enum_lookup[tests/resources/enums/enum_lookup.csv-enum,value,label\\nGENDER,M,Male\\nGENDER,F,Female\\n]", "tests/test_patient_prep.py::test_copy_enum_lookup[tests/resources/enums/enum_lookup2.csv-enum,value,label\\n]", "tests/test_patient_prep.py::test_copy_enum_lookup[tests/resources/enums/enum_lookup3.csv-enum,value,label\\nGENDER,M,Male\\n]", "tests/test_patient_prep.py::test_copy_enum_lookup_missing_dataset_description", "tests/test_patient_prep.py::test_copy_enum_lookup_missing_dataset_family", "tests/test_patient_prep.py::test_execute[data-transform/preprocess-evgeniy-synpuf_preprocess-00000000]", "tests/test_patient_prep.py::test_execute[data-transform/test_dbc-adip1-synpuf_subset-20240307]", "tests/test_patient_prep.py::test_generate_rdc_dataset_desc", "tests/test_patient_prep.py::test_merge_transform_directories[standard_default-None]", "tests/test_patient_prep.py::test_merge_transform_directories[standard_default-empty_custom]", "tests/test_patient_prep.py::test_merge_transform_directories[standard_default-full_custom]", "tests/test_patient_prep.py::test_merge_transform_directories[standard_default-nonexistant]", "tests/test_patient_prep.py::test_upload_sql", "tests/test_patient_prep.py::test_validate_global_sql[test1-Please delete DATASET_DESC from global.sql and copy and paste the following entry to raw-data-catalog.yml to its respective dataset entry: \\n\\ndataset_description: {tag: \"default\", short_description: \"Optum CDM\", long_description: \"Clinformatics Data Mart\"}\\n\\nDataset name is \"DATASET_NAME\". Please check that upload path is equal to \"DATASET_NAME\". If not, set legacy_dataset_name to \"DATASET_NAME\". If legacy_dataset_name is already set, please change it to legacy_attribute_prefix.\\n\\nTag is \"default\".\\n]", "tests/test_patient_prep.py::test_validate_global_sql[test2-Please delete DATASET_DESC from global.sql and copy and paste the following entry to raw-data-catalog.yml to its respective dataset entry: \\n\\ndataset_description: {tag: \"sanofi\", short_description: \"Optum CDM\", long_description: \"Clinformatics Data Mart\"}\\n\\nDataset name is \"UNITED_CDM\". Please check that upload path is equal to \"UNITED_CDM\". If not, set legacy_dataset_name to \"UNITED_CDM\". If legacy_dataset_name is already set, please change it to legacy_attribute_prefix.\\n\\nTag is \"sanofi\".\\n]", "tests/test_patient_prep.py::test_validate_global_sql[test3-Please delete DATASET_DESC from global.sql and copy and paste the following entry to raw-data-catalog.yml to its respective dataset entry: \\n\\ndataset_description: {tag: \"default\", short_description: \"Optum CDM\", long_description: \"Clinformatics Data Mart\", gender_attribute: \"PATIENT/SEX\", coding_systems: \"common, marketscan\", enrollment_attribute: \"PATIENT/CRD_ENROLLMENT\", sample_shard_num: \"5\", sample_shard_pct: \"0.005\"}\\n\\nDataset name is \"DATASET_NAME\". Please check that upload path is equal to \"DATASET_NAME\". If not, set legacy_dataset_name to \"DATASET_NAME\". If legacy_dataset_name is already set, please change it to legacy_attribute_prefix.\\n\\nTag is \"default\".\\n]", "tests/test_patient_prep.py::test_validate_global_sql_pass", "tests/test_preflight_dag.py::TestWithMockHttpServer::test_check_http_error", "tests/test_preflight_dag.py::TestWithMockHttpServer::test_check_http_redirect", "tests/test_preflight_dag.py::TestWithMockHttpServer::test_check_http_transport", "tests/test_preflight_dag.py::TestWithMockHttpServer::test_http_username", "tests/test_preflight_dag.py::test_check_http_redirect[None-Skipping access_internet check since variable 'preflight_example_http_endpoint' is not set-None]", "tests/test_preflight_dag.py::test_check_http_redirect[google.com-None-None]", "tests/test_preflight_dag.py::test_check_http_redirect[http://google.com-None-None]", "tests/test_preflight_dag.py::test_check_http_redirect[nosuchhost-Failed to get a response from https://nosuchhost while performing an http request-URLError]", "tests/test_preflight_dag.py::test_ssh_connectivity[ssh://github.com-None-None]", "tests/test_preflight_dag.py::test_ssh_connectivity[ssh://github.com:221-Could not connect to-TimeoutError]", "tests/test_preflight_dag.py::test_ssh_connectivity[ssh://nosuchhost.local-Could not connect to-gaierror]", "tests/test_preprocess_prep.py::TestPreprocessPrep::test_execute_preprocess_file_not_exist", "tests/test_preprocess_prep.py::TestPreprocessPrep::test_execute_success", "tests/test_preprocess_prep.py::TestPreprocessPrep::test_upload_failure", "tests/test_shard_utils.py::test_generate_shard_uuid", "tests/test_sql_validator.py::test_execute", "tests/test_super_dag.py::test_contain_tasks[super_dag_test1-task_ordering0]", "tests/test_super_dag.py::test_task_count[super_dag_test1-8]", "tests/test_super_dag.py::test_task_ordering[super_dag_test1]", "tests/test_task_pyspark_failure_callback.py::TestTaskPysparkFailureCallback::test_task_jvmspark_databricks_uses_default_callback", "tests/test_task_pyspark_failure_callback.py::TestTaskPysparkFailureCallback::test_task_jvmspark_uses_default_callback", "tests/test_task_pyspark_failure_callback.py::TestTaskPysparkFailureCallback::test_task_jvmspark_uses_specific_callback", "tests/test_task_pyspark_failure_callback.py::TestTaskPysparkFailureCallback::test_task_pyspark_accepts_on_failure_callback", "tests/test_task_pyspark_failure_callback.py::TestTaskPysparkFailureCallback::test_task_pyspark_databricks_uses_default_callback", "tests/test_task_pyspark_failure_callback.py::TestTaskPysparkFailureCallback::test_task_pyspark_works_without_callback", "tests/test_utils.py::test_extract_gdr_from_spec", "tests/test_utils.py::test_find_prev_revision_in_fs[20220212-None]", "tests/test_utils.py::test_find_prev_revision_in_fs[20220229-20220224]", "tests/test_utils.py::test_find_prev_revision_in_fs[99999999-20220229]", "tests/test_validation_prep.py::test_execute[preprocess-evgeniy-synpuf_preprocess-00000000-data-validation-full]", "tests/test_validation_prep.py::test_execute[preprocess-evgeniy-synpuf_preprocess-00000000-data-validation]", "tests/test_validation_prep.py::test_execute[test_dbc-adip1-synpuf_subset-20240307-data-validation-full]", "tests/test_validation_prep.py::test_execute[test_dbc-adip1-synpuf_subset-20240307-data-validation]", "tests/tools/test_config.py::test_resolve_databricks_libraries__databricks_above_v14", "tests/tools/test_config.py::test_resolve_databricks_libraries__databricks_under_v14", "tests/tools/test_git_utils.py::test_generated_branch[False-2025-06-10-client_ds_2025-06-10]", "tests/tools/test_git_utils.py::test_generated_branch[False-r1-client_ds_r1]", "tests/tools/test_git_utils.py::test_generated_branch[True-2025-06-10-client_ds_2025-06-10]", "tests/tools/test_git_utils.py::test_generated_branch[True-r1-client_ds_r1]", "tests/tools/test_git_utils.py::test_get_client_branch_name_all_empty[!@#$-__-   ]", "tests/tools/test_git_utils.py::test_get_client_branch_name_all_empty[Client 1-!@#$-Rev_2]", "tests/tools/test_git_utils.py::test_get_client_branch_name_success[Client-Data-Rev-client-data-rev]", "tests/tools/test_git_utils.py::test_get_client_branch_name_success[a b c-d_e_f-G-H-I-a_b_c-d_e_f-g_h_i]", "tests/tools/test_git_utils.py::test_get_client_branch_name_success[hu.m-a_na-dataset_name-1234-hu_m_a_na-dataset_name-1234]", "tests/tools/test_git_utils.py::test_merge_file_to_git_creates_expected_effects[featureX-False-False]", "tests/tools/test_git_utils.py::test_merge_file_to_git_creates_expected_effects[featureX-True-False]", "tests/tools/test_git_utils.py::test_merge_file_to_git_creates_expected_effects[master-True-True]", "tests/tools/test_git_utils.py::test_override_branch[False-feature-xyz-master-feature-xyz]", "tests/tools/test_git_utils.py::test_override_branch[False-hotfix-123-develop-hotfix-123]", "tests/tools/test_git_utils.py::test_override_branch[True-feature-xyz-master-feature-xyz]", "tests/tools/test_git_utils.py::test_override_branch[True-hotfix-123-develop-hotfix-123]", "tests/tools/test_git_utils.py::test_sanitize_for_branch_various[!@#$%^&*()-]", "tests/tools/test_git_utils.py::test_sanitize_for_branch_various[MY__BRANCH-my_branch]", "tests/tools/test_git_utils.py::test_sanitize_for_branch_various[MixED Case_and-Spaces-mixed_case_and_spaces]", "tests/tools/test_git_utils.py::test_sanitize_for_branch_various[My Branch-my_branch]", "tests/tools/test_git_utils.py::test_sanitize_for_branch_various[_branch name_-branch_name]", "tests/tools/test_git_utils.py::test_sanitize_for_branch_various[branch--name-branch_name]", "tests/tools/test_git_utils.py::test_sanitize_for_branch_various[hu.m-a_na-hu_m_a_na]", "tests/tools/test_has_coding_systems.py::test_has_coding_systems_inherited", "tests/tools/test_has_coding_systems.py::test_has_coding_systems_none", "tests/tools/test_has_coding_systems.py::test_has_coding_systems_present"]