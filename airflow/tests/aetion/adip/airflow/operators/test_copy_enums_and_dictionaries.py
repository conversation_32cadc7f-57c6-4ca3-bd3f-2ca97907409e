from importlib import resources
from collections import OrderedDict
from unittest.mock import Mock, patch

import pytest
from aetion.adip.airflow.integrations.s3.s3_support import AetionS3FileSystem
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from operators.copy_enums_and_dictionaries import CopyEnumsAndDictionaries


@pytest.fixture
def setup_bucket(s3_hook: S3Hook):
    """
    Create a test S3 bucket for data processing tests.
    """

    bucket_name = "test-bucket"
    s3_hook.create_bucket(bucket_name=bucket_name)
    return bucket_name


@pytest.fixture
def setup_s3_data(s3_hook: S3Hook, setup_bucket: str):
    """
    Populate S3 test bucket with realistic enum data for testing merge scenarios.
    """

    bucket = setup_bucket

    prev_enums = resources.read_text(
        "tests.resources.enums.super_dbc", "previous_enums.csv"
    )
    s3_hook.load_string(
        prev_enums,
        key="tests/resources/enums/super_dbc/previous_enums.csv",
        bucket_name=bucket,
    )

    new_enums = resources.read_text("tests.resources.enums.super_dbc", "new_enums.csv")
    s3_hook.load_string(
        new_enums,
        key="tests/resources/enums/super_dbc/new_enums.csv",
        bucket_name=bucket,
    )

    return bucket


@pytest.fixture
def copy_operator():
    """
    Create a configured CopyEnumsAndDictionaries operator for data processing tests.
    """

    return CopyEnumsAndDictionaries(
        transform_path="s3://test-bucket/transform",
        source_enum_and_dict_path="s3://test-bucket/source",
        dest_enum_and_dict_path="s3://test-bucket/dest",
        copy_parallelism=2,
        git_meta_repo="test-repo",
        aws_conn_id="aws_default",
        task_id="test_copy_enums",
    )


@pytest.fixture
def mock_filesystem():
    """
    Create a mock AetionS3FileSystem for isolated testing of operator logic.
    """

    fs = Mock(spec=AetionS3FileSystem)
    fs.exists.return_value = True
    fs.open.return_value.__enter__ = Mock()
    fs.open.return_value.__exit__ = Mock()
    fs.copy_with_pattern_parallel = Mock()
    return fs


class TestCopyEnumsAndDictionaries:
    def test_init_with_all_parameters(self):
        """Test initialization with all parameters."""

        # Given: All possible initialization parameters for data processing
        # including custom S3 paths, parallelism settings, and AWS connection

        # When: The CopyEnumsAndDictionaries operator is instantiated with all parameters
        operator = CopyEnumsAndDictionaries(
            transform_path="s3://bucket/transform",
            source_enum_and_dict_path="s3://bucket/source",
            dest_enum_and_dict_path="s3://bucket/dest",
            copy_parallelism=4,
            git_meta_repo="test-repo",
            aws_conn_id="custom_conn",
            task_id="test_task",
        )

        # Then: All parameters should be stored correctly for data processing
        assert operator.transform_path == "s3://bucket/transform"
        assert operator.source_enum_and_dict_path == "s3://bucket/source"
        assert operator.dest_enum_and_dict_path == "s3://bucket/dest"
        assert operator.copy_parallelism == 4
        assert operator.aws_conn_id == "custom_conn"
        assert operator._fs is None  # S3 filesystem should be lazy-initialized

    def test_init_with_minimal_parameters(self):
        """Test initialization with minimal required parameters."""

        # Given: Only essential parameters required for data processing
        # with default AWS connection and standard parallelism settings

        # When: The operator is instantiated with minimal parameters (optional params omitted)
        operator = CopyEnumsAndDictionaries(
            transform_path="s3://bucket/transform",
            source_enum_and_dict_path="s3://bucket/source",
            dest_enum_and_dict_path="s3://bucket/dest",
            copy_parallelism=2,
            task_id="test_task",
        )

        # Then: Default values should be applied and operator should be functional
        assert operator.aws_conn_id == "aws_default"  # Should default to standard AWS connection
        assert operator._fs is None  # S3 filesystem should remain uninitialized until first use

    def test_fs_property_lazy_loading(self, copy_operator):
        """Test that fs property creates and caches AetionS3FileSystem instance."""

        # Given: A CopyEnumsAndDictionary Operator with S3 filesystem initially uninitialized
        # and lazy loading pattern

        # When: The fs property is accessed for the first time
        fs1 = copy_operator.fs

        # Then: First access should create a new AetionS3FileSystem instance
        assert fs1 is not None
        assert isinstance(fs1, AetionS3FileSystem)
        assert copy_operator._fs is fs1  # Instance should be cached in _fs attribute

        # When: The fs property is accessed again in subsequent operations
        fs2 = copy_operator.fs

        # Then: Second access should return the same cached instance (no recreation)
        assert fs2 is fs1

    @patch.object(CopyEnumsAndDictionaries, "copy_dictionaries")
    @patch.object(CopyEnumsAndDictionaries, "resolve_enums")
    @patch.object(CopyEnumsAndDictionaries, "upload_enums")
    def test_execute_success(self, mock_upload, mock_resolve, mock_copy, copy_operator):
        """Test successful execution."""

        # Given: A CopyEnumsAndDictionary Operator with mocked methods for the complete workflow
        # and simulated enum resolution returning code mappings
        mock_resolve.return_value = {"E1": {"1": "label1"}}

        # When: The operator's execute method is called with Airflow context
        # processing dictionaries and enums sequentially
        copy_operator.execute({})

        # Then: All methods should be called with correct data pipeline paths
        mock_copy.assert_called_once_with(
            "s3://test-bucket/source/dict/", "s3://test-bucket/dest/dict/"
        )
        mock_resolve.assert_called_once_with(
            copy_operator.fs,
            "s3://test-bucket/transform/enums.csv",
            "s3://test-bucket/source/enums/enums.csv",
        )
        mock_upload.assert_called_once_with(
            {"E1": {"1": "label1"}}, "s3://test-bucket/dest/enums/enums.csv"
        )

    def test_copy_dictionaries_success(self, copy_operator, mock_filesystem):
        """Test successful dictionary copying."""

        # Given: A CopyEnumsAndDictionary Operator with mock S3 filesystem for testing
        # dictionary file operations (ICD codes, procedure codes, etc.)
        copy_operator._fs = mock_filesystem

        # When: The copy_dictionaries method is called with dictionary paths
        # using parallel processing to efficiently copy multiple dictionary files
        copy_operator.copy_dictionaries(
            "s3://bucket/source/dict/", "s3://bucket/dest/dict/"
        )

        # Then: The S3 filesystem should be called with correct parameters for data
        mock_filesystem.copy_with_pattern_parallel.assert_called_once_with(
            "s3://bucket/source/dict/", "s3://bucket/dest/dict/", ".*", 2
        )

    def test_copy_dictionaries_with_custom_parallelism(self, mock_filesystem):
        """Test dictionary copying with custom parallelism."""

        # Given: A CopyEnumsAndDictionary Operator configured with high parallelism (8 workers)
        # for large datasets requiring optimized performance
        operator = CopyEnumsAndDictionaries(
            transform_path="s3://bucket/transform",
            source_enum_and_dict_path="s3://bucket/source",
            dest_enum_and_dict_path="s3://bucket/dest",
            copy_parallelism=8,
            task_id="test_task",
        )
        operator._fs = mock_filesystem

        # When: Dictionary copying is performed with custom parallelism settings
        # for efficient processing of extensive coding systems
        operator.copy_dictionaries("s3://bucket/source/dict/", "s3://bucket/dest/dict/")

        # Then: The S3 filesystem should use the custom parallelism setting (8 workers)
        # enabling faster processing of large datasets
        mock_filesystem.copy_with_pattern_parallel.assert_called_once_with(
            "s3://bucket/source/dict/", "s3://bucket/dest/dict/", ".*", 8
        )

    def test_resolve_enums_both_files_exist(
        self, s3_filesystem: AetionS3FileSystem, setup_s3_data: str
    ):
        """Test enum resolution when both previous and new enums exist."""

        # Given: Previous enum file with existing code mappings (E1 with codes 3,4,5)
        # and new enum file with additional codes and categories (E1 extended, E2 added)
        # simulating SYNPUF dataset processing scenarios
        bucket = setup_s3_data

        # When: The resolve_enums method processes both files with intelligent merging
        # preserving existing code mappings while adding new codes
        final_enums = CopyEnumsAndDictionaries.resolve_enums(
            s3_filesystem,
            f"s3://{bucket}/tests/resources/enums/super_dbc/previous_enums.csv",
            f"s3://{bucket}/tests/resources/enums/super_dbc/new_enums.csv",
        )

        # Then: Final enums should contain all codes from both sources
        # E1 merges existing (3,4,5) + new (0,1,2), E2 added completely (0,7)
        # with existing labels preserved and order maintained for processing
        expected = {
            "E1": {
                "3": "3",
                "4": "existing label 4",  # Existing label preserved
                "5": "5",
                "2": "2",
                "1": "1",
                "0": "0",
            },
            "E2": {"7": "7", "0": "new label 0"},  # New category added completely
        }
        assert final_enums == expected

    def test_resolve_enums_only_previous_exists(
        self, s3_filesystem: AetionS3FileSystem, setup_s3_data: str
    ):
        """Test enum resolution when only previous enums exist."""

        # Given: Previous enum file with existing code mappings
        # and non-existent new enum file (simulating missing or delayed data source)
        bucket = setup_s3_data

        # When: The resolve_enums method is called with existing previous and missing new files
        # requiring robust handling of missing sources in data pipeline
        final_enums = CopyEnumsAndDictionaries.resolve_enums(
            s3_filesystem,
            f"s3://{bucket}/tests/resources/enums/super_dbc/previous_enums.csv",
            f"s3://{bucket}/nonexistent.csv",
        )

        # Then: Should return only previous enum mappings without errors
        # enabling data processing to continue with existing code mappings
        expected = {"E1": {"3": "3", "4": "existing label 4", "5": "5"}}
        assert final_enums == expected

    def test_resolve_enums_only_new_exists(
        self, s3_filesystem: AetionS3FileSystem, setup_s3_data: str
    ):
        """Test enum resolution when only new enums exist."""

        # Given: Non-existent previous enum file (initial data load scenario)
        # and new enum file with fresh code mappings from source system
        bucket = setup_s3_data

        # When: The resolve_enums method processes new dataset without historical context
        # with comprehensive codes (E1 with codes 0-5, E2 with codes 0,7)
        final_enums = CopyEnumsAndDictionaries.resolve_enums(
            s3_filesystem,
            f"s3://{bucket}/nonexistent.csv",
            f"s3://{bucket}/tests/resources/enums/super_dbc/new_enums.csv",
        )

        # Then: Should return only new enum mappings with all codes preserved
        # providing complete code mappings for fresh data processing
        expected = {
            "E1": {"5": "5", "4": "4", "3": "3", "2": "2", "1": "1", "0": "0"},
            "E2": {"7": "7", "0": "new label 0"},
        }
        assert final_enums == expected

    def test_resolve_enums_neither_exists(
        self, s3_filesystem: AetionS3FileSystem, setup_bucket: str
    ):
        """Test enum resolution when neither file exists."""
        # Given: Non-existent previous and new enum files
        # simulating complete absence of enum data in pipeline
        bucket = setup_bucket

        # When: The resolve_enums method is called with both files missing
        # testing graceful handling of missing code mappings
        final_enums = CopyEnumsAndDictionaries.resolve_enums(
            s3_filesystem,
            f"s3://{bucket}/nonexistent1.csv",
            f"s3://{bucket}/nonexistent2.csv",
        )

        # Then: Should return {} indicating no enum data available
        # maintaining system stability when code mappings are unavailable
        assert final_enums == {}

    def test_resolve_enums_with_empty_files(
        self, s3_filesystem: AetionS3FileSystem, s3_hook: S3Hook, setup_bucket: str
    ):
        """Test enum resolution with empty CSV files."""

        # Given: Empty CSV files with only headers (no code data)
        # simulating scenario where enum files exist but contain no mappings
        bucket = setup_bucket
        empty_csv = "enum,code,label\n"
        s3_hook.load_string(empty_csv, key="empty1.csv", bucket_name=bucket)
        s3_hook.load_string(empty_csv, key="empty2.csv", bucket_name=bucket)

        # When: The resolve_enums method processes empty enum files
        final_enums = CopyEnumsAndDictionaries.resolve_enums(
            s3_filesystem,
            f"s3://{bucket}/empty1.csv",
            f"s3://{bucket}/empty2.csv",
        )

        # Then: Should return {} indicating no code mappings available
        assert final_enums == {}

    def test_resolve_enums_with_malformed_csv(
        self, s3_filesystem: AetionS3FileSystem, s3_hook: S3Hook, setup_bucket: str
    ):
        """Test enum resolution with malformed CSV files."""

        # Given: Malformed CSV file missing required columns for enum structure
        # testing robustness when source data doesn't match expected format
        bucket = setup_bucket
        malformed_csv = "enum,code\nE1,1\n"  # Missing 'label' column
        s3_hook.load_string(malformed_csv, key="malformed.csv", bucket_name=bucket)

        # When: The resolve_enums method attempts to process malformed data
        # Then: Should raise an exception due to missing columns in enum structure
        with pytest.raises(Exception):  # Healthcare enum processing requires all columns
            CopyEnumsAndDictionaries.resolve_enums(
                s3_filesystem,
                f"s3://{bucket}/malformed.csv",
                f"s3://{bucket}/nonexistent.csv",
            )

    def test_resolve_enums_overlapping_enum_names_different_codes(
        self, s3_filesystem: AetionS3FileSystem, s3_hook: S3Hook, setup_bucket: str
    ):
        """Test enum merging with overlapping enum names but different codes."""

        # Given: Previous enums with existing codes (E1: 1,2)
        # and new enums with same category but different codes (E1: 3,4)
        bucket = setup_bucket
        prev_csv = "enum,code,label\nE1,1,old_label1\nE1,2,old_label2\n"
        s3_hook.load_string(prev_csv, key="prev_overlap.csv", bucket_name=bucket)

        new_csv = "enum,code,label\nE1,3,new_label3\nE1,4,new_label4\n"
        s3_hook.load_string(new_csv, key="new_overlap.csv", bucket_name=bucket)

        # When: The resolve_enums method merges enums with same category names
        # but different code values (expanding the code range for E1)
        final_enums = CopyEnumsAndDictionaries.resolve_enums(
            s3_filesystem,
            f"s3://{bucket}/prev_overlap.csv",
            f"s3://{bucket}/new_overlap.csv",
        )

        # Then: Should merge all codes within the same enum category
        # preserving existing codes and adding new ones for comprehensive mapping
        expected = {
            "E1": {
                "1": "old_label1",  # Previous codes preserved
                "2": "old_label2",
                "3": "new_label3",  # New codes added
                "4": "new_label4",
            }
        }
        assert final_enums == expected

    def test_resolve_enums_overlapping_codes_keeps_previous(
        self, s3_filesystem: AetionS3FileSystem, s3_hook: S3Hook, setup_bucket: str
    ):
        """Test that overlapping codes keep the previous label."""

        # Given: Previous enum with established code-label mapping
        # and new enum with same code but different label (conflict scenario)
        bucket = setup_bucket
        prev_csv = "enum,code,label\nE1,1,previous_label\n"
        s3_hook.load_string(prev_csv, key="prev_conflict.csv", bucket_name=bucket)

        new_csv = "enum,code,label\nE1,1,new_label\n"
        s3_hook.load_string(new_csv, key="new_conflict.csv", bucket_name=bucket)

        # When: The resolve_enums method encounters conflicting code mappings
        # with same enum category and code but different labels
        final_enums = CopyEnumsAndDictionaries.resolve_enums(
            s3_filesystem,
            f"s3://{bucket}/prev_conflict.csv",
            f"s3://{bucket}/new_conflict.csv",
        )

        # Then: Should preserve the previous label to maintain data consistency
        # ensuring established code mappings are not overwritten
        expected = {"E1": {"1": "previous_label"}}
        assert final_enums == expected

    def test_upload_enums_success(
        self, copy_operator, s3_filesystem: AetionS3FileSystem, setup_bucket: str
    ):
        """Test successful enum upload."""

        # Given: A CopyEnumsAndDictionary Operator with enum data representing code mappings
        # (E1 with codes 1,2 and E2 with code 0) for processing
        bucket = setup_bucket
        copy_operator._fs = s3_filesystem

        enums_data = OrderedDict(
            [
                ("E1", OrderedDict([("1", "label1"), ("2", "label2")])),
                ("E2", OrderedDict([("0", "label0")])),
            ]
        )

        # When: The upload_enums method writes enum data to S3 in CSV format
        dest_path = f"s3://{bucket}/uploaded_enums.csv"
        copy_operator.upload_enums(enums_data, dest_path)

        # Then: File should be uploaded successfully with correct enum structure
        assert s3_filesystem.exists(dest_path)

        # And: Content should match expected CSV format for code mappings
        content = s3_filesystem.cat(dest_path, as_bytes=False)
        lines = content.strip().splitlines()

        assert lines[0] == "enum,code,label"  # Header row
        assert "E1,1,label1" in lines  # Healthcare enum entries
        assert "E1,2,label2" in lines
        assert "E2,0,label0" in lines

    def test_upload_enums_empty_dict(
        self, copy_operator, s3_filesystem: AetionS3FileSystem, setup_bucket: str
    ):
        """Test uploading empty enum dictionary."""

        # Given: A CopyEnumsAndDictionary Operator with empty enum data
        # simulating scenario where no code mappings are available
        bucket = setup_bucket
        copy_operator._fs = s3_filesystem

        # When: The upload_enums method is called with empty enum dictionary
        dest_path = f"s3://{bucket}/empty_enums.csv"
        copy_operator.upload_enums(OrderedDict(), dest_path)

        # Then: File should still be uploaded with valid CSV header structure
        assert s3_filesystem.exists(dest_path)
        content = s3_filesystem.cat(dest_path, as_bytes=False)
        assert content.strip() == "enum,code,label"

    def test_upload_enums_complex_structure(
        self, copy_operator, s3_filesystem: AetionS3FileSystem, setup_bucket: str
    ):
        """Test uploading complex nested enum structures."""

        # Given: Complex enum data with multiple categories and codes
        # representing realistic processing scenarios (STATUS, PRIORITY)
        bucket = setup_bucket
        copy_operator._fs = s3_filesystem

        enums_data = OrderedDict(
            [
                (
                    "STATUS",
                    OrderedDict([("0", "Inactive"), ("1", "Active"), ("2", "Pending")]),
                ),
                (
                    "PRIORITY",
                    OrderedDict(
                        [
                            ("HIGH", "High Priority"),
                            ("MED", "Medium Priority"),
                            ("LOW", "Low Priority"),
                        ]
                    ),
                ),
            ]
        )

        # When: The upload_enums method processes complex nested enum structures
        dest_path = f"s3://{bucket}/complex_enums.csv"
        copy_operator.upload_enums(enums_data, dest_path)

        # Then: Content should be properly structured with all enum entries
        content = s3_filesystem.cat(dest_path, as_bytes=False)
        lines = content.strip().splitlines()

        assert len(lines) == 7  # Header + 6 data rows for enums
        assert lines[0] == "enum,code,label"

        # And: All enum entries should be present in CSV format
        content_str = "\n".join(lines)
        assert "STATUS,0,Inactive" in content_str  # STATUS enum codes
        assert "STATUS,1,Active" in content_str
        assert "STATUS,2,Pending" in content_str
        assert "PRIORITY,HIGH,High Priority" in content_str  # PRIORITY enum codes
        assert "PRIORITY,MED,Medium Priority" in content_str
        assert "PRIORITY,LOW,Low Priority" in content_str

    def test_resolve_enums_preserves_order(
        self, s3_filesystem: AetionS3FileSystem, s3_hook: S3Hook, setup_bucket: str
    ):
        """Test that enum resolution preserves order using OrderedDict."""

        # Given: enum CSV with specific code order (3,1,2)
        # testing that OrderedDict preserves insertion order for consistent processing
        bucket = setup_bucket
        ordered_csv = "enum,code,label\nE1,3,third\nE1,1,first\nE1,2,second\n"
        s3_hook.load_string(ordered_csv, key="ordered.csv", bucket_name=bucket)

        # When: The resolve_enums method processes enum data
        # using OrderedDict to maintain code sequence for downstream consistency
        final_enums = CopyEnumsAndDictionaries.resolve_enums(
            s3_filesystem,
            f"s3://{bucket}/ordered.csv",
            f"s3://{bucket}/nonexistent.csv",
        )

        # Then: Order should be preserved exactly as in source data
        enum_codes = list(final_enums["E1"].keys())
        assert enum_codes == ["3", "1", "2"]  # Original insertion order maintained

    @patch.object(AetionS3FileSystem, "copy_with_pattern_parallel")
    def test_copy_dictionaries_filesystem_error(self, mock_copy, copy_operator):
        """Test copy_dictionaries handles filesystem errors."""

        # Given: A CopyEnumsAndDictionary Operator with S3 filesystem configured to fail
        # simulating network issues or permission problems during dictionary copying
        mock_copy.side_effect = Exception("S3 connection error")

        # When: The copy_dictionaries method encounters S3 filesystem errors
        # Then: Should propagate the exception for proper error handling in pipeline
        with pytest.raises(Exception, match="S3 connection error"):
            copy_operator.copy_dictionaries(
                "s3://bucket/source/dict/", "s3://bucket/dest/dict/"
            )

    @patch.object(AetionS3FileSystem, "open")
    def test_upload_enums_filesystem_error(self, mock_open, copy_operator):
        """Test upload_enums handles filesystem errors."""

        # Given: A CopyEnumsAndDictionary Operator with S3 filesystem configured to fail writes
        # simulating permission issues or storage problems during enum upload
        mock_open.side_effect = Exception("S3 write error")
        enums_data = OrderedDict([("E1", OrderedDict([("1", "label1")]))])

        # When: The upload_enums method encounters S3 write errors
        # Then: Should propagate the exception for proper error handling in pipeline
        with pytest.raises(Exception, match="S3 write error"):
            copy_operator.upload_enums(enums_data, "s3://bucket/dest/enums.csv")

    def test_template_fields(self):
        """Test that template fields are correctly defined."""

        # Given: The CopyEnumsAndDictionaries operator class definition
        # When: Checking template fields for Airflow templating support
        # Then: Should include all S3 path fields for dynamic data processing
        expected_fields = (
            "transform_path",
            "source_enum_and_dict_path",
            "dest_enum_and_dict_path",
        )
        assert CopyEnumsAndDictionaries.template_fields == expected_fields

    def test_template_ext(self):
        """Test that template extensions are correctly defined."""

        # Given: The CopyEnumsAndDictionaries operator class definition
        # When: Checking template extensions for Airflow templating support
        # Then: Should be empty tuple as no file extensions are templated
        assert CopyEnumsAndDictionaries.template_ext == ()

    def test_resolve_enums_with_unicode_characters(
        self, s3_filesystem: AetionS3FileSystem, s3_hook: S3Hook, setup_bucket: str
    ):
        """Test enum resolution with unicode characters in labels."""

        # Given: enum CSV with unicode characters in labels
        # representing international data with accented characters
        bucket = setup_bucket
        unicode_csv = (
            "enum,code,label\nSTATUS,1,Activé\nSTATUS,2,Inactif\nTYPE,1,Spécialisé\n"
        )
        s3_hook.load_string(unicode_csv, key="unicode.csv", bucket_name=bucket)

        # When: The resolve_enums method processes data with unicode characters
        # testing international character support in code mappings
        final_enums = CopyEnumsAndDictionaries.resolve_enums(
            s3_filesystem,
            f"s3://{bucket}/unicode.csv",
            f"s3://{bucket}/nonexistent.csv",
        )

        # Then: Unicode characters should be preserved correctly in enum labels
        expected = {
            "STATUS": {"1": "Activé", "2": "Inactif"},  # French accented characters
            "TYPE": {"1": "Spécialisé"},
        }
        assert final_enums == expected

    def test_copy_dictionaries_with_multiple_threads(self, s3_filesystem: AetionS3FileSystem, setup_bucket: str):
        """Test dictionary copying with multiple threads."""

        # Given: an existing bucket in S3
        bucket = setup_bucket

        # And: a CopyEnumsAndDictionaries operator with some level of parallelism
        copy_operator = CopyEnumsAndDictionaries(
            transform_path=f"s3://{bucket}/transform",
            source_enum_and_dict_path=f"s3://{bucket}/source",
            dest_enum_and_dict_path=f"s3://{bucket}/dest",
            copy_parallelism=4,
            task_id="test_copy_dictionaries_with_multiple_threads",
        )

        # And: a list of files in the source dict subfolder
        for i in range(10):
            s3_filesystem.hook.load_string(f"Content of File {i}", key=f"source/dict/file{i}.csv", bucket_name=bucket)

        # When: Dictionary copying is performed with multiple threads
        copy_operator.copy_dictionaries(
            f"{copy_operator.source_enum_and_dict_path}/dict/",
            f"{copy_operator.dest_enum_and_dict_path}/dict/"
        )

        # Then: All the files should be copied to the destination directory
        dest_files = list(s3_filesystem.walk_all(f"{copy_operator.dest_enum_and_dict_path}/dict/"))
        assert len(dest_files) == 10

