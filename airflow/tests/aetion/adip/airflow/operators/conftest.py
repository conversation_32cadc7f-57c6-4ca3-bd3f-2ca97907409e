from collections.abc import Generator
import pytest
import boto3
from moto import mock_s3
from mypy_boto3_s3 import S3Client
from aetion.adip.airflow.integrations.s3.s3_support import AetionS3FileSystem
from airflow.providers.amazon.aws.hooks.s3 import S3Hook


@pytest.fixture(scope="function", autouse=True)
def airflow_env(monkeypatch):
    """Mock Airflow environment with in-memory metadata database and AWS connection."""
    monkeypatch.setenv("AIRFLOW__DATABASE__SQL_ALCHEMY_CONN", "sqlite:///:memory:")
    monkeypatch.setenv(
        "AIRFLOW_CONN_AWS_DEFAULT",
        '{"conn_type": "aws", "login": "testing", "password": "testing", "extra": {"region_name": "us-east-1"}}',
    )


@pytest.fixture
def mockito_s3():
    """Mock S3 client."""
    with mock_s3() as aws_mock:
        yield aws_mock

@pytest.fixture
def s3_client(mockito_s3) -> Generator[S3Client, None, None]:
    """Create a mocked S3 client."""
    yield boto3.client("s3", region_name="us-east-1")

@pytest.fixture
def s3_filesystem(mockito_s3) -> Generator[AetionS3FileSystem, None, None]:
    """Create AetionS3FileSystem instance with real S3Hook using moto."""
    yield AetionS3FileSystem()


@pytest.fixture
def s3_hook(mockito_s3) -> Generator[S3Hook, None, None]:
    """Create a mocked S3 client."""
    yield S3Hook(aws_conn_id="aws_default")
