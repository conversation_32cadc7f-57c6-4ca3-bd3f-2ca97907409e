import logging
import os
import re
import tempfile
import zipfile
from collections.abc import Generator
from io import String<PERSON>
from pathlib import Path
from typing import Any
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import pytest
from aetion.adip.airflow.integrations.s3.s3_support import (
    AetionS3FileSystem,
    parse_s3_url,
)
from airflow.exceptions import AirflowException
from mypy_boto3_s3 import S3Client

from dags.operators.artifacts_publisher import ArtifactsPublisher


@pytest.fixture
def bucket(s3_client: S3Client) -> Generator[str, None, None]:
    """Create a test S3 bucket for data processing tests."""
    bucket_name = "test-bucket"
    s3_client.create_bucket(Bucket=bucket_name)
    yield bucket_name


@pytest.fixture
def sample_artifacts() -> dict[str, bool]:
    """
    Create a standard artifacts dictionary for testing artifact publishing scenarios.
    """
    return {
        "adip_utilities-0.0.1-py3-none-any.whl": True,
        "adip_utilities-0.0.1-py3-none-any.egg": True,
        "automated_ingestion_config-0.0.0-py3-none-any.whl": True,
        "automated_ingestion_config-0.0.0-py3-none-any.egg": True,
        "coding_systems.csv": False,
        "generic_runner.py": False,
        "raw-data-catalog.yml": False,
        "spark-connector-assembly-1.3-SNAPSHOT.jar": True,
        "spark_validator-1.0b0-py3-none-any.whl": True,
        "spark_validator-1.0b0-py3-none-any.egg": True,
        "unarchiver-0.0.1-py3-none-any.whl": True,
        "unarchiver-0.0.1-py3-none-any.egg": True,
        "unarchiver_cluster_init.sh": False,
    }


@pytest.fixture
def artifacts_publisher_factory(sample_artifacts: dict[str, bool], bucket: str):
    """
    Create a factory function for ArtifactsPublisher instances with customizable parameters.

    This fixture provides a factory function that creates ArtifactsPublisher instances
    with sensible defaults while allowing individual tests to override specific parameters.

    Args:
        sample_artifacts: Dictionary of artifact files and their expected formats

    Returns:
        Factory function that accepts keyword arguments to override default parameters

    Example:
        def test_something(artifacts_publisher_factory):
            # Use defaults
            publisher = artifacts_publisher_factory()

            # Override specific parameters
            publisher = artifacts_publisher_factory(
                revision="20220101",
                task_id="custom_task"
            )
    """

    def _create_publisher(**overrides: Any) -> ArtifactsPublisher:
        # Default configuration parameters
        defaults = {
            "repo": "test-repo",
            "private_key": "test-private-key",
            "git_default_branch": "main",
            "branch": "feature-branch",
            "global_artifacts_path": f"s3://{bucket}/global/artifacts",
            "dataset_artifacts_path": f"s3://{bucket}/dataset/artifacts",
            "rdc": "raw-data-catalog.yml",
            "artifacts": sample_artifacts,
            "revision": "20210901",
            "task_id": "test_publish_artifacts",
            "aws_conn_id": "aws_default",
            "git_meta_repo": None,
        }

        # Merge defaults with any overrides provided by the test
        config = {**defaults, **overrides}

        return ArtifactsPublisher(**config)  # type: ignore[arg-type]

    return _create_publisher


@pytest.fixture
def artifacts_publisher(artifacts_publisher_factory):
    """
    Create a configured ArtifactsPublisher operator with default parameters for testing.

    This fixture provides a ready-to-use ArtifactsPublisher instance with sensible
    defaults for tests that don't need parameter customization.
    """
    return artifacts_publisher_factory()


class TestArtifactsPublisher:
    """Test suite for ArtifactsPublisher operator."""

    def test_init_with_all_parameters(
        self, artifacts_publisher_factory, sample_artifacts: dict[str, bool]
    ):
        """Test initialization with all parameters."""

        # Given: All possible initialization parameters for artifact publishing
        # including custom S3 paths, git configuration, and AWS connection

        # When: The ArtifactsPublisher operator is instantiated with all parameters
        operator = artifacts_publisher_factory(
            global_artifacts_path="s3://test-bucket/global",
            dataset_artifacts_path="s3://test-bucket/dataset",
            aws_conn_id="custom_aws_conn",
            git_meta_repo="test-meta-repo",
            client="test-client",
            dataset="test-dataset",
            git_config_file_path="/config/path",
            config_file_url="s3://config/url",
            task_id="test_task",
        )

        # Then: All parameters should be properly assigned to instance variables
        assert operator.repo == "test-repo"
        assert operator.private_key == "test-private-key"
        assert operator.git_default_branch == "main"
        assert operator.branch == "feature-branch"
        assert operator.global_artifacts_path == "s3://test-bucket/global"
        assert operator.dataset_artifacts_path == "s3://test-bucket/dataset"
        assert operator.rdc == "raw-data-catalog.yml"
        assert operator.artifacts == sample_artifacts
        assert operator.aws_conn_id == "custom_aws_conn"
        assert operator.git_meta_repo == "test-meta-repo"
        assert operator.client == "test-client"
        assert operator.dataset == "test-dataset"
        assert operator.revision == "20210901"
        assert operator.git_config_file_path == "/config/path"
        assert operator.config_file_url == "s3://config/url"

    def test_check_revision_success(self, artifacts_publisher: ArtifactsPublisher):
        """Test successful revision validation with numeric revision."""

        # Given: An ArtifactsPublisher with a valid numeric revision
        # representing a standard dataset processing scenario
        publisher = artifacts_publisher

        # When: The check_revision method is called with a valid numeric revision
        # Then: The method should complete successfully without raising exceptions
        publisher.check_revision()  # Should not raise any exception

    @pytest.mark.parametrize(
        "revision",
        [
            pytest.param("20210901_v2", id="non-numeric_revision"),
            pytest.param(None, id="none"),
        ],
    )
    def test_check_revision_must_be_numeric(
        self, artifacts_publisher_factory, revision: str | None
    ):
        with pytest.raises(AirflowException):
            artifacts_publisher_factory(revision=revision).check_revision()

    def test_copy_artifacts(
        self, artifacts_publisher: ArtifactsPublisher, s3_client: S3Client, bucket: str
    ):
        """Test copying all artifacts from global to dataset path."""

        # Given: an existing bucket
        assert artifacts_publisher.artifacts

        # Given: artifacts in the global artifacts path
        global_key = parse_s3_url(artifacts_publisher.global_artifacts_path).key.lstrip(
            "/"
        )
        for artifact in artifacts_publisher.artifacts:
            s3_client.put_object(
                Bucket=bucket,
                Key=f"{global_key}/{artifact}",
                Body=b"whatever",
            )

        # When: _copy_artifacts is called
        artifacts_publisher._copy_artifacts()

        # Then: the artifacts should be in the dataset artifacts path
        dataset_key = parse_s3_url(
            artifacts_publisher.dataset_artifacts_path
        ).key.lstrip("/")
        for artifact in artifacts_publisher.artifacts:
            dataset_artifact_key = f"{dataset_key}/{artifact}"
            assert (
                s3_client.get_object(Bucket=bucket, Key=dataset_artifact_key)[
                    "Body"
                ].read()
                == b"whatever"
            )

    def test_copy_artifacts_missing_in_global(
        self, artifacts_publisher: ArtifactsPublisher, s3_client: S3Client, bucket: str
    ):
        # Given: some global artifact
        artifacts_publisher.artifacts = {
            "missing_artifact.txt": False,
        }
        # And: that global artifact does not exist in the global artifacts path
        global_key = parse_s3_url(artifacts_publisher.global_artifacts_path).key.lstrip(
            "/"
        )
        assert (
            s3_client.list_objects_v2(
                Bucket=bucket, Prefix=f"{global_key}/missing_artifact.txt"
            )["KeyCount"]
            == 0
        )

        # When: _copy_artifacts is called
        artifacts_publisher._copy_artifacts()

        # Then: the missing artifact is not in the dataset artifacts path
        dataset_key = parse_s3_url(
            artifacts_publisher.dataset_artifacts_path
        ).key.lstrip("/")
        assert (
            s3_client.list_objects_v2(
                Bucket=bucket, Prefix=f"{dataset_key}/missing_artifact.txt"
            )["KeyCount"]
            == 0
        )

    def test_copy_ama_artifacts(
        self, artifacts_publisher: ArtifactsPublisher, s3_client: S3Client, bucket: str
    ):
        """Test copying ama artifacts from global to dataset path."""

        # Given: a subkey in the global key under which some ama files exist
        global_key = parse_s3_url(artifacts_publisher.global_artifacts_path).key.lstrip(
            "/"
        )
        s3_client.put_object(
            Bucket=bucket, Key=f"{global_key}/ama/ama_file1.txt", Body=b"whatever"
        )
        s3_client.put_object(
            Bucket=bucket, Key=f"{global_key}/ama/ama_file2.csv", Body=b"whatever"
        )
        s3_client.put_object(
            Bucket=bucket, Key=f"{global_key}/ama/another_file.txt", Body=b"whatever"
        )

        # When: _copy_ama_artifacts is called
        artifacts_publisher._copy_ama_artifacts()

        # Then: the ama files are in the dataset artifacts path
        dataset_key = parse_s3_url(
            artifacts_publisher.dataset_artifacts_path
        ).key.lstrip("/")
        dataset_objs = s3_client.list_objects_v2(
            Bucket=bucket, Prefix=f"{dataset_key}/ama"
        )
        assert dataset_objs["KeyCount"] == 3
        expected_files = [
            "ama_file1.txt",
            "ama_file2.csv",
            "another_file.txt",
        ]
        # for every f in expected_files, exists and obj: objects['Contents'] that obj['Key'].endswith(f)
        for file in expected_files:
            assert any(
                obj["Key"].endswith(f"{dataset_key}/ama/{file}")
                for obj in dataset_objs["Contents"]
            )

    def test_copy_prophecy_artifacts(
        self, artifacts_publisher: ArtifactsPublisher, s3_client: S3Client, bucket: str
    ):
        """Test copying prophecy artifacts from global to dataset path."""

        # Given: a subkey in the global key under which some ama files exist
        global_key = parse_s3_url(artifacts_publisher.global_artifacts_path).key.lstrip(
            "/"
        )
        s3_client.put_object(
            Bucket=bucket, Key=f"{global_key}/prophecy/file1.zip", Body=b"whatever"
        )
        s3_client.put_object(
            Bucket=bucket, Key=f"{global_key}/prophecy/file2.jar", Body=b"whatever"
        )
        s3_client.put_object(
            Bucket=bucket,
            Key=f"{global_key}/prophecy/another_file.txt",
            Body=b"whatever",
        )

        # When: _copy_prophecy_artifacts is called
        artifacts_publisher._copy_prophecy_artifacts()

        # Then: the ama files are in the dataset artifacts path
        dataset_key = parse_s3_url(
            artifacts_publisher.dataset_artifacts_path
        ).key.lstrip("/")
        dataset_objs = s3_client.list_objects_v2(
            Bucket=bucket, Prefix=f"{dataset_key}/prophecy"
        )
        assert dataset_objs["KeyCount"] == 3
        expected_files = [
            "file1.zip",
            "file2.jar",
            "another_file.txt",
        ]
        # for every f in expected_files, exists and obj: objects['Contents'] that obj['Key'].endswith(f)
        for file in expected_files:
            assert any(
                obj["Key"].endswith(f"{dataset_key}/prophecy/{file}")
                for obj in dataset_objs["Contents"]
            )

    def test_copy_git_artifacts_rdc_copied(self, artifacts_publisher_factory):
        """
        Test _copy_git_artifacts copies the raw data catalog when the current branch is not the default git branch.

        """

        # Given: an ArtifactsPublisher on a feature branch
        publisher: ArtifactsPublisher = artifacts_publisher_factory(
            branch="feature-branch", git_default_branch="main", rdc="my-rdc.yml"
        )

        # And: the current git branch is not the default branch
        assert publisher.branch != publisher.git_default_branch

        # When: _copy_git_artifacts is called
        publisher.copy_artifacts_from_git_to_s3 = Mock()
        publisher._copy_git_artifacts()

        # Then: the raw data catalog is copied
        expected_rdc_source_path = os.path.join("data-catalog", "my-rdc.yml")
        expected_rdc_dest_path = os.path.join(
            publisher.dataset_artifacts_path, "my-rdc.yml"
        )
        publisher.copy_artifacts_from_git_to_s3.assert_called_once_with(
            [("my-rdc.yml", expected_rdc_source_path, expected_rdc_dest_path)]
        )

    def test_copy_git_artifacts_config_copied(self, artifacts_publisher_factory):
        """
        Test _copy_git_artifacts copies the dataset-specific config file when the necessary paths and URLs are configured.
        """

        # Given: an ArtifactsPublisher on a feature branch
        publisher: ArtifactsPublisher = artifacts_publisher_factory(
            branch="main",
            git_default_branch="main",
            rdc="my-rdc.yml",
            git_config_file_path="/config/path",
            config_file_url="s3://config/url",
            client="test-client",
            dataset="test-dataset",
            revision="20210901",
        )

        # And: git_config_file_path and config_file_url are defined
        assert publisher.git_config_file_path and publisher.config_file_url, (
            "git_config_file_path and config_file_url must be configured"
        )

        # When: _copy_git_artifacts is called
        publisher.copy_artifacts_from_git_to_s3 = Mock()
        publisher._copy_git_artifacts()

        # Then: the dataset-specific config file is copied
        expected_config_source_path = os.path.join(
            publisher.git_config_file_path,
            publisher.client,
            publisher.dataset,
            publisher.revision,
            "config.yaml",
        )
        publisher.copy_artifacts_from_git_to_s3.assert_called_once_with(
            [("config.yaml", expected_config_source_path, publisher.config_file_url)]
        )

    def test_copy_git_artifacts_nothing_is_copied(self, artifacts_publisher_factory):
        """
        Test _copy_git_artifacts does not copy anything when neither the rdc nor the config file are configured.
        """

        # Given: an ArtifactsPublisher on a feature branch
        publisher = artifacts_publisher_factory(
            branch="main",
            git_default_branch="main",
            rdc=None,
            git_config_file_path=None,
            config_file_url=None,
        )

        # And: the current branch and the default are the same
        assert publisher.branch == publisher.git_default_branch

        # And: Either git_config_file_path or config_file_url are undefined
        assert not publisher.git_config_file_path or not publisher.config_file_url

        # When: _copy_git_artifacts is called
        publisher.copy_artifacts_from_git_to_s3 = Mock()
        publisher._copy_git_artifacts()

        # Then: nothing is copied
        publisher.copy_artifacts_from_git_to_s3.assert_not_called()

    @patch("dags.operators.artifacts_publisher.GitHook")
    def test_copy_artifacts_from_git_to_s3_success(
        self, mock_git_hook_class, artifacts_publisher, tmp_path
    ):
        """Test successful copying of artifacts from git repository to S3."""

        # Given: a publisher with a mocked filesystem and a mocked git hook
        mock_git_hook = Mock()
        mock_git_hook_class.return_value.__enter__.return_value = mock_git_hook
        publisher = artifacts_publisher
        publisher._fs = Mock(spec=AetionS3FileSystem)

        # And: a list of artifacts to copy
        artifact_name = "config.yaml"
        artifact_relative_path = "config/path/config.yaml"
        artifact_copy_info = [
            (artifact_name, "config/path/config.yaml", "s3://bucket/config.yaml")
        ]

        # And: a local folder for the cloned git repo exists
        local_git_repo = tmp_path
        artifact_local_path = local_git_repo / artifact_relative_path
        artifact_local_path.parent.mkdir(parents=True, exist_ok=True)
        artifact_local_path.touch()

        # When: copy_artifacts_from_git_to_s3 is called withthe list of artifacts to copy
        with patch(
            "dags.operators.artifacts_publisher.tempfile.TemporaryDirectory"
        ) as mock_temp_dir:
            mock_temp_dir.return_value.__enter__.return_value = str(local_git_repo)
            publisher.copy_artifacts_from_git_to_s3(artifact_copy_info)

        # Then: a git clone is performed and the artifact is copied to S3
        mock_git_hook.clone.assert_called_once_with(
            str(local_git_repo), publisher.branch
        )

        # And: the artifact (config/path/config.yaml) is uploaded to S3 (s3://bucket/config.yaml)
        publisher.fs.put.assert_called_once_with(
            str(artifact_local_path), "s3://bucket/config.yaml"
        )

    @patch("dags.operators.artifacts_publisher.GitHook")
    def test_copy_artifacts_from_git_to_s3_missing_artifact(
        self, mock_git_hook_class, artifacts_publisher, tmp_path
    ):
        """Test error handling when artifact is missing in git repository."""
        # Given: a publisher with a mocked filesystem and a mocked git hook
        mock_git_hook = Mock()
        mock_git_hook_class.return_value.__enter__.return_value = mock_git_hook
        publisher = artifacts_publisher
        publisher._fs = Mock(spec=AetionS3FileSystem)

        # And: a list of artifacts to copy, one of which is missing
        artifact_name = "missing_config.yaml"
        artifact_relative_path = "missing/path/config.yaml"
        put_path = "s3://bucket/config.yaml"
        artifact_copy_info = [(artifact_name, artifact_relative_path, put_path)]

        local_git_repo = tmp_path

        # When: copy_artifacts_from_git_to_s3 is called with the list of artifacts to copy
        with patch(
            "dags.operators.artifacts_publisher.tempfile.TemporaryDirectory"
        ) as mock_temp_dir:
            mock_temp_dir.return_value.__enter__.return_value = str(local_git_repo)

            with pytest.raises(AirflowException) as exc_info:
                publisher.copy_artifacts_from_git_to_s3(artifact_copy_info)

        # Then: an exception is raised indicating the missing artifact
        assert f"Cannot find {artifact_name}" in str(exc_info.value)

        # And: the git clone was successful
        mock_git_hook.clone.assert_called_once_with(
            str(local_git_repo), publisher.branch
        )

        # And: the artifact was not uploaded to S3
        publisher.fs.put.assert_not_called()

    def test_check_git_version(
        self,
        artifacts_publisher: ArtifactsPublisher,
        s3_client: S3Client,
        bucket: str,
        tmp_path: Path,
        caplog: pytest.LogCaptureFixture,
    ):
        """Test checking git version of artifacts."""

        # Given: some zip-like files in the dataset artifacts path
        artifacts_publisher.artifacts = {
            "artifact1.zip": True,
            "artifact2.jar": True,
        }

        # And: the zip files contain a GIT_VERSION file
        dataset_key = parse_s3_url(
            artifacts_publisher.dataset_artifacts_path
        ).key.lstrip("/")
        for artifact in artifacts_publisher.artifacts.keys():
            s3_client.put_object(
                Bucket=bucket,
                Key=f"{dataset_key}/{artifact}",
                Body=_generate_fake_zip_content(),
            )

        # And: these zip-like files are in the dataset artifacts path
        assert (
            s3_client.list_objects_v2(
                Bucket=bucket, Prefix=f"{dataset_key}/artifact1.zip"
            )["KeyCount"]
            == 1
        )
        assert (
            s3_client.list_objects_v2(
                Bucket=bucket, Prefix=f"{dataset_key}/artifact2.jar"
            )["KeyCount"]
            == 1
        )

        # When: _check_git_version is called
        with caplog.at_level(logging.INFO):
            artifacts_publisher._check_git_version()

        # Then: the git version is logged for all zip-like files
        assert "is not a zip-like file" not in caplog.text
        assert "artifact1.zip git version: v1.2.3" in caplog.text
        assert "artifact2.jar git version: v1.2.3" in caplog.text


    def test_check_git_version_ignore_non_zip_like_files(self, artifacts_publisher: ArtifactsPublisher,
                                                         s3_client: S3Client, bucket: str, tmp_path: Path,
                                                         caplog: pytest.LogCaptureFixture):
        """Test checking git version of artifacts ignores non-zip-like files."""

        # Given: some non-zip-like files in the dataset artifacts path
        artifacts_publisher.artifacts = {
            "artifact1.txt": True,
            "artifact2.csv": True,
        }
        dataset_key = parse_s3_url(
            artifacts_publisher.dataset_artifacts_path
        ).key.lstrip("/")
        for artifact in artifacts_publisher.artifacts.keys():
            s3_client.put_object(
                Bucket=bucket,
                Key=f"{dataset_key}/{artifact}",
                Body=b"whatever",
            )

        # When: _check_git_version is called
        with caplog.at_level(logging.INFO):
            artifacts_publisher._check_git_version()

        # Then: the git version is logged for all zip-like files
        assert "artifact1.txt is not a zip-like file" in caplog.text
        assert "artifact2.csv is not a zip-like file" in caplog.text

    def test_check_git_version_missing_git_version_file(
        self,
        artifacts_publisher: ArtifactsPublisher,
        s3_client: S3Client,
        bucket: str,
        tmp_path: Path,
        caplog: pytest.LogCaptureFixture,
    ):
        """Test checking git version of artifacts when GIT_VERSION is missing in some zip-like file."""

        # Given: some zip-like files in the dataset artifacts path
        artifacts_publisher.artifacts = {
            "artifact1.zip": True,
            "artifact2.jar": True,
        }
        dataset_key = parse_s3_url(
            artifacts_publisher.dataset_artifacts_path
        ).key.lstrip("/")

        # And: the artifact1.zip contains a GIT_VERSION file
        s3_client.put_object(
            Bucket=bucket,
            Key=f"{dataset_key}/artifact1.zip",
            Body=_generate_fake_zip_content(),
        )

        # And: the artifact2.jar does not contain a GIT_VERSION file
        s3_client.put_object(
            Bucket=bucket,
            Key=f"{dataset_key}/artifact2.jar",
            Body=_generate_fake_zip_content(version=None),
        )

        # When: _check_git_version is called
        with caplog.at_level(logging.INFO):
            artifacts_publisher._check_git_version()
            expected_re = r"Failed to get version from .*/artifact2.jar: \"There is no item named 'GIT_VERSION' in the archive\""
            assert any(
                re.search(expected_re, record.getMessage()) for record in caplog.records
            )

    def _mock_csv_file_content(self, publisher, csv_content: str):
        """Helper method to mock CSV file content for filesystem operations."""
        mock_fs = Mock()
        mock_file = StringIO(csv_content)
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__.return_value = mock_file
        mock_context_manager.__exit__.return_value = None
        mock_fs.open.return_value = mock_context_manager
        publisher._fs = mock_fs
        return mock_fs

    def _mock_filesystem_error(self, publisher, error_exception):
        """Helper method to mock filesystem errors."""
        mock_fs = Mock()
        mock_fs.open.side_effect = error_exception
        publisher._fs = mock_fs
        return mock_fs

    def test_load_ndc_mapping_file_name_success(self, artifacts_publisher_factory):
        """
        Test successful loading of NDC mapping file name from coding systems CSV.

        Verifies that the method correctly parses a valid coding systems CSV file,
        locates the NDC_FDB_2014 entry, and returns the associated data resource path.
        """
        # Given: An ArtifactsPublisher instance with mocked filesystem
        publisher = artifacts_publisher_factory()

        # And: A valid coding systems CSV with NDC_FDB_2014 entry
        csv_content = """name,type,csResource,csInstanceResource,csInstanceDataResource,caseSensitive,codeProcessor
MARKETSCAN_THERGRP,marketscan,versioned/coding/marketscan/therGrp/base/marketscan_therGrp.json,versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.json,versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.csv.gz,false,
NDC_FDB_2014,common,versioned/coding/common/ndc_fdb/base/ndc_fdb.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz,false,
NDC_FDB_AHFS,common,versioned/coding/common/ndc_fdb_ahfs/base/ndc_fdb_ahfs.json,versioned/coding/common/ndc_fdb_ahfs/202309/ndc_fdb_ahfs_202309.json,versioned/coding/common/ndc_fdb_ahfs/202309/ndc_fdb_ahfs_202309.csv.gz,false,"""

        # When: The load_ndc_mapping_file_name method is called with mocked file content
        mock_fs = self._mock_csv_file_content(publisher, csv_content)
        result = publisher.load_ndc_mapping_file_name("s3://bucket/coding_systems.csv")

        # Then: The method should return the correct NDC mapping file path
        assert result == "versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"

        # And: The filesystem open method should be called with correct parameters
        mock_fs.open.assert_called_once_with("s3://bucket/coding_systems.csv", "r")

    def test_load_ndc_mapping_file_name_missing_ndc_entry(
        self, artifacts_publisher_factory
    ):
        """
        Test error handling when NDC_FDB_2014 entry is missing from coding systems CSV.

        Verifies that the method raises an appropriate exception when the required
        NDC coding system entry is not found in the CSV file.
        """
        # Given: An ArtifactsPublisher instance with mocked filesystem
        publisher = artifacts_publisher_factory()

        # And: A coding systems CSV without NDC_FDB_2014 entry
        csv_content = """name,type,csResource,csInstanceResource,csInstanceDataResource,caseSensitive,codeProcessor
MARKETSCAN_THERGRP,marketscan,versioned/coding/marketscan/therGrp/base/marketscan_therGrp.json,versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.json,versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.csv.gz,false,
NDC_FDB_AHFS,common,versioned/coding/common/ndc_fdb_ahfs/base/ndc_fdb_ahfs.json,versioned/coding/common/ndc_fdb_ahfs/202309/ndc_fdb_ahfs_202309.json,versioned/coding/common/ndc_fdb_ahfs/202309/ndc_fdb_ahfs_202309.csv.gz,false,"""

        # When: The load_ndc_mapping_file_name method is called with missing NDC entry
        # Then: An exception should be raised with the expected error message
        self._mock_csv_file_content(publisher, csv_content)
        with pytest.raises(Exception) as exc_info:
            publisher.load_ndc_mapping_file_name("s3://bucket/coding_systems.csv")

        # And: The exception message should indicate the missing coding system
        assert str(exc_info.value) == "coding system for NDC_FDB_2014 not found"

    def test_load_ndc_mapping_file_name_empty_csv(self, artifacts_publisher_factory):
        """
        Test error handling when coding systems CSV contains only headers.

        Verifies that the method raises an appropriate exception when the CSV file
        contains only the header row with no data entries.
        """
        # Given: An ArtifactsPublisher instance with mocked filesystem
        publisher = artifacts_publisher_factory()

        # And: A coding systems CSV with only headers
        csv_content = """name,type,csResource,csInstanceResource,csInstanceDataResource,caseSensitive,codeProcessor"""

        # When: The load_ndc_mapping_file_name method is called with empty CSV
        # Then: An exception should be raised indicating missing coding system
        self._mock_csv_file_content(publisher, csv_content)
        with pytest.raises(Exception) as exc_info:
            publisher.load_ndc_mapping_file_name("s3://bucket/coding_systems.csv")

        # And: The exception message should indicate the missing coding system
        assert str(exc_info.value) == "coding system for NDC_FDB_2014 not found"

    def test_load_ndc_mapping_file_name_malformed_csv_insufficient_columns(
        self, artifacts_publisher_factory
    ):
        """
        Test error handling when CSV rows have insufficient columns.

        Verifies that the method handles CSV parsing errors gracefully when
        data rows don't match the expected namedtuple structure.
        """
        # Given: An ArtifactsPublisher instance with mocked filesystem
        publisher = artifacts_publisher_factory()

        # And: A malformed CSV with insufficient columns in data rows
        csv_content = """name,type,csResource,csInstanceResource,csInstanceDataResource,caseSensitive,codeProcessor
MARKETSCAN_THERGRP,marketscan,versioned/coding/marketscan/therGrp/base/marketscan_therGrp.json
NDC_FDB_2014,common,versioned/coding/common/ndc_fdb/base/ndc_fdb.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.json"""

        # When: The load_ndc_mapping_file_name method is called with malformed CSV
        # Then: A TypeError should be raised due to insufficient arguments for namedtuple
        self._mock_csv_file_content(publisher, csv_content)
        with pytest.raises(TypeError):
            publisher.load_ndc_mapping_file_name("s3://bucket/coding_systems.csv")

    def test_load_ndc_mapping_file_name_malformed_csv_extra_columns(
        self, artifacts_publisher_factory
    ):
        """
        Test error handling when CSV rows have too many columns.

        Verifies that the method handles CSV parsing errors gracefully when
        data rows have more columns than expected by the namedtuple structure.
        """
        # Given: An ArtifactsPublisher instance with mocked filesystem
        publisher = artifacts_publisher_factory()

        # And: A malformed CSV with extra columns in data rows
        csv_content = """name,type,csResource,csInstanceResource,csInstanceDataResource,caseSensitive,codeProcessor
MARKETSCAN_THERGRP,marketscan,versioned/coding/marketscan/therGrp/base/marketscan_therGrp.json,versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.json,versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.csv.gz,false,,extra_column
NDC_FDB_2014,common,versioned/coding/common/ndc_fdb/base/ndc_fdb.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz,false,,extra_column"""

        # When: The load_ndc_mapping_file_name method is called with malformed CSV
        # Then: A TypeError should be raised due to too many arguments for namedtuple
        self._mock_csv_file_content(publisher, csv_content)
        with pytest.raises(TypeError):
            publisher.load_ndc_mapping_file_name("s3://bucket/coding_systems.csv")

    def test_load_ndc_mapping_file_name_file_not_found(
        self, artifacts_publisher_factory
    ):
        """
        Test error handling when the coding systems CSV file does not exist.

        Verifies that the method properly propagates filesystem errors when
        the specified file path cannot be found or accessed.
        """
        # Given: An ArtifactsPublisher instance with mocked filesystem
        publisher = artifacts_publisher_factory()

        # When: The load_ndc_mapping_file_name method is called with non-existent file
        # Then: A FileNotFoundError should be raised
        self._mock_filesystem_error(publisher, FileNotFoundError("File not found"))
        with pytest.raises(FileNotFoundError):
            publisher.load_ndc_mapping_file_name("s3://bucket/nonexistent.csv")

    def test_load_ndc_mapping_file_name_permission_error(
        self, artifacts_publisher_factory
    ):
        """
        Test error handling when access to the coding systems CSV file is denied.

        Verifies that the method properly propagates permission errors when
        the file exists but cannot be accessed due to insufficient permissions.
        """
        # Given: An ArtifactsPublisher instance with mocked filesystem
        publisher = artifacts_publisher_factory()

        # When: The load_ndc_mapping_file_name method is called with access-denied file
        # Then: A PermissionError should be raised
        self._mock_filesystem_error(publisher, PermissionError("Access denied"))
        with pytest.raises(PermissionError):
            publisher.load_ndc_mapping_file_name("s3://bucket/restricted.csv")

    def test_load_ndc_mapping_file_name_multiple_ndc_entries(
        self, artifacts_publisher_factory
    ):
        """
        Test behavior when CSV contains multiple NDC_FDB_2014 entries.

        Verifies that the method returns the data resource path from the first
        matching NDC_FDB_2014 entry when multiple entries exist in the CSV.
        """
        # Given: An ArtifactsPublisher instance with mocked filesystem
        publisher = artifacts_publisher_factory()

        # And: A coding systems CSV with multiple NDC_FDB_2014 entries
        csv_content = """name,type,csResource,csInstanceResource,csInstanceDataResource,caseSensitive,codeProcessor
MARKETSCAN_THERGRP,marketscan,versioned/coding/marketscan/therGrp/base/marketscan_therGrp.json,versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.json,versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.csv.gz,false,
NDC_FDB_2014,common,versioned/coding/common/ndc_fdb/base/ndc_fdb.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz,false,
NDC_FDB_2014,common,versioned/coding/common/ndc_fdb/base/ndc_fdb.json,versioned/coding/common/ndc_fdb/202310/ndc_fdb_202310.json,versioned/coding/common/ndc_fdb/202310/ndc_fdb_202310.csv.gz,false,"""

        # When: The load_ndc_mapping_file_name method is called with multiple NDC entries
        self._mock_csv_file_content(publisher, csv_content)
        result = publisher.load_ndc_mapping_file_name("s3://bucket/coding_systems.csv")

        # Then: The method should return the data resource path from the first matching entry
        assert result == "versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"

    def test_load_ndc_mapping_file_name_case_sensitive_match(
        self, artifacts_publisher_factory
    ):
        """
        Test that NDC coding system name matching is case-sensitive.

        Verifies that the method only matches entries with exact case for the
        NDC_FDB_2014 coding system name, ignoring case variations.
        """
        # Given: An ArtifactsPublisher instance with mocked filesystem
        publisher = artifacts_publisher_factory()

        # And: A coding systems CSV with case variations of NDC_FDB_2014
        csv_content = """name,type,csResource,csInstanceResource,csInstanceDataResource,caseSensitive,codeProcessor
ndc_fdb_2014,common,versioned/coding/common/ndc_fdb/base/ndc_fdb.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz,false,
NDC_fdb_2014,common,versioned/coding/common/ndc_fdb/base/ndc_fdb.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz,false,
Ndc_Fdb_2014,common,versioned/coding/common/ndc_fdb/base/ndc_fdb.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz,false,"""

        # When: The load_ndc_mapping_file_name method is called with case variations
        # Then: An exception should be raised since exact case match is not found
        self._mock_csv_file_content(publisher, csv_content)
        with pytest.raises(Exception) as exc_info:
            publisher.load_ndc_mapping_file_name("s3://bucket/coding_systems.csv")

        # And: The exception message should indicate the missing coding system
        assert str(exc_info.value) == "coding system for NDC_FDB_2014 not found"

    def test_load_ndc_mapping_file_name_whitespace_in_data(
        self, artifacts_publisher_factory
    ):
        """
        Test handling of CSV data with extra whitespace in fields.

        Verifies that the method correctly processes CSV entries that contain
        leading or trailing whitespace in the name field and other columns.
        """
        # Given: An ArtifactsPublisher instance with mocked filesystem
        publisher = artifacts_publisher_factory()

        # And: A coding systems CSV with whitespace in the NDC entry name
        csv_content = """name,type,csResource,csInstanceResource,csInstanceDataResource,caseSensitive,codeProcessor
 MARKETSCAN_THERGRP ,marketscan,versioned/coding/marketscan/therGrp/base/marketscan_therGrp.json,versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.json,versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.csv.gz,false,
 NDC_FDB_2014 ,common, versioned/coding/common/ndc_fdb/base/ndc_fdb.json , versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.json , versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz ,false,"""

        # When: The load_ndc_mapping_file_name method is called with whitespace in data
        # Then: An exception should be raised since whitespace prevents exact match
        self._mock_csv_file_content(publisher, csv_content)
        with pytest.raises(Exception) as exc_info:
            publisher.load_ndc_mapping_file_name("s3://bucket/coding_systems.csv")

        # And: The exception message should indicate the missing coding system
        assert str(exc_info.value) == "coding system for NDC_FDB_2014 not found"

    def test_load_ndc_mapping_file_name_exact_whitespace_match(
        self, artifacts_publisher_factory
    ):
        """
        Test successful matching when NDC entry has exact name without whitespace.

        Verifies that the method successfully finds and returns the data resource path
        when the NDC_FDB_2014 entry has the exact name match without extra whitespace.
        """
        # Given: An ArtifactsPublisher instance with mocked filesystem
        publisher = artifacts_publisher_factory()

        # And: A coding systems CSV with exact NDC_FDB_2014 name (no whitespace)
        csv_content = """name,type,csResource,csInstanceResource,csInstanceDataResource,caseSensitive,codeProcessor
MARKETSCAN_THERGRP,marketscan,versioned/coding/marketscan/therGrp/base/marketscan_therGrp.json,versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.json,versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.csv.gz,false,
NDC_FDB_2014,common,versioned/coding/common/ndc_fdb/base/ndc_fdb.json,versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.json, versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz ,false,"""

        # When: The load_ndc_mapping_file_name method is called with exact name match
        self._mock_csv_file_content(publisher, csv_content)
        result = publisher.load_ndc_mapping_file_name("s3://bucket/coding_systems.csv")

        # Then: The method should return the data resource path (with potential whitespace preserved)
        assert (
            result == " versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz "
        )

    def test_load_ndc_mapping_file_name_csv_parsing_with_quotes(
        self, artifacts_publisher_factory
    ):
        """
        Test CSV parsing when fields contain quoted values with commas.

        Verifies that the method correctly handles CSV entries where fields
        are quoted and may contain commas or other special characters.
        """
        # Given: An ArtifactsPublisher instance with mocked filesystem
        publisher = artifacts_publisher_factory()

        # And: A coding systems CSV with quoted fields containing commas
        csv_content = """name,type,csResource,csInstanceResource,csInstanceDataResource,caseSensitive,codeProcessor
"MARKETSCAN_THERGRP,SPECIAL",marketscan,"versioned/coding/marketscan/therGrp/base/marketscan_therGrp.json","versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.json","versioned/coding/marketscan/therGrp/201809/marketscan_therGrp_201809.csv.gz",false,
NDC_FDB_2014,common,"versioned/coding/common/ndc_fdb/base/ndc_fdb.json","versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.json","versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz",false,"""

        # When: The load_ndc_mapping_file_name method is called with quoted CSV fields
        self._mock_csv_file_content(publisher, csv_content)
        result = publisher.load_ndc_mapping_file_name("s3://bucket/coding_systems.csv")

        # Then: The method should return the correct data resource path
        assert result == "versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"

    def test_download_ndc_lookup_file_success(self, artifacts_publisher_factory):
        """
        Test successful NDC lookup file download from git metadata repository.

        Verifies that the method correctly downloads the NDC mapping file when
        git_meta_repo is configured, including proper git operations and S3 upload.
        """
        # Given: An ArtifactsPublisher instance configured with git metadata repository
        publisher = artifacts_publisher_factory(
            git_meta_repo="test-meta-repo",
            dataset_artifacts_path="s3://bucket/dataset/artifacts",
        )

        # And: Mock the load_ndc_mapping_file_name method to return a test file path
        test_ndc_file_path = (
            "versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
        )
        publisher.load_ndc_mapping_file_name = Mock(return_value=test_ndc_file_path)

        # And: Mock the filesystem operations
        mock_fs = Mock()
        publisher._fs = mock_fs

        # And: Mock GitHook and temporary directory operations
        with (
            patch("tempfile.TemporaryDirectory") as mock_temp_dir,
            patch("dags.operators.artifacts_publisher.GitHook") as mock_git_hook_class,
            patch("os.path.exists") as mock_exists,
        ):
            # Setup temporary directory mock
            temp_dir_path = "/tmp/test_git_clone"
            mock_temp_context = MagicMock()
            mock_temp_context.__enter__.return_value = temp_dir_path
            mock_temp_context.__exit__.return_value = None
            mock_temp_dir.return_value = mock_temp_context

            # Setup GitHook mock
            mock_git_instance = MagicMock()
            mock_git_context = MagicMock()
            mock_git_context.__enter__.return_value = mock_git_instance
            mock_git_context.__exit__.return_value = None
            mock_git_hook_class.return_value = mock_git_context

            # Setup file existence check
            mock_exists.return_value = True

            # When: The download_ndc_lookup_file method is called
            publisher.download_ndc_lookup_file()

        # Then: The load_ndc_mapping_file_name method should be called with correct path
        expected_coding_systems_path = (
            "s3://bucket/dataset/artifacts/coding_systems.csv"
        )
        publisher.load_ndc_mapping_file_name.assert_called_once_with(
            expected_coding_systems_path
        )

        # And: GitHook should be instantiated with correct parameters
        mock_git_hook_class.assert_called_once_with(
            "test-meta-repo", "test-private-key"
        )

        # And: Git clone should be called with correct parameters
        mock_git_instance.clone.assert_called_once_with(
            target_dir=temp_dir_path,
            branch="main",
            sparse_folders=["metaDataStore/versioned/coding/common/ndc_fdb/202309"],
        )

        # And: File existence should be checked for the artifact
        expected_artifact_dir = f"{temp_dir_path}/metaDataStore/versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
        mock_exists.assert_called_once_with(expected_artifact_dir)

        # And: The file should be uploaded to S3
        expected_dest_path = "s3://bucket/dataset/artifacts/ndc_fdb.csv.gz"
        mock_fs.put.assert_called_once_with(expected_artifact_dir, expected_dest_path)

    def test_download_ndc_lookup_file_skipped_when_no_git_meta_repo(
        self, artifacts_publisher_factory
    ):
        """
        Test that NDC lookup file download is skipped when git_meta_repo is not configured.

        Verifies that the method logs an appropriate message and performs no operations
        when git_meta_repo is None, avoiding unnecessary git and filesystem operations.
        """
        # Given: An ArtifactsPublisher instance without git metadata repository configured
        publisher = artifacts_publisher_factory(git_meta_repo=None)

        # And: Mock the load_ndc_mapping_file_name method (should not be called)
        publisher.load_ndc_mapping_file_name = Mock()

        # And: Mock the filesystem operations (should not be called)
        mock_fs = Mock()
        publisher._fs = mock_fs

        # When: The download_ndc_lookup_file method is called
        with patch("dags.operators.artifacts_publisher.logger") as mock_logger:
            publisher.download_ndc_lookup_file()

        # Then: An informational log message should be recorded
        mock_logger.info.assert_called_with(
            "skipping ndc artifacts (metadatastore repo is not configured) ..."
        )

        # And: No git or filesystem operations should be performed
        publisher.load_ndc_mapping_file_name.assert_not_called()
        mock_fs.put.assert_not_called()

    def test_download_ndc_lookup_file_git_clone_failure(
        self, artifacts_publisher_factory
    ):
        """
        Test error handling when git clone operation fails.

        Verifies that the method properly propagates git-related exceptions when
        the GitHook clone operation encounters errors such as network issues or
        invalid repository configurations.
        """
        # Given: An ArtifactsPublisher instance configured with git metadata repository
        publisher = artifacts_publisher_factory(
            git_meta_repo="invalid-repo",
            dataset_artifacts_path="s3://bucket/dataset/artifacts",
        )

        # And: Mock the load_ndc_mapping_file_name method
        test_ndc_file_path = (
            "versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
        )
        publisher.load_ndc_mapping_file_name = Mock(return_value=test_ndc_file_path)

        # And: Mock GitHook to raise an exception during clone
        with (
            patch("tempfile.TemporaryDirectory") as mock_temp_dir,
            patch("dags.operators.artifacts_publisher.GitHook") as mock_git_hook_class,
        ):
            # Setup temporary directory mock
            temp_dir_path = "/tmp/test_git_clone"
            mock_temp_context = MagicMock()
            mock_temp_context.__enter__.return_value = temp_dir_path
            mock_temp_context.__exit__.return_value = None
            mock_temp_dir.return_value = mock_temp_context

            # Setup GitHook to raise exception during clone
            mock_git_instance = MagicMock()
            mock_git_instance.clone.side_effect = Exception("Git clone failed")
            mock_git_context = MagicMock()
            mock_git_context.__enter__.return_value = mock_git_instance
            mock_git_context.__exit__.return_value = None
            mock_git_hook_class.return_value = mock_git_context

            # When: The download_ndc_lookup_file method is called
            # Then: The git exception should be propagated
            with pytest.raises(Exception, match="Git clone failed"):
                publisher.download_ndc_lookup_file()

    def test_download_ndc_lookup_file_artifact_not_found(
        self, artifacts_publisher_factory
    ):
        """
        Test error handling when the NDC artifact file is not found in the cloned repository.

        Verifies that the method raises an AirflowException with appropriate error message
        when the expected artifact file does not exist in the cloned git repository.
        """
        # Given: An ArtifactsPublisher instance configured with git metadata repository
        publisher = artifacts_publisher_factory(
            git_meta_repo="test-meta-repo",
            dataset_artifacts_path="s3://bucket/dataset/artifacts",
        )

        # And: Mock the load_ndc_mapping_file_name method
        test_ndc_file_path = (
            "versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
        )
        publisher.load_ndc_mapping_file_name = Mock(return_value=test_ndc_file_path)

        # And: Mock GitHook and temporary directory operations
        with (
            patch("tempfile.TemporaryDirectory") as mock_temp_dir,
            patch("dags.operators.artifacts_publisher.GitHook") as mock_git_hook_class,
            patch("os.path.exists") as mock_exists,
        ):
            # Setup temporary directory mock
            temp_dir_path = "/tmp/test_git_clone"
            mock_temp_context = MagicMock()
            mock_temp_context.__enter__.return_value = temp_dir_path
            mock_temp_context.__exit__.return_value = None
            mock_temp_dir.return_value = mock_temp_context

            # Setup GitHook mock (successful clone)
            mock_git_instance = MagicMock()
            mock_git_context = MagicMock()
            mock_git_context.__enter__.return_value = mock_git_instance
            mock_git_context.__exit__.return_value = None
            mock_git_hook_class.return_value = mock_git_context

            # Setup file existence check to return False (artifact not found)
            mock_exists.return_value = False

            # When: The download_ndc_lookup_file method is called
            # Then: An AirflowException should be raised with appropriate message
            with pytest.raises(AirflowException) as exc_info:
                publisher.download_ndc_lookup_file()

            # And: The exception message should indicate the missing artifact
            expected_artifact_dir = f"{temp_dir_path}/metaDataStore/versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
            assert f"Cannot find {expected_artifact_dir}" in str(exc_info.value)

    def test_download_ndc_lookup_file_filesystem_upload_error(
        self, artifacts_publisher_factory
    ):
        """
        Test error handling when S3 filesystem upload operation fails.

        Verifies that the method properly propagates filesystem errors when
        the S3 put operation encounters issues such as permission errors or
        network connectivity problems.
        """
        # Given: An ArtifactsPublisher instance configured with git metadata repository
        publisher = artifacts_publisher_factory(
            git_meta_repo="test-meta-repo",
            dataset_artifacts_path="s3://bucket/dataset/artifacts",
        )

        # And: Mock the load_ndc_mapping_file_name method
        test_ndc_file_path = (
            "versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
        )
        publisher.load_ndc_mapping_file_name = Mock(return_value=test_ndc_file_path)

        # And: Mock the filesystem to raise an exception during put operation
        mock_fs = Mock()
        mock_fs.put.side_effect = Exception("S3 upload failed")
        publisher._fs = mock_fs

        # And: Mock GitHook and temporary directory operations
        with (
            patch("tempfile.TemporaryDirectory") as mock_temp_dir,
            patch("dags.operators.artifacts_publisher.GitHook") as mock_git_hook_class,
            patch("os.path.exists") as mock_exists,
        ):
            # Setup successful git operations and file existence
            temp_dir_path = "/tmp/test_git_clone"
            mock_temp_context = MagicMock()
            mock_temp_context.__enter__.return_value = temp_dir_path
            mock_temp_context.__exit__.return_value = None
            mock_temp_dir.return_value = mock_temp_context

            mock_git_instance = MagicMock()
            mock_git_context = MagicMock()
            mock_git_context.__enter__.return_value = mock_git_instance
            mock_git_context.__exit__.return_value = None
            mock_git_hook_class.return_value = mock_git_context

            mock_exists.return_value = True

            # When: The download_ndc_lookup_file method is called
            # Then: The filesystem exception should be propagated
            with pytest.raises(Exception, match="S3 upload failed"):
                publisher.download_ndc_lookup_file()

    def test_download_ndc_lookup_file_load_ndc_mapping_error(
        self, artifacts_publisher_factory
    ):
        """
        Test error handling when load_ndc_mapping_file_name method fails.

        Verifies that the method properly propagates exceptions from the
        load_ndc_mapping_file_name method, such as when the coding systems
        CSV file cannot be read or parsed.
        """
        # Given: An ArtifactsPublisher instance configured with git metadata repository
        publisher = artifacts_publisher_factory(
            git_meta_repo="test-meta-repo",
            dataset_artifacts_path="s3://bucket/dataset/artifacts",
        )

        # And: Mock the load_ndc_mapping_file_name method to raise an exception
        publisher.load_ndc_mapping_file_name = Mock(
            side_effect=Exception("coding system for NDC_FDB_2014 not found")
        )

        # When: The download_ndc_lookup_file method is called
        # Then: The exception from load_ndc_mapping_file_name should be propagated
        with pytest.raises(Exception, match="coding system for NDC_FDB_2014 not found"):
            publisher.download_ndc_lookup_file()

        # And: The load_ndc_mapping_file_name method should have been called
        expected_coding_systems_path = (
            "s3://bucket/dataset/artifacts/coding_systems.csv"
        )
        publisher.load_ndc_mapping_file_name.assert_called_once_with(
            expected_coding_systems_path
        )

    def test_download_ndc_lookup_file_path_with_metadata_store_prefix(
        self, artifacts_publisher_factory
    ):
        """
        Test that artifact paths are correctly prefixed with metaDataStore when needed.

        Verifies that the method correctly handles artifact paths that don't already
        start with 'metaDataStore' by adding the prefix for proper git sparse checkout.
        """
        # Given: An ArtifactsPublisher instance configured with git metadata repository
        publisher = artifacts_publisher_factory(
            git_meta_repo="test-meta-repo",
            dataset_artifacts_path="s3://bucket/dataset/artifacts",
        )

        # And: Mock the load_ndc_mapping_file_name method to return path without metaDataStore prefix
        test_ndc_file_path = (
            "versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
        )
        publisher.load_ndc_mapping_file_name = Mock(return_value=test_ndc_file_path)

        # And: Mock the filesystem operations
        mock_fs = Mock()
        publisher._fs = mock_fs

        # And: Mock GitHook and temporary directory operations
        with (
            patch("tempfile.TemporaryDirectory") as mock_temp_dir,
            patch("dags.operators.artifacts_publisher.GitHook") as mock_git_hook_class,
            patch("os.path.exists") as mock_exists,
        ):
            # Setup mocks for successful operation
            temp_dir_path = "/tmp/test_git_clone"
            mock_temp_context = MagicMock()
            mock_temp_context.__enter__.return_value = temp_dir_path
            mock_temp_context.__exit__.return_value = None
            mock_temp_dir.return_value = mock_temp_context

            mock_git_instance = MagicMock()
            mock_git_context = MagicMock()
            mock_git_context.__enter__.return_value = mock_git_instance
            mock_git_context.__exit__.return_value = None
            mock_git_hook_class.return_value = mock_git_context

            mock_exists.return_value = True

            # When: The download_ndc_lookup_file method is called
            publisher.download_ndc_lookup_file()

        # Then: Git clone should be called with metaDataStore prefix added to the path
        expected_sparse_folder = "metaDataStore/versioned/coding/common/ndc_fdb/202309"
        mock_git_instance.clone.assert_called_once_with(
            target_dir=temp_dir_path,
            branch="main",
            sparse_folders=[expected_sparse_folder],
        )

    def test_download_ndc_lookup_file_path_already_has_metadata_store_prefix(
        self, artifacts_publisher_factory
    ):
        """
        Test that artifact paths already starting with metaDataStore are not double-prefixed.

        Verifies that the method correctly handles artifact paths that already start
        with 'metaDataStore' by not adding an additional prefix.
        """
        # Given: An ArtifactsPublisher instance configured with git metadata repository
        publisher = artifacts_publisher_factory(
            git_meta_repo="test-meta-repo",
            dataset_artifacts_path="s3://bucket/dataset/artifacts",
        )

        # And: Mock the load_ndc_mapping_file_name method to return path with metaDataStore prefix
        test_ndc_file_path = (
            "metaDataStore/versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
        )
        publisher.load_ndc_mapping_file_name = Mock(return_value=test_ndc_file_path)

        # And: Mock the filesystem operations
        mock_fs = Mock()
        publisher._fs = mock_fs

        # And: Mock GitHook and temporary directory operations
        with (
            patch("tempfile.TemporaryDirectory") as mock_temp_dir,
            patch("dags.operators.artifacts_publisher.GitHook") as mock_git_hook_class,
            patch("os.path.exists") as mock_exists,
        ):
            # Setup mocks for successful operation
            temp_dir_path = "/tmp/test_git_clone"
            mock_temp_context = MagicMock()
            mock_temp_context.__enter__.return_value = temp_dir_path
            mock_temp_context.__exit__.return_value = None
            mock_temp_dir.return_value = mock_temp_context

            mock_git_instance = MagicMock()
            mock_git_context = MagicMock()
            mock_git_context.__enter__.return_value = mock_git_instance
            mock_git_context.__exit__.return_value = None
            mock_git_hook_class.return_value = mock_git_context

            mock_exists.return_value = True

            # When: The download_ndc_lookup_file method is called
            publisher.download_ndc_lookup_file()

        # Then: Git clone should be called with the original path (no double prefix)
        expected_sparse_folder = "metaDataStore/versioned/coding/common/ndc_fdb/202309"
        mock_git_instance.clone.assert_called_once_with(
            target_dir=temp_dir_path,
            branch="main",
            sparse_folders=[expected_sparse_folder],
        )

    def test_download_ndc_lookup_file_custom_git_branch(
        self, artifacts_publisher_factory
    ):
        """
        Test that the method uses the correct git branch for cloning operations.

        Verifies that the method respects the git_default_branch configuration
        when performing git clone operations for the metadata repository.
        """
        # Given: An ArtifactsPublisher instance with custom git default branch
        publisher = artifacts_publisher_factory(
            git_meta_repo="test-meta-repo",
            git_default_branch="develop",
            dataset_artifacts_path="s3://bucket/dataset/artifacts",
        )

        # And: Mock the load_ndc_mapping_file_name method
        test_ndc_file_path = (
            "versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
        )
        publisher.load_ndc_mapping_file_name = Mock(return_value=test_ndc_file_path)

        # And: Mock the filesystem operations
        mock_fs = Mock()
        publisher._fs = mock_fs

        # And: Mock GitHook and temporary directory operations
        with (
            patch("tempfile.TemporaryDirectory") as mock_temp_dir,
            patch("dags.operators.artifacts_publisher.GitHook") as mock_git_hook_class,
            patch("os.path.exists") as mock_exists,
        ):
            # Setup mocks for successful operation
            temp_dir_path = "/tmp/test_git_clone"
            mock_temp_context = MagicMock()
            mock_temp_context.__enter__.return_value = temp_dir_path
            mock_temp_context.__exit__.return_value = None
            mock_temp_dir.return_value = mock_temp_context

            mock_git_instance = MagicMock()
            mock_git_context = MagicMock()
            mock_git_context.__enter__.return_value = mock_git_instance
            mock_git_context.__exit__.return_value = None
            mock_git_hook_class.return_value = mock_git_context

            mock_exists.return_value = True

            # When: The download_ndc_lookup_file method is called
            publisher.download_ndc_lookup_file()

        # Then: Git clone should be called with the custom branch
        mock_git_instance.clone.assert_called_once_with(
            target_dir=temp_dir_path,
            branch="develop",
            sparse_folders=["metaDataStore/versioned/coding/common/ndc_fdb/202309"],
        )

    def test_download_ndc_lookup_file_temporary_directory_error(
        self, artifacts_publisher_factory
    ):
        """
        Test error handling when temporary directory creation fails.

        Verifies that the method properly propagates exceptions when the
        temporary directory context manager encounters issues during setup.
        """
        # Given: An ArtifactsPublisher instance configured with git metadata repository
        publisher = artifacts_publisher_factory(
            git_meta_repo="test-meta-repo",
            dataset_artifacts_path="s3://bucket/dataset/artifacts",
        )

        # And: Mock the load_ndc_mapping_file_name method
        test_ndc_file_path = (
            "versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
        )
        publisher.load_ndc_mapping_file_name = Mock(return_value=test_ndc_file_path)

        # And: Mock temporary directory to raise an exception
        with patch("tempfile.TemporaryDirectory") as mock_temp_dir:
            mock_temp_dir.side_effect = OSError("Failed to create temporary directory")

            # When: The download_ndc_lookup_file method is called
            # Then: The temporary directory exception should be propagated
            with pytest.raises(OSError, match="Failed to create temporary directory"):
                publisher.download_ndc_lookup_file()

    def test_download_ndc_lookup_file_git_hook_context_manager_error(
        self, artifacts_publisher_factory
    ):
        """
        Test error handling when GitHook context manager fails during setup.

        Verifies that the method properly propagates exceptions when the
        GitHook context manager encounters issues during initialization.
        """
        # Given: An ArtifactsPublisher instance configured with git metadata repository
        publisher = artifacts_publisher_factory(
            git_meta_repo="test-meta-repo",
            dataset_artifacts_path="s3://bucket/dataset/artifacts",
        )

        # And: Mock the load_ndc_mapping_file_name method
        test_ndc_file_path = (
            "versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
        )
        publisher.load_ndc_mapping_file_name = Mock(return_value=test_ndc_file_path)

        # And: Mock GitHook context manager to raise an exception
        with (
            patch("tempfile.TemporaryDirectory") as mock_temp_dir,
            patch("dags.operators.artifacts_publisher.GitHook") as mock_git_hook_class,
        ):
            # Setup temporary directory mock
            temp_dir_path = "/tmp/test_git_clone"
            mock_temp_context = MagicMock()
            mock_temp_context.__enter__.return_value = temp_dir_path
            mock_temp_context.__exit__.return_value = None
            mock_temp_dir.return_value = mock_temp_context

            # Setup GitHook context manager to raise exception
            mock_git_context = MagicMock()
            mock_git_context.__enter__.side_effect = Exception(
                "GitHook initialization failed"
            )
            mock_git_hook_class.return_value = mock_git_context

            # When: The download_ndc_lookup_file method is called
            # Then: The GitHook exception should be propagated
            with pytest.raises(Exception, match="GitHook initialization failed"):
                publisher.download_ndc_lookup_file()

    def test_download_ndc_lookup_file_logging_behavior(
        self, artifacts_publisher_factory
    ):
        """
        Test that appropriate log messages are generated during successful operation.

        Verifies that the method logs informational messages at key points during
        the download process for debugging and monitoring purposes.
        """
        # Given: An ArtifactsPublisher instance configured with git metadata repository
        publisher = artifacts_publisher_factory(
            git_meta_repo="test-meta-repo",
            dataset_artifacts_path="s3://bucket/dataset/artifacts",
        )

        # And: Mock the load_ndc_mapping_file_name method
        test_ndc_file_path = (
            "versioned/coding/common/ndc_fdb/202309/ndc_fdb_202309.csv.gz"
        )
        publisher.load_ndc_mapping_file_name = Mock(return_value=test_ndc_file_path)

        # And: Mock the filesystem operations
        mock_fs = Mock()
        publisher._fs = mock_fs

        # And: Mock GitHook and temporary directory operations
        with (
            patch("tempfile.TemporaryDirectory") as mock_temp_dir,
            patch("dags.operators.artifacts_publisher.GitHook") as mock_git_hook_class,
            patch("os.path.exists") as mock_exists,
            patch("dags.operators.artifacts_publisher.logger") as mock_logger,
        ):
            # Setup mocks for successful operation
            temp_dir_path = "/tmp/test_git_clone"
            mock_temp_context = MagicMock()
            mock_temp_context.__enter__.return_value = temp_dir_path
            mock_temp_context.__exit__.return_value = None
            mock_temp_dir.return_value = mock_temp_context

            mock_git_instance = MagicMock()
            mock_git_context = MagicMock()
            mock_git_context.__enter__.return_value = mock_git_instance
            mock_git_context.__exit__.return_value = None
            mock_git_hook_class.return_value = mock_git_context

            mock_exists.return_value = True

            # When: The download_ndc_lookup_file method is called
            publisher.download_ndc_lookup_file()

        # Then: Appropriate log messages should be generated
        expected_coding_systems_path = (
            "s3://bucket/dataset/artifacts/coding_systems.csv"
        )
        mock_logger.info.assert_any_call(
            f"coding system path: {expected_coding_systems_path}"
        )

        # And: Loading and publishing log messages should be present
        mock_logger.info.assert_any_call(
            "loading ndc_fdb_202309.csv.gz from metadata folder metaDataStore/versioned/coding/common/ndc_fdb/202309 to s3://bucket/dataset/artifacts/ndc_fdb.csv.gz"
        )
        mock_logger.info.assert_any_call(
            "Publish ndc_fdb_202309.csv.gz to s3://bucket/dataset/artifacts/ndc_fdb.csv.gz"
        )

    @pytest.mark.parametrize('revision',[
                             pytest.param("20210901", id="numeric_revision"),
                             pytest.param(None, id="none"),
                             ]
                             )
    def test_execute_success(self, artifacts_publisher: ArtifactsPublisher, revision: str | None):
        """Test successful execution of the operator."""
        # Given: an ArtifactsPublisher with all necessary configurations
        publisher = artifacts_publisher
        artifacts_publisher.revision = revision

        # And: all the method are mocked
        publisher._copy_artifacts = Mock() # type: ignore[method-assign]
        publisher._copy_ama_artifacts = Mock()  # type: ignore[method-assign]
        publisher._copy_prophecy_artifacts = Mock() # type: ignore[method-assign]
        publisher.check_revision = Mock()  # type: ignore[method-assign]
        publisher._copy_git_artifacts = Mock()  # type: ignore[method-assign]
        publisher.download_ndc_lookup_file = Mock()  # type: ignore[method-assign]

        # When: The execute method is called
        publisher.execute({})

        # Then: all the methods are called
        if revision:
            publisher.check_revision.assert_called_once_with()
        else:
            publisher.check_revision.assert_not_called()
        publisher._copy_artifacts.assert_called_once_with()
        publisher._copy_ama_artifacts.assert_called_once_with()
        publisher._copy_prophecy_artifacts.assert_called_once_with()
        publisher._copy_git_artifacts.assert_called_once_with()
        publisher.download_ndc_lookup_file.assert_called_once_with()



def _generate_fake_zip_content(version: str | None = "v1.2.3"):
    with tempfile.NamedTemporaryFile("wb", suffix=".zip") as zip_content:
        with zipfile.ZipFile(zip_content, "w") as zf:
            zf.writestr("some_file.txt", "whatever")
            if version:
                zf.writestr("GIT_VERSION", version)
        return Path(zip_content.name).read_bytes()


