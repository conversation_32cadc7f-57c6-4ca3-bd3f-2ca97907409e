import csv
import logging
import os
import tempfile
from collections.abc import Generator
from unittest.mock import Mock

import pytest
from aetion.adip.airflow.integrations.s3.s3_support import AetionS3FileSystem
from airflow.exceptions import AirflowException
from mypy_boto3_s3 import S3Client
from operators.catalog_loader import get_catalog_from_s3

from dags.operators.file_validator import FileValidator


@pytest.fixture
def bucket(s3_client: S3Client) -> Generator[str, None, None]:
    """Create a test S3 bucket for data processing tests."""
    bucket_name = "test-bucket"
    s3_client.create_bucket(Bucket=bucket_name)
    yield bucket_name


@pytest.fixture
def s3_filesystem_with_test_data(
    s3_client: S3Client, bucket: str
) -> Generator[AetionS3FileSystem, None, None]:
    """Create AetionS3FileSystem instance with test data for file validator tests."""

    # Create S3 client and filesystem
    fs = AetionS3FileSystem()

    # Get the path to test resources
    # Current file: tests/aetion/adip/airflow/operators/test_file_validator.py
    # We need to go to: tests/resources/
    current_dir = os.path.dirname(__file__)  # operators dir
    airflow_dir = os.path.dirname(current_dir)  # airflow dir
    adip_dir = os.path.dirname(airflow_dir)  # adip dir
    aetion_dir = os.path.dirname(adip_dir)  # aetion dir
    tests_dir = os.path.dirname(aetion_dir)  # tests dir
    test_resources_dir = os.path.join(tests_dir, "resources")

    # Upload raw-data-catalog.yml to a separate location (not in raw data path)
    catalog_path = os.path.join(test_resources_dir, "raw-data-catalog.yml")
    if os.path.exists(catalog_path):
        with open(catalog_path, "rb") as f:
            s3_client.put_object(
                Bucket=bucket,
                Key="config/raw-data-catalog.yml",  # Put in config folder, not in raw data path
                Body=f.read(),
            )

    # Upload raw data files to the raw/ folder
    raw_dir = os.path.join(test_resources_dir, "raw")
    if os.path.exists(raw_dir):
        for filename in os.listdir(raw_dir):
            file_path = os.path.join(raw_dir, filename)
            if os.path.isfile(file_path):
                with open(file_path, "rb") as f:
                    s3_client.put_object(
                        Bucket=bucket,
                        Key=f"raw/{filename}",  # Upload to raw/ folder
                        Body=f.read(),
                    )

    yield fs


@pytest.fixture
def file_validator(bucket: str, s3_filesystem_with_test_data: AetionS3FileSystem):
    return FileValidator(
        raw_data_path=f"{bucket}/etl/dataset/revision/raw/",
        raw_data_inventory_summary_path=f"{bucket}/etl/dataset/revision/raw-inventory/inventory_summary.csv",
        rdc_path=f"s3://{bucket}/etl/dataset/revision/artifacts/raw-data-catalog.yml",
        client="client",
        dataset="dataset",
        task_id="test_validator",
    )


def test_file_validator(file_validator: FileValidator, bucket: str):
    # raw_data_path should be prefixed with the bucket
    assert file_validator.raw_data_path.startswith(f"{bucket}/")

    # raw_data_inventory_summary_path should be prefixed with the bucket
    assert file_validator.raw_data_inventory_summary_path.startswith(f"{bucket}/")

    # rdc_path should be prefixed with the bucket
    assert file_validator.rdc_path.startswith(f"s3://{bucket}/")

    # client and dataset should be set
    assert file_validator.client == "client"
    assert file_validator.dataset == "dataset"


@pytest.fixture
def large_file_inventory() -> list[tuple[str, int, str]]:
    """Fixture providing large file inventory for performance testing"""
    return [
        (f"file{i}.csv", 100 + i, f"2024-01-{i % 30 + 1:02d}") for i in range(10000)
    ]


@pytest.fixture
def complex_catalog_configurations():
    """Fixture providing various catalog configurations for testing"""
    return {
        "with_skip_groups": {
            "name": "test-client",
            "skip_loading_table_groups": ["skip_group"],
            "table_groups": ["default", "skip_group"],
            "tables": [
                {"name": "KEEP", "glob": "keep*.csv", "group": "default"},
                {"name": "SKIP", "glob": "skip*.csv", "group": "skip_group"},
            ],
        },
        "with_skip_tables": {
            "name": "test-client",
            "skip_loading_tables": ["SKIP_TABLE"],
            "tables": [
                {"name": "KEEP_TABLE", "glob": "keep*.csv", "group": "default"},
                {"name": "SKIP_TABLE", "glob": "skip*.csv", "group": "default"},
            ],
        },
    }


@pytest.mark.parametrize(
    "expected_table_globs, ignored_globs, s3_files, file_options, lookup_tables",
    [
        (["abc*.csv", "bcd*.csv"], [], ["abcd.csv", "bcd1.csv", "bcd2.csv"], {}, []),
        (
            ["abc*.csv", "bcd*.csv"],
            [],
            ["abcd.csv"],
            {"bcd*.csv": {"allowMissingOrEmpty": True}},
            [],
        ),
        (["abc*.csv"], ["ignore.csv"], ["abcd.csv", "ignore.csv"], {}, []),
        (["{uppercase,UPPERCASE}*.csv"], [], ["uppercase.csv"], {}, []),
        (["one/**/ab*.csv"], [], ["one/abc.csv", "one/two/abc.csv"], {}, []),
        (["abc*.csv"], [], ["abcd.csv", "med.csv"], {}, [{"name": "MED", "path": "med.csv"}]),
        (["abc*.csv"], ["ignore.csv"], ["abcd.csv", "ignore.csv", "lookup.csv"], {}, [{"name": "LOOKUP", "path": "lookup.csv"}]),
    ],
)
def test_validate(
    expected_table_globs,
    ignored_globs,
    s3_files,
    file_options,
    lookup_tables,
    file_validator: FileValidator,
):
    validator = file_validator

    validator._expected_files = expected_table_globs
    validator._ignored_globs = ignored_globs
    validator._inventory_files = s3_files
    validator._file_options = file_options
    validator._lookup_tables = lookup_tables

    assert validator.validate() is None


@pytest.mark.parametrize(
    "inventory, yaml_catalog, expected_output",
    [
        (
            [
                ("abcd.csv", 123, "10-01-2024"),
                ("bcd1.csv", 124, "10-02-2024"),
                ("bcd2.csv", 125, "10-03-2024"),
            ],
            {
                "tables": [
                    {"name": "ABC", "glob": "abc*.csv", "group": "default"},
                    {"name": "BCD", "glob": "bcd*.csv", "group": "default"},
                ],
                "ignored_files": [],
            },
            [
                ["key", "size", "last_modified", "table", "glob", "ignored", "lookup_table"],
                ["abcd.csv", "123", "10-01-2024", "ABC", "abc*.csv", "False", "False"],
                ["bcd1.csv", "124", "10-02-2024", "BCD", "bcd*.csv", "False", "False"],
                ["bcd2.csv", "125", "10-03-2024", "BCD", "bcd*.csv", "False", "False"],
            ],
        ),
        (
            [("abcd.csv", 345, "08-01-2024")],
            {
                "tables": [
                    {"name": "ABC", "glob": "abc*.csv", "group": "default"},
                    {"name": "BCD", "glob": "bcd*.csv", "group": "default"},
                ],
                "ignored_files": [],
            },
            [
                ["key", "size", "last_modified", "table", "glob", "ignored", "lookup_table"],
                ["abcd.csv", "345", "08-01-2024", "ABC", "abc*.csv", "False", "False"],
            ],
        ),
        (
            [("abcd.csv", 563, "09-01-2024"), ("ignore.csv", 2, "11-01-2023")],
            {
                "tables": [
                    {"name": "ABC", "glob": "abc*.csv", "group": "default"},
                    {"name": "BCD", "glob": "bcd*.csv", "group": "default"},
                ],
                "ignored_files": ["ignore.csv"],
            },
            [
                ["key", "size", "last_modified", "table", "glob", "ignored", "lookup_table"],
                ["abcd.csv", "563", "09-01-2024", "ABC", "abc*.csv", "False", "False"],
                ["ignore.csv", "2", "11-01-2023", "", "", "True", "False"],
            ],
        ),
        (
            [("uppercase.csv", 22, "06-01-2024")],
            {
                "tables": [
                    {
                        "name": "UPPERCASE",
                        "glob": "{uppercase,UPPERCASE}*.csv",
                        "group": "default",
                    },
                ],
                "ignored_files": [],
            },
            [
                ["key", "size", "last_modified", "table", "glob", "ignored", "lookup_table"],
                [
                    "uppercase.csv",
                    "22",
                    "06-01-2024",
                    "UPPERCASE",
                    "{uppercase,UPPERCASE}*.csv",
                    "False",
                    "False",
                ],
            ],
        ),
        (
            [
                ("one/abc.csv", 667, "06-01-2024"),
                ("one/two/abc.csv", 778, "06-02-2024"),
            ],
            {
                "tables": [
                    {"name": "ABC", "glob": "one/**/ab*.csv", "group": "default"},
                    {"name": "BCD", "glob": "bcd*.csv", "group": "default"},
                ],
                "ignored_files": [],
            },
            [
                ["key", "size", "last_modified", "table", "glob", "ignored", "lookup_table"],
                ["one/abc.csv", "667", "06-01-2024", "ABC", "one/**/ab*.csv", "False", "False"],
                [
                    "one/two/abc.csv",
                    "778",
                    "06-02-2024",
                    "ABC",
                    "one/**/ab*.csv",
                    "False",
                    "False",
                ],
            ],
        ),
        (
            [("one/abc.csv", 667, "06-01-2024"), ("bcd.csv", 778, "06-02-2024")],
            {
                "tables": [
                    {
                        "name": "UPPERCASE",
                        "glob": "{uppercase,UPPERCASE}*.csv",
                        "group": "default",
                    }
                ],
                "ignored_files": [],
            },
            [
                ["key", "size", "last_modified", "table", "glob", "ignored", "lookup_table"],
                ["one/abc.csv", "667", "06-01-2024", "", "", "False", "False"],
                ["bcd.csv", "778", "06-02-2024", "", "", "False", "False"],
            ],
        ),
        (
            [("abc.csv", 667, "06-01-2024"), ("abcd.csv", 778, "06-02-2024")],
            {
                "tables": [
                    {"name": "ABC", "glob": "abc*.csv", "group": "default"},
                    {"name": "BCD", "glob": "*bcd*.csv", "group": "default"},
                ],
                "ignored_files": [],
            },
            [
                ["key", "size", "last_modified", "table", "glob", "ignored", "lookup_table"],
                ["abc.csv", "667", "06-01-2024", "ABC", "abc*.csv", "False", "False"],
                [
                    "abcd.csv",
                    "778",
                    "06-02-2024",
                    "ABC;BCD",
                    "abc*.csv;*bcd*.csv",
                    "False",
                    "False",
                ],
            ],
        ),
        (
            [("abcd.csv", 563, "09-01-2024"), ("ignore.csv", 2, "11-01-2023"), ("med.csv", 100, "12-01-2024")],
            {
                "tables": [
                    {"name": "ABC", "glob": "abc*.csv", "group": "default"},
                    {"name": "BCD", "glob": "bcd*.csv", "group": "default"},
                ],
                "ignored_files": ["ignore.csv"],
                "lookup_tables": [{"name": "MED", "path": "med.csv"}],
            },
            [
                ["key", "size", "last_modified", "table", "glob", "ignored", "lookup_table"],
                ["abcd.csv", "563", "09-01-2024", "ABC", "abc*.csv", "False", "False"],
                ["ignore.csv", "2", "11-01-2023", "", "", "True", "False"],
                ["med.csv", "100", "12-01-2024", "", "", "False", "True"],
            ],
        ),
    ],
)
def test_inventory_summary(
    s3_filesystem_with_test_data: AetionS3FileSystem,
    inventory,
    yaml_catalog,
    expected_output,
    file_validator: FileValidator,
):
    validator = file_validator

    # Override the filesystem with the mocked one
    validator._fs = s3_filesystem_with_test_data
    validator._inventory = inventory
    validator._yaml_catalog = yaml_catalog

    assert validator.write_inventory_summary() is None

    # Read the CSV content from S3
    csv_content = s3_filesystem_with_test_data.cat(
        "s3://" + validator.raw_data_inventory_summary_path, as_bytes=False
    )
    data = list(csv.reader(csv_content.splitlines()))

    assert len(data[0]) == len(expected_output[0])
    assert len(data) == len(expected_output)
    assert data == expected_output


@pytest.mark.parametrize(
    "expected_table_globs, ignored_globs, s3_files, file_options, lookup_tables, error_msg",
    [
        (
            ["abc*.csv"],
            [],
            ["abcd.csv", "bcd1.csv", "bcd2.csv"],
            {},
            [],
            "Found files in s3://test-bucket/resources/raw/ that do not have an associated table: "
            "\\['bcd1.csv', 'bcd2.csv'\\]. Please add them to be ignored or as lookup_tables in the raw data catalog",
        ),
        (
            ["abc*.csv", "bcd*.csv"],
            [],
            ["abcd.csv"],
            {"abc*.csv": {}, "bcd*.csv": {}},
            [],
            "The following tables had 0 files matching in s3://test-bucket/resources/raw/: \\['bcd\\*.csv'\\]. "
            "Please check the raw data catalog",
        ),
        (
            ["ambiguous*.csv", "ambiguou*.csv"],
            [],
            ["ambiguous.csv"],
            {},
            [],
            "The following files were found to match multiple tables in s3://test-bucket/resources/raw/: "
            "{'ambiguous.csv': \\['ambiguous\\*.csv', 'ambiguou\\*.csv'\\]}. Please correct the ambiguity.",
        ),
        (
            ["abc*.csv"],
            [],
            ["abcd.csv"],
            {},
            [{"name": "MED", "path": "med.csv"}],
            "The following lookup tables were defined in the raw data catalog but not found in s3://test-bucket/resources/raw/: "
            "\\['med.csv'\\]. Please check the raw data catalog",
        ),
    ],
)
def test_validate_errors(
    expected_table_globs, ignored_globs, s3_files, file_options, lookup_tables, error_msg
):
    rdc_path = "s3://test-bucket/resources/raw-data-catalog.yml"
    raw_data_path = "s3://test-bucket/resources/raw/"
    client = "client"
    dataset = "dataset"
    raw_data_inventory_summary_path = "s3://test-bucket/inventory/summary.csv"

    validator = FileValidator(
        raw_data_path=raw_data_path,
        raw_data_inventory_summary_path=raw_data_inventory_summary_path,
        rdc_path=rdc_path,
        client=client,
        dataset=dataset,
        task_id="file_validator",
    )

    validator._expected_files = expected_table_globs
    validator._ignored_globs = ignored_globs
    validator._inventory_files = s3_files
    validator._file_options = file_options
    validator._lookup_tables = lookup_tables

    with pytest.raises(AirflowException, match=error_msg):
        validator.validate()


@pytest.mark.parametrize(
    "client, dataset, expected_default_options, expected_table_options",
    [
        (
            "client0",
            "dataset0",
            {"header": True, "delimiter": ","},
            {"TABLE": {}, "EMPTY": {}},
        ),
        (
            "client0",
            "dataset1",
            {"header": True, "delimiter": ",", "allowMissingOrEmpty": True},
            {"TABLE": {}, "EMPTY": {}, "MISSING": {}},
        ),
        (
            "client0",
            "missing_table",
            {"header": True, "delimiter": ","},
            {
                "TABLE": {},
                "EMPTY": {},
                "MISSING": {
                    "header": True,
                    "delimiter": ",",
                    "allowMissingOrEmpty": True,
                },
            },
        ),
    ],
)
def test_get_catalog_from_s3(
    s3_filesystem_with_test_data,
    client,
    dataset,
    expected_default_options,
    expected_table_options,
):
    rdc_path = "s3://test-bucket/config/raw-data-catalog.yml"  # Updated path

    yaml_catalog = get_catalog_from_s3(
        client, dataset, rdc_path, s3_filesystem_with_test_data
    )

    assert yaml_catalog["name"] == dataset + "-" + client
    assert expected_default_options == yaml_catalog["default_options"]
    for table in yaml_catalog["tables"]:
        assert expected_table_options[table["name"]] == table.get("options", {})


@pytest.mark.parametrize(
    "client, dataset",
    [("client0", "dataset0"), ("client0", "dataset1"), ("client0", "missing_table")],
)
def test_execute(s3_filesystem_with_test_data, client, dataset):
    rdc_path = "s3://test-bucket/config/raw-data-catalog.yml"  # Updated path
    raw_data_path = "s3://test-bucket/raw/"  # Use proper raw data path
    raw_data_inventory_summary_path = "s3://test-bucket/inventory/summary.csv"

    validator = FileValidator(
        raw_data_path=raw_data_path,
        raw_data_inventory_summary_path=raw_data_inventory_summary_path,
        rdc_path=rdc_path,
        client=client,
        dataset=dataset,
        task_id="file_validator",
    )

    # Override the filesystem with the mocked one
    validator._fs = s3_filesystem_with_test_data

    # Mock the inventory_files property to return just filenames (not full paths)
    # This simulates the expected behavior where the FileValidator strips the raw data path prefix
    validator._inventory_files = ["empty.csv", "table1.csv", "table2.csv"]

    validator.execute(None)

    # Check that the inventory summary file was created and has the expected number of lines
    csv_content = s3_filesystem_with_test_data.cat(
        raw_data_inventory_summary_path, as_bytes=False
    )
    lines = csv_content.splitlines()
    assert len(lines) == 4


def test_execute_error(s3_filesystem_with_test_data):
    client = "client0"
    dataset = "missing_table_error"
    rdc_path = "s3://test-bucket/config/raw-data-catalog.yml"  # Updated path
    raw_data_path = "s3://test-bucket/raw/"  # Updated path to match other tests
    raw_data_inventory_summary_path = "s3://test-bucket/inventory/summary.csv"

    validator = FileValidator(
        raw_data_path=raw_data_path,
        raw_data_inventory_summary_path=raw_data_inventory_summary_path,
        rdc_path=rdc_path,
        client=client,
        dataset=dataset,
        task_id="file_validator",
    )

    # Override the filesystem with the mocked one
    validator._fs = s3_filesystem_with_test_data

    # Mock the inventory_files property to return just filenames (not full paths)
    # This simulates the expected behavior where the FileValidator strips the raw data path prefix
    validator._inventory_files = ["empty.csv", "table1.csv", "table2.csv"]

    with pytest.raises(AirflowException):
        validator.execute(None)


def test_fs_property_lazy_initialization(file_validator: FileValidator):
    """Test that the fs property creates an AetionS3FileSystem instance when _fs is None"""
    # Given: A FileValidator instance with no filesystem initialized
    validator = file_validator

    # When: The _fs attribute is initially None
    assert validator._fs is None

    # Then: Accessing the fs property should create an AetionS3FileSystem instance
    filesystem = validator.fs
    assert isinstance(filesystem, AetionS3FileSystem)
    assert validator._fs is not None

    # And: Subsequent calls should return the same instance (cached)
    filesystem2 = validator.fs
    assert filesystem is filesystem2


def test_inventory_files_property_processing(file_validator: FileValidator):
    """Test that the inventory_files property correctly processes inventory data using lambda function"""
    # Given: A FileValidator instance with mock inventory data
    validator = file_validator

    # And: Mock inventory data with tuples of (filename, size, last_modified)
    from datetime import datetime

    mock_inventory = [
        ("file1.csv", 1024, datetime(2024, 1, 1, 10, 0, 0)),
        ("file2.csv", 2048, datetime(2024, 1, 2, 11, 0, 0)),
        ("file3.csv", 512, datetime(2024, 1, 3, 12, 0, 0)),
    ]
    validator._inventory = mock_inventory

    # When: Accessing the inventory_files property
    files = validator.inventory_files

    # Then: Should extract only the filenames (first element of each tuple)
    expected_files = ["file1.csv", "file2.csv", "file3.csv"]
    assert files == expected_files

    # And: Subsequent calls should return cached result
    files2 = validator.inventory_files
    assert files is files2


# Missing Functionality Tests


def test_expected_files_with_skip_loading_table_groups(file_validator: FileValidator):
    """Test filtering of tables by skip_loading_table_groups"""
    # Given: A FileValidator with a catalog containing skip_loading_table_groups
    validator = file_validator

    # And: Mock catalog with tables in different groups, some to be skipped
    mock_catalog = {
        "name": "dataset0-client0",
        "skip_loading_table_groups": ["skip_group", "temp_group"],
        "table_groups": ["default", "skip_group", "temp_group"],
        "tables": [
            {"name": "KEEP_TABLE1", "glob": "keep1*.csv", "group": "default"},
            {"name": "KEEP_TABLE2", "glob": "keep2*.csv", "group": "default"},
            {"name": "SKIP_TABLE1", "glob": "skip1*.csv", "group": "skip_group"},
            {"name": "SKIP_TABLE2", "glob": "skip2*.csv", "group": "temp_group"},
        ],
    }
    validator._yaml_catalog = mock_catalog

    # When: Getting expected files
    expected = validator.expected_files

    # Then: Should only include tables from non-skipped groups
    assert "keep1*.csv" in expected
    assert "keep2*.csv" in expected
    assert "skip1*.csv" not in expected
    assert "skip2*.csv" not in expected
    assert len(expected) == 2


def test_expected_files_with_skip_loading_tables(file_validator: FileValidator):
    """Test filtering of individual tables by skip_loading_tables"""
    # Given: A FileValidator with a catalog containing skip_loading_tables
    validator = file_validator

    # And: Mock catalog with specific tables to be skipped
    mock_catalog = {
        "name": "dataset0-client0",
        "skip_loading_tables": ["SKIP_TABLE1", "SKIP_TABLE3"],
        "table_groups": ["default"],
        "tables": [
            {"name": "KEEP_TABLE1", "glob": "keep1*.csv", "group": "default"},
            {"name": "SKIP_TABLE1", "glob": "skip1*.csv", "group": "default"},
            {"name": "KEEP_TABLE2", "glob": "keep2*.csv", "group": "default"},
            {"name": "SKIP_TABLE3", "glob": "skip3*.csv", "group": "default"},
        ],
    }
    validator._yaml_catalog = mock_catalog

    # When: Getting expected files
    expected = validator.expected_files

    # Then: Should only include tables not in skip_loading_tables
    assert "keep1*.csv" in expected
    assert "keep2*.csv" in expected
    assert "skip1*.csv" not in expected
    assert "skip3*.csv" not in expected
    assert len(expected) == 2


def test_expected_files_with_table_groups_filtering(file_validator: FileValidator):
    """Test table_groups filtering when specific table_groups are defined"""
    # Given: A FileValidator with a catalog specifying specific table_groups
    validator = file_validator

    # And: Mock catalog with table_groups filter (only include 'default' and 'special')
    mock_catalog = {
        "name": "dataset0-client0",
        "table_groups": ["default", "special"],  # Only these groups should be included
        "tables": [
            {"name": "INCLUDE_TABLE1", "glob": "include1*.csv", "group": "default"},
            {"name": "INCLUDE_TABLE2", "glob": "include2*.csv", "group": "special"},
            {"name": "EXCLUDE_TABLE1", "glob": "exclude1*.csv", "group": "other"},
            {"name": "EXCLUDE_TABLE2", "glob": "exclude2*.csv", "group": "temp"},
        ],
    }
    validator._yaml_catalog = mock_catalog

    # When: Getting expected files
    expected = validator.expected_files

    # Then: Should only include tables from specified table_groups
    assert "include1*.csv" in expected
    assert "include2*.csv" in expected
    assert "exclude1*.csv" not in expected
    assert "exclude2*.csv" not in expected
    assert len(expected) == 2


def test_file_options_inheritance_from_default(file_validator: FileValidator):
    """Test that table options inherit from default_options when not specified"""
    # Given: A FileValidator with a catalog containing default_options
    validator = file_validator

    # And: Mock catalog with default_options and tables with/without specific options
    mock_catalog = {
        "name": "dataset0-client0",
        "default_options": {
            "header": True,
            "delimiter": ",",
            "allowMissingOrEmpty": False,
        },
        "tables": [
            # Table without specific options - should inherit default_options
            {"name": "INHERIT_TABLE", "glob": "inherit*.csv", "group": "default"},
            # Table with specific options - should override default_options
            {
                "name": "OVERRIDE_TABLE",
                "glob": "override*.csv",
                "group": "default",
                "options": {
                    "header": False,
                    "delimiter": "|",
                    "allowMissingOrEmpty": True,
                },
            },
            # Table with partial options - should merge with default_options
            {
                "name": "PARTIAL_TABLE",
                "glob": "partial*.csv",
                "group": "default",
                "options": {"delimiter": ";"},
            },
        ],
    }
    validator._yaml_catalog = mock_catalog

    # When: Getting file options
    options = validator.file_options

    # Then: Table without options should inherit all default_options
    inherit_options = options["inherit*.csv"]
    assert inherit_options["header"] is True
    assert inherit_options["delimiter"] == ","
    assert inherit_options["allowMissingOrEmpty"] is False

    # And: Table with specific options should use those options
    override_options = options["override*.csv"]
    assert override_options["header"] is False
    assert override_options["delimiter"] == "|"
    assert override_options["allowMissingOrEmpty"] is True

    # And: Table with partial options should merge with defaults
    partial_options = options["partial*.csv"]
    assert partial_options["delimiter"] == ";"  # Overridden
    # Note: The current implementation uses table.get('options', default_options)
    # which means partial options don't merge, they replace entirely


def test_file_options_with_no_default_options(file_validator: FileValidator):
    """Test file_options behavior when default_options is not specified"""
    # Given: A FileValidator with a catalog without default_options
    validator = file_validator

    # And: Mock catalog without default_options
    mock_catalog = {
        "name": "dataset0-client0",
        "tables": [
            {"name": "TABLE1", "glob": "table1*.csv", "group": "default"},
            {
                "name": "TABLE2",
                "glob": "table2*.csv",
                "group": "default",
                "options": {"header": True},
            },
        ],
    }
    validator._yaml_catalog = mock_catalog

    # When: Getting file options
    options = validator.file_options

    # Then: Tables should get None as default options when no default_options specified
    assert options["table1*.csv"] is None
    assert options["table2*.csv"]["header"] is True


def test_catalog_file_read_error():
    """Test error handling when catalog file cannot be read from S3"""
    # Given: A FileValidator with a non-existent catalog path
    validator = FileValidator(
        raw_data_path="test-bucket/raw/",
        raw_data_inventory_summary_path="test-bucket/inventory/summary.csv",
        rdc_path="s3://test-bucket/nonexistent/catalog.yml",
        client="client0",
        dataset="dataset0",
        task_id="test_validator",
    )

    # And: Mock filesystem that raises an error when trying to open the file
    mock_fs = Mock()
    mock_fs.open.side_effect = FileNotFoundError("No such file or directory")
    validator._fs = mock_fs

    # When: Trying to access the yaml_catalog property
    # Then: Should raise the FileNotFoundError
    with pytest.raises(FileNotFoundError, match="No such file or directory"):
        _ = validator.yaml_catalog


def test_empty_file_inventory_validation(
    complex_catalog_configurations: dict,
    file_validator: FileValidator,
    bucket: str,
    s3_client: S3Client,
    caplog: pytest.LogCaptureFixture,
):
    """Test behavior when there are no files in the inventory"""

    # Given: an existing bucket
    _assert_existing_bucket(s3_client, bucket)

    # And: A FileValidator with an empty inventory
    validator = file_validator
    raw_data_s3_prefix = "/".join(validator.raw_data_path.split("/")[1:])
    assert (
        s3_client.list_objects_v2(Bucket=bucket, Prefix=raw_data_s3_prefix)["KeyCount"]
        == 0
    )

    # And: some raw data catalog
    validator._yaml_catalog = complex_catalog_configurations

    # When: Validating
    with caplog.at_level(logging.INFO):
        validator.validate()

    # Then: Should not raise any errors (no files, nothing to validate)
    # Note: This test relies on the fact that other tests cover the case where
    # there are expected files but none in the inventory. This test is just for
    # the specific case of no inventory and no expected files.

    assert "s3_files: []" in caplog.text
    assert "not_in_ignored_or_lookup_s3_files: []" in caplog.text
    assert "files_to_globs: {}" in caplog.text
    assert "all_matched_glob_patterns: set()" in caplog.text


def test_complex_glob_patterns(file_validator: FileValidator):
    """Test advanced glob pattern matching with various patterns"""
    # Given: A FileValidator instance
    validator = file_validator

    # When: Testing various glob patterns
    test_cases = [
        # (pattern, filename, should_match)
        ("file[0-9].csv", "file1.csv", True),
        ("file[0-9].csv", "fileA.csv", False),
        ("file?.csv", "file1.csv", True),
        ("file?.csv", "file12.csv", False),
        ("**/*.csv", "dir1/file.csv", True),
        ("**/*.csv", "dir1/dir2/file.csv", True),
        ("file{1,2,3}.csv", "file1.csv", True),
        ("file{1,2,3}.csv", "file4.csv", False),
        ("data_*.csv", "data_2024.csv", True),
        ("data_*.csv", "metadata.csv", False),
    ]

    # Then: Each pattern should match correctly
    for pattern, filename, should_match in test_cases:
        matches = validator.glob_match(filename, [pattern])
        if should_match:
            assert len(matches) == 1 and matches[0] == pattern, (
                f"Pattern '{pattern}' should match '{filename}'"
            )
        else:
            assert len(matches) == 0, (
                f"Pattern '{pattern}' should not match '{filename}'"
            )


def test_inventory_files_property_should_be_relative_paths_to_raw_data_path(
    s3_client: S3Client, file_validator: FileValidator, bucket: str
):
    """Test that the inventory_files property returns paths relative to raw_data_path"""

    # Given: a existing bucket
    assert bucket in [b["Name"] for b in s3_client.list_buckets()["Buckets"]]

    # Given: A FileValidator instance
    validator = file_validator

    # Given some raw files uner the raw_data_path
    key_prefix = "/".join(
        validator.raw_data_path.split("/")[1:]
    )  # remove the bucket name from the path
    s3_client.put_object(
        Bucket=bucket, Key=f"{key_prefix}/file1.csv", Body=b"idontcare"
    )
    s3_client.put_object(
        Bucket=bucket, Key=f"{key_prefix}/dir/file2.csv", Body=b"idontcare"
    )
    s3_client.put_object(
        Bucket=bucket,
        Key=f"{key_prefix}/another_dir/file3.csv",
        Body=b"idontcare",
    )

    # When: Accessing the inventory_files property
    files = validator.inventory_files

    # Then: Should return paths relative to raw_data_path
    expected_files = ["file1.csv", "dir/file2.csv", "another_dir/file3.csv"]
    assert set(files) == set(expected_files)


def test_execute_large_file_inventory(
    s3_client: S3Client,
    file_validator: FileValidator,
    bucket: str,
    large_file_inventory: list[tuple[str, int, str]],
):
    """Test execute with a large number of files"""

    # Given: a bucket
    s3_client.create_bucket(Bucket="test-bucket")

    # And: a large inventory in the raw data path
    for filename, *_ in large_file_inventory:
        s3_client.put_object(
            Bucket="test-bucket",
            Key=f"etl/dataset/revision/raw/{filename}",
            Body=b"idontcare",
        )

    # And: A FileValidator instance
    validator = file_validator

    # And: a raw data catalog for that large file inventory
    raw_data_catalog = {
        "name": "dataset-client",
        "tables": [{"name": "TABLE1", "glob": "file*.csv", "group": "default"}],
    }
    validator._yaml_catalog = raw_data_catalog

    # When: executing
    validator.execute({})

    # Then: the inventory summary should be written
    body = (
        s3_client.get_object(
            Bucket="test-bucket",
            Key="etl/dataset/revision/raw-inventory/inventory_summary.csv",
        )
        .get("Body")
        .read()
    )
    assert body is not None

    # And: the inventory summary should have the correct number of lines
    summary_iter = csv.reader(body.decode("utf-8").splitlines())
    summary = list(summary_iter)
    assert len(summary) == 10001
    assert summary[0] == ["key", "size", "last_modified", "table", "glob", "ignored", "lookup_table"]
    summary_by_file_name = {
        row[0]: row for row in summary[1:]
    }  # index summary by filename
    for i in range(10000):
        assert f"file{i}.csv" in summary_by_file_name.keys(), (
            f"Missing file file{i}.csv in inventory summary"
        )
        row = summary_by_file_name[f"file{i}.csv"]
        assert row[0] == f"file{i}.csv"  # key column
        assert row[3] == "TABLE1"  # table column
        assert row[4] == "file*.csv"  # glob column
        assert row[5] == "False"  # ignored column
        assert row[6] == "False"  # lookup_table column


def _assert_existing_bucket(s3_client: S3Client, bucket_name: str):
    assert bucket_name in [b["Name"] for b in s3_client.list_buckets()["Buckets"]]


def test_lookup_table_functionality(file_validator: FileValidator):
    """Test lookup table functionality."""
    validator = file_validator
    
    # Test is_lookup_table method
    validator._lookup_tables = [
        {"name": "MED", "path": "med.csv"},
        {"name": "LOOKUP", "path": "lookup.csv"}
    ]
    
    assert validator.is_lookup_table("med.csv") is True
    assert validator.is_lookup_table("lookup.csv") is True
    assert validator.is_lookup_table("other.csv") is False
    
    # Test that lookup tables are excluded from validation
    validator._expected_files = ["abc*.csv"]
    validator._ignored_globs = []
    validator._inventory_files = ["abcd.csv", "med.csv", "lookup.csv"]
    validator._file_options = {}
    
    # Should not raise exception because med.csv and lookup.csv are lookup tables
    assert validator.validate() is None


def test_lookup_table_inventory_summary(file_validator: FileValidator):
    """Test that lookup tables are properly marked in inventory summary."""
    validator = file_validator
    
    # Mock the fs property to avoid S3 operations
    mock_fs = Mock()
    mock_file = Mock()
    written_content = []
    
    def mock_write(content):
        written_content.append(content)
    
    mock_file.write = mock_write
    mock_file.__enter__ = Mock(return_value=mock_file)
    mock_file.__exit__ = Mock(return_value=None)
    mock_fs.open = Mock(return_value=mock_file)
    validator._fs = mock_fs
    
    validator._inventory = [
        ("abcd.csv", 123, "10-01-2024"),
        ("med.csv", 100, "12-01-2024"),
        ("lookup.csv", 200, "12-02-2024")
    ]
    validator._yaml_catalog = {
        "tables": [
            {"name": "ABC", "glob": "abc*.csv", "group": "default"}
        ],
        "ignored_files": [],
        "lookup_tables": [
            {"name": "MED", "path": "med.csv"},
            {"name": "LOOKUP", "path": "lookup.csv"}
        ]
    }
    
    # Write the inventory summary
    validator.write_inventory_summary()
    
    # Parse CSV content
    csv_content = ''.join(written_content)
    lines = csv_content.strip().split('\n')
    csv_data = [line.strip().split(',') for line in lines]
    
    # Check that lookup tables are marked correctly
    assert len(csv_data) == 4  # Header + 3 rows
    
    # Find the lookup table rows
    lookup_rows = [row for row in csv_data[1:] if row[6] == "True"]  # lookup_table column (index 6)
    assert len(lookup_rows) == 2
    
    # Check that regular files are not marked as lookup tables
    regular_rows = [row for row in csv_data[1:] if row[6] == "False"]
    assert len(regular_rows) == 1
    assert regular_rows[0][0] == "abcd.csv"  # First column should be the filename
