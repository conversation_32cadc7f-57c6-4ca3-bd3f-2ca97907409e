from collections.abc import Generator
from io import BytesIO

from unittest.mock import patch

from mypy_boto3_s3 import S3Client
import pytest


@pytest.fixture
def bucket(s3_client: S3Client) -> Generator[str, None, None]:
    """Create a test S3 bucket for data processing tests."""
    bucket_name = "test-bucket"
    s3_client.create_bucket(Bucket=bucket_name)
    yield bucket_name


@patch("aetion.adip.airflow.integrations.google.google_drive_reader.GDriveReader")
def test_copy_spec_from_gdrive_to_s3(
    mock_gdrive_reader, s3_client: S3Client, bucket: str
):
    """
    Test complete workflow when reading spec from Google Drive and uploading to S3.

    Verifies the end-to-end functionality when no data specification
    exists in S3 and the operator needs to read from Google Drive
    and upload to the dataset artifacts path.
    """

    # Given: a copy_spec operator with a mock Google Drive reader
    from dags.operators.spec_copy_operator import SpecCopyOperator

    copy_spec = SpecCopyOperator(
        global_artifacts_path=f"s3://{bucket}/global/artifacts/",
        dataset_artifacts_path=f"s3://{bucket}/etl/dataset/revision/artifacts/",
        client="client",
        dataset="dataset",
        revision="revision",
        use_copy_spec=True,
        task_id="copy_spec",
    )

    # And: No data data specification in the global artifacts path
    assert (
        s3_client.list_objects_v2(Bucket=bucket, Prefix="global/artifacts/")["KeyCount"]
        == 0
    )

    # And: the mock GDriveReader is set up to return a BytesIO object
    mock_gdrive_reader.return_value.__enter__.return_value = BytesIO(b"test data")

    # When: the operator is executed
    copy_spec.execute({})

    # Then: the data specification should be copied to the dataset artifacts path
    actual_data = s3_client.get_object(
        Bucket=bucket, Key="etl/dataset/revision/artifacts/data_specification.xlsx"
    )["Body"].read()
    assert actual_data == b"test data"


def test_copy_spec_from_s3_to_s3(s3_client: S3Client, bucket: str):
    """
    Test complete workflow when copying spec from S3 global to dataset path.

    Verifies the end-to-end functionality when a data specification
    already exists in the global artifacts path and needs to be copied
    to the dataset artifacts path.
    """
    # Given: A SpecCopyOperator configured for full workflow
    from dags.operators.spec_copy_operator import SpecCopyOperator

    operator = SpecCopyOperator(
        global_artifacts_path=f"s3://{bucket}/global/artifacts/",
        dataset_artifacts_path=f"s3://{bucket}/dataset/artifacts/",
        client="client",
        dataset="dataset",
        revision="revision",
        use_copy_spec=True,
        task_id="test",
    )

    # And: A data specification exists in the global artifacts path
    global_spec_key = (
        "global/artifacts/specs/data specification - client - dataset - revision.xlsx"
    )
    original_spec_data = b"spec data from s3"
    s3_client.put_object(Bucket=bucket, Key=global_spec_key, Body=original_spec_data)

    # When: Executing the complete workflow
    operator.execute({})

    # Then: The spec should be copied to the dataset artifacts path
    dataset_spec_key = "dataset/artifacts/data_specification.xlsx"
    copied_data = s3_client.get_object(Bucket=bucket, Key=dataset_spec_key)[
        "Body"
    ].read()
    assert copied_data == original_spec_data

    # And: Original spec should still exist in global path
    original_data = s3_client.get_object(Bucket=bucket, Key=global_spec_key)[
        "Body"
    ].read()
    assert original_data == original_spec_data
