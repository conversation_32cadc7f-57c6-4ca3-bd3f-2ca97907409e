from collections.abc import Generator
from io import BytesIO

from unittest.mock import patch

from mypy_boto3_s3 import S3Client
import pytest


@pytest.fixture
def bucket(s3_client: S3Client) -> Generator[str, None, None]:
    """Create a test S3 bucket for data processing tests."""
    bucket_name = "test-bucket"
    s3_client.create_bucket(Bucket=bucket_name)
    yield bucket_name


@patch("aetion.adip.airflow.integrations.google.google_drive_reader.GDriveReader")
def test_copy_spec_from_gdrive_to_s3(mock_gdrive_reader, s3_client: S3Client, bucket: str):

    # Given: a copy_spec operator with a mock Google Drive reader
    from dags.operators.spec_copy_operator import SpecCopyOperator
    copy_spec = SpecCopyOperator(
        global_artifacts_path=f"s3://{bucket}/global/artifacts/",
        dataset_artifacts_path=f"s3://{bucket}/etl/dataset/revision/artifacts/",
        client="client",
        dataset="dataset",
        revision="revision",
        use_copy_spec=True,
        task_id="copy_spec",
    )

    # And: No data data specification in the global artifacts path
    assert s3_client.list_objects_v2(Bucket=bucket, Prefix="global/artifacts/")["KeyCount"] == 0

    # And: the mock GDriveReader is set up to return a BytesIO object
    mock_gdrive_reader.return_value.__enter__.return_value = BytesIO(b"test data")

    # When: the operator is executed
    copy_spec.execute({})

    # Then: the data specification should be copied to the dataset artifacts path
    actual_data = s3_client.get_object(
        Bucket=bucket,
        Key="etl/dataset/revision/artifacts/data_specification.xlsx")['Body'].read()
    assert actual_data == b"test data"

