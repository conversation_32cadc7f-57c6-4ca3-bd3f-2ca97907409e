import logging
import os
from pathlib import Path
import tempfile
from io import Bytes<PERSON>
from typing import Any

import boto3
import pytest
from aetion.adip.airflow.integrations.s3.s3_support import AetionS3FileSystem
from moto import mock_s3
from mypy_boto3_s3 import S3Client


@pytest.fixture
def aws_credentials(monkeypatch):
    """Mocked AWS Credentials for moto with proper isolation."""
    # Set AWS environment variables for the test duration
    monkeypatch.setenv("AWS_ACCESS_KEY_ID", "testing")
    monkeypatch.setenv("AWS_SECRET_ACCESS_KEY", "testing")
    monkeypatch.setenv("AWS_SECURITY_TOKEN", "testing")
    monkeypatch.setenv("AWS_SESSION_TOKEN", "testing")
    monkeypatch.setenv("AWS_DEFAULT_REGION", "us-east-1")


@pytest.fixture
def s3_client(aws_credentials):
    """Create a mocked S3 client."""
    with mock_s3():
        yield boto3.client("s3", region_name="us-east-1")


@pytest.fixture
def s3_filesystem(aws_credentials):
    """Create AetionS3FileSystem instance with real S3Hook using moto."""
    with mock_s3():
        fs = AetionS3FileSystem()
        yield fs


@pytest.fixture
def bucket(s3_client):
    """Setup test bucket with sample data."""
    bucket_name = "test-bucket"
    s3_client.create_bucket(Bucket=bucket_name)

    # Add some test files
    s3_client.put_object(Bucket=bucket_name, Key="test-file.txt", Body=b"Hello World")
    s3_client.put_object(
        Bucket=bucket_name, Key="folder/nested-file.txt", Body=b"Nested content"
    )
    s3_client.put_object(
        Bucket=bucket_name, Key="folder/subfolder/deep-file.txt", Body=b"Deep content"
    )

    return bucket_name


class TestAetionS3FileSystem:
    def test_init(self):
        """Test initialization of AetionS3FileSystem."""
        fs = AetionS3FileSystem()
        assert fs.aws_conn_id == "aws_default"
        assert fs._hook is None

        fs_custom = AetionS3FileSystem(aws_conn_id="custom_conn")
        assert fs_custom.aws_conn_id == "custom_conn"

    def test_hook_property(self, s3_filesystem: AetionS3FileSystem):
        """Test hook property creates S3Hook instance."""
        hook = s3_filesystem.hook
        assert hook is not None
        # Verify it's the same instance when called again
        assert hook is s3_filesystem.hook

    def test_client_property(self, s3_filesystem: AetionS3FileSystem):
        """Test client property returns boto3 S3 client."""
        assert s3_filesystem.client is not None

    @pytest.mark.parametrize(
        "s3_url",
        [
            pytest.param(
                "s3://my-bucket/path/to/file.txt",
                id="s3://",
            ),
            pytest.param(
                "s3a://my-bucket/path/to/file.txt",
                id="s3a://",
            ),
            pytest.param(
                "my-bucket/path/to/file.txt",
                id="bucket/key",
            ),
        ],
    )
    def test_parse_s3_path_valid(
        self,
        s3_filesystem: AetionS3FileSystem,
        s3_url: str,
    ):
        """Test parsing valid S3 paths."""

        bucket, key = s3_filesystem._parse_s3_path(s3_url)
        assert bucket == "my-bucket"
        assert key == "path/to/file.txt"

    def test_parse_s3_path_invalid(self, s3_filesystem: AetionS3FileSystem):
        """Test parsing invalid S3 paths."""
        with pytest.raises(ValueError):
            s3_filesystem._parse_s3_path("invalid-path")

    def test_put_bytesio(
        self,
        s3_filesystem: AetionS3FileSystem,
        bucket: str,
        s3_client: S3Client,
    ):
        """Test uploading BytesIO data."""

        # Given: an existing bucket

        # And: some data in BytesIO
        data = BytesIO(b"BytesIO content")

        # And: an S3 path to put the data
        test_key = "bytesio-test.txt"
        s3_path = f"s3://{bucket}/{test_key}"

        # When: put the BytesIO data
        s3_filesystem.put(data, s3_path)

        # Then: the content should be uploaded correctly
        assert (
            s3_client.get_object(Bucket=bucket, Key=test_key)["Body"].read()
            == b"BytesIO content"
        )

    def test_put_file_path(self, s3_filesystem: AetionS3FileSystem, bucket):
        """Test uploading from file path."""
        test_key = "file-path-test.txt"
        s3_path = f"s3://{bucket}/{test_key}"

        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp.write(b"File path content")
            tmp_path = tmp.name

        try:
            s3_filesystem.put(tmp_path, s3_path)

            # Verify content was uploaded correctly
            content = s3_filesystem.cat(s3_path, as_bytes=True)
            assert content == b"File path content"
        finally:
            os.unlink(tmp_path)

    @pytest.mark.parametrize(
        "data",
        [
            pytest.param(None, id="None"),
            pytest.param(123, id="int"),
            pytest.param(123.456, id="float"),
            pytest.param(True, id="bool"),
        ],
    )
    def test_put_non_valid_data(
        self, s3_filesystem: AetionS3FileSystem, bucket: str, data: Any
    ):
        """Test uploading from non valid data should throw TypeError."""

        # Given: an existing bucket

        # When: put invalid data in some s3 path
        with pytest.raises(TypeError):
            # Then: it should throw TypeError
            s3_filesystem.put(data, f"s3://{bucket}/path/to/file.txt")

    def test_rm_single_file(self, s3_filesystem: AetionS3FileSystem, bucket):
        """Test deleting single file."""
        test_key = "to-be-deleted.txt"
        s3_path = f"s3://{bucket}/{test_key}"

        # Create a file to delete
        s3_filesystem.put(b"Delete me", s3_path)

        # Verify file exists
        assert s3_filesystem.exists(s3_path)

        # Delete the file
        s3_filesystem.rm(s3_path, recursive=False)

        # Verify file no longer exists
        assert not s3_filesystem.exists(s3_path)

    def test_rm_recursive(
        self, s3_filesystem: AetionS3FileSystem, bucket, s3_client
    ):
        """Test recursive deletion."""
        prefix = "folder-to-delete/"

        # Create multiple files in the folder
        for i in range(3):
            key = f"{prefix}file{i}.txt"
            s3_client.put_object(Bucket=bucket, Key=key, Body=f"Content {i}".encode())

        # Verify files exist
        s3_path = f"s3://{bucket}/{prefix}"
        assert s3_filesystem.exists(f"s3://{bucket}/{prefix}file0.txt")

        # Delete the folder recursively
        s3_filesystem.rm(s3_path, recursive=True)

        # Verify files no longer exist
        for i in range(3):
            assert not s3_filesystem.exists(f"s3://{bucket}/{prefix}file{i}.txt")

    def test_rm_recursive_but_no_file_found_to_delete(
        self,
        s3_filesystem: AetionS3FileSystem,
        bucket: str,
        s3_client: S3Client,
        caplog,
    ):
        """Test recursive deletion but no file found."""

        # Given: an existing bucket

        # And: a prefix that doesn't have any files
        prefix = "folder-to-delete/"
        s3_path = f"s3://{bucket}/{prefix}"
        key_count = s3_client.list_objects_v2(Bucket=bucket, Prefix=prefix)["KeyCount"]
        assert key_count == 0, (
            f"Expected no keys under {s3_path}, but found {key_count} keys"
        )

        # When: Delete all the files under the prefix
        with caplog.at_level(logging.INFO):
            s3_filesystem.rm(s3_path, recursive=True)

        # Then: a warning should be logged
        assert f"No objects found to delete under {s3_path}" in caplog.text

    def test_exists(self, s3_filesystem: AetionS3FileSystem, bucket):
        """Test exists method."""

        # Test existing file
        assert s3_filesystem.exists(f"s3://{bucket}/test-file.txt")

        # Test non-existing file
        assert not s3_filesystem.exists(f"s3://{bucket}/nonexistent.txt")

    def test_info(self, s3_filesystem: AetionS3FileSystem, bucket):
        """Test getting object metadata."""
        s3_path = f"s3://{bucket}/test-file.txt"

        info = s3_filesystem.info(s3_path)
        assert info["name"] == s3_path
        assert info["size"] == 11  # "Hello World" is 11 bytes
        assert info["type"] == "file"
        assert info["last_modified"] is not None

    def test_info_not_found(self, s3_filesystem: AetionS3FileSystem, bucket):
        """Test info method when object not found."""
        s3_path = f"s3://{bucket}/nonexistent.txt"

        with pytest.raises(FileNotFoundError):
            s3_filesystem.info(s3_path)

    @pytest.mark.parametrize("version_id", [None, "123"])
    def test_split_path(self, s3_filesystem: AetionS3FileSystem, version_id: str):
        """Test splitting S3 URL."""
        bucket = "my-bucket"
        key = "path/to/file.csv"
        s3_path = f"s3://{bucket}/{key}"
        if version_id:
            s3_path += f"?versionId={version_id}"
        bucket_parsed, key_parsed, version_id_parsed = s3_filesystem.split_path(s3_path)
        assert bucket == bucket_parsed
        assert key == key_parsed
        assert version_id == version_id_parsed

    def test_split_path_invalid(self, s3_filesystem: AetionS3FileSystem):
        """Test split_path with invalid URL."""
        with pytest.raises(ValueError):
            s3_filesystem.split_path("invalid://bucket/key")

    def test_upload_file(self, s3_filesystem: AetionS3FileSystem, bucket):
        """Test uploading a single file."""
        s3_path = f"s3://{bucket}/uploaded-file.txt"

        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp.write(b"Upload file content")
            tmp_path = tmp.name

        try:
            s3_filesystem.upload(tmp_path, s3_path)

            # Verify file was uploaded
            assert s3_filesystem.exists(s3_path)
            content = s3_filesystem.cat(s3_path, as_bytes=True)
            assert content == b"Upload file content"
        finally:
            os.unlink(tmp_path)

    def test_upload_directory(
        self, s3_filesystem: AetionS3FileSystem, bucket
    ):
        """Test uploading a directory."""
        s3_prefix = f"s3://{bucket}/dir-upload/"

        with tempfile.TemporaryDirectory() as tmp_dir:
            # Create test files
            file1_path = os.path.join(tmp_dir, "file1.txt")
            subdir = os.path.join(tmp_dir, "subdir")
            os.makedirs(subdir)
            file2_path = os.path.join(subdir, "file2.txt")

            with open(file1_path, "w") as f:
                f.write("content1")
            with open(file2_path, "w") as f:
                f.write("content2")

            s3_filesystem.upload(tmp_dir, s3_prefix)

            # Verify files were uploaded
            assert s3_filesystem.exists(f"{s3_prefix}file1.txt")
            assert s3_filesystem.exists(f"{s3_prefix}subdir/file2.txt")

            # Verify content
            assert (
                s3_filesystem.cat(f"{s3_prefix}file1.txt", as_bytes=False) == "content1"
            )
            assert (
                s3_filesystem.cat(f"{s3_prefix}subdir/file2.txt", as_bytes=False)
                == "content2"
            )

    def test_listdir(self, s3_filesystem: AetionS3FileSystem, bucket):
        """Test listing directory contents."""
        s3_path = f"s3://{bucket}/folder/"

        # List without detail
        result = s3_filesystem.listdir(s3_path, detail=False)
        assert len(result) >= 1
        assert any("nested-file.txt" in item for item in result)

        # List with detail
        result_detail = s3_filesystem.listdir(s3_path, detail=True)
        assert len(result_detail) >= 1
        assert all(isinstance(item, dict) for item in result_detail)

    def test_open_read_mode(self, s3_filesystem: AetionS3FileSystem, bucket):
        """Test opening file in read mode."""
        s3_path = f"s3://{bucket}/test-file.txt"

        with s3_filesystem.open(s3_path, mode="r") as f:
            content = f.read()
            assert content == "Hello World"

    def test_open_write_mode(
        self, s3_filesystem: AetionS3FileSystem, bucket
    ):
        """Test opening file in write mode."""
        s3_path = f"s3://{bucket}/write-test.txt"

        with s3_filesystem.open(s3_path, mode="w") as f:
            f.write("Written content")

        # Verify content was written
        assert s3_filesystem.exists(s3_path)
        content = s3_filesystem.cat(s3_path, as_bytes=False)
        assert content == "Written content"

    def test_open_binary_modes(
        self, s3_filesystem: AetionS3FileSystem, bucket
    ):
        """Test opening file in binary modes."""
        s3_path = f"s3://{bucket}/binary-test.bin"

        # Test write binary
        with s3_filesystem.open(s3_path, mode="wb") as f:
            f.write(b"\x00\x01\x02\x03")

        # Test read binary
        with s3_filesystem.open(s3_path, mode="rb") as f:
            content = f.read()
            assert content == b"\x00\x01\x02\x03"

    def test_open_not_implemented(self, s3_filesystem: AetionS3FileSystem):
        """Test opening file with unsupported mode."""
        with pytest.raises(NotImplementedError):
            with s3_filesystem.open("s3://bucket/file.txt", mode="a"):
                pass

    def test_upload_dir_parallel(
        self, s3_filesystem: AetionS3FileSystem, bucket
    ):
        """Test parallel directory upload."""
        s3_prefix = f"s3://{bucket}/parallel-upload/"

        with tempfile.TemporaryDirectory() as tmp_dir:
            # Create multiple test files
            for i in range(5):
                file_path = os.path.join(tmp_dir, f"file{i}.txt")
                with open(file_path, "w") as f:
                    f.write(f"Content {i}")

            s3_filesystem.upload_dir_parallel(tmp_dir, s3_prefix, max_workers=3)

            # Verify files were uploaded
            for i in range(5):
                assert s3_filesystem.exists(f"{s3_prefix}file{i}.txt")
                content = s3_filesystem.cat(f"{s3_prefix}file{i}.txt", as_bytes=False)
                assert content == f"Content {i}"

    def test_read_file_content_s3(
        self, s3_filesystem: AetionS3FileSystem, bucket
    ):
        """Test reading file content from S3."""
        s3_path = f"s3://{bucket}/test-file.txt"

        content = s3_filesystem.read_file_content(s3_path)
        assert content == "Hello World"

    def test_read_file_content_local(self, s3_filesystem: AetionS3FileSystem):
        """Test reading file content from local file."""
        with tempfile.NamedTemporaryFile(mode="w", delete=False) as tmp:
            tmp.write("Local content")
            tmp_path = tmp.name

        try:
            content = s3_filesystem.read_file_content(f"file:{tmp_path}")
            assert content == "Local content"
        finally:
            os.unlink(tmp_path)

    def test_copy_basic(self, s3_filesystem: AetionS3FileSystem, bucket):
        """Test basic file copy."""
        src_path = f"s3://{bucket}/test-file.txt"
        dst_path = f"s3://{bucket}/copied-file.txt"

        s3_filesystem.copy_basic(src_path, dst_path)

        # Verify file was copied
        assert s3_filesystem.exists(dst_path)
        content = s3_filesystem.cat(dst_path, as_bytes=False)
        assert content == "Hello World"

    def test_copy_basic_directory(
        self, s3_filesystem: AetionS3FileSystem, bucket
    ):
        """Test basic directory copy."""
        src_path = f"s3://{bucket}/folder/"
        dst_path = f"s3://{bucket}/folder-copy"

        s3_filesystem.copy_basic_directory(src_path, dst_path)

        # Verify directory was copied
        assert s3_filesystem.exists(f"{dst_path}/nested-file.txt")

    def test_walk_all(
        self, s3_filesystem: AetionS3FileSystem, bucket, s3_client: S3Client
    ):
        """Test that walk_all method returns all objects under the given path."""
        s3_path = f"s3://{bucket}/data/"

        keys = [
                "data/level1/file1.txt",
                "data/level1/file2.txt",
                "data/level1/subdir/file3.txt",
                "data/level2/file4.txt",
        ]

        # Create additional nested structure for comprehensive testing
        for key in keys:
            s3_client.put_object(Bucket=bucket, Key=key, Body=b"whatever")

        results = list(s3_filesystem.walk_all(s3_path))
        actual_keys = [i[0] for i in results]
        assert len(results) == len(keys)
        assert set(actual_keys) == set(keys)


    def test_walk_basic_functionality(
        self, s3_filesystem: AetionS3FileSystem, bucket, s3_client
    ):
        """Test basic walk functionality with nested directory structure."""

        # Create additional nested structure for comprehensive testing
        s3_client.put_object(
            Bucket=bucket, Key="data/level1/file1.txt", Body=b"content1"
        )
        s3_client.put_object(
            Bucket=bucket, Key="data/level1/file2.txt", Body=b"content2"
        )
        s3_client.put_object(
            Bucket=bucket, Key="data/level1/subdir/file3.txt", Body=b"content3"
        )
        s3_client.put_object(
            Bucket=bucket, Key="data/level2/file4.txt", Body=b"content4"
        )

        s3_path = f"s3://{bucket}/data/"
        results = list(s3_filesystem.walk(s3_path))

        # Should yield tuples of (dirpath, dirnames, filenames)
        assert len(results) >= 1
        for dirpath, dirnames, filenames in results:
            assert isinstance(dirpath, str)
            assert isinstance(dirnames, list)
            assert isinstance(filenames, list)

        expected = [
            ("s3://test-bucket/data", ["level1", "level2"], []),
            ("s3://test-bucket/data/level1", ["subdir"], ["file1.txt", "file2.txt"]),
            ("s3://test-bucket/data/level1/subdir", [], ["file3.txt"]),
            ("s3://test-bucket/data/level2", [], ["file4.txt"]),
        ]
        assert results == expected

    def test_walk_maxdepth_zero(
        self, s3_filesystem: AetionS3FileSystem, bucket, s3_client
    ):
        """Test walk with maxdepth=0 (only current directory)."""

        # Create nested structure
        s3_client.put_object(Bucket=bucket, Key="root/file1.txt", Body=b"content1")
        s3_client.put_object(
            Bucket=bucket, Key="root/subdir/file2.txt", Body=b"content2"
        )

        s3_path = f"s3://{bucket}/root/"
        results = list(s3_filesystem.walk(s3_path, maxdepth=0))

        # Should only return the root directory
        assert len(results) == 1
        dirpath, dirnames, filenames = results[0]
        assert dirpath == f"s3://{bucket}/root"
        assert "file1.txt" in filenames
        assert "subdir" in dirnames

    def test_walk_maxdepth_one(
        self, s3_filesystem: AetionS3FileSystem, bucket, s3_client
    ):
        """Test walk with maxdepth=1 (current + immediate subdirectories)."""

        # Create multi-level structure
        s3_client.put_object(Bucket=bucket, Key="root/file1.txt", Body=b"content1")
        s3_client.put_object(
            Bucket=bucket, Key="root/level1/file2.txt", Body=b"content2"
        )
        s3_client.put_object(
            Bucket=bucket, Key="root/level1/level2/file3.txt", Body=b"content3"
        )

        s3_path = f"s3://{bucket}/root/"
        results = list(s3_filesystem.walk(s3_path, maxdepth=1))

        # Should return root and level1, but not level2
        assert len(results) == 2
        dirpaths = [result[0] for result in results]
        assert f"s3://{bucket}/root" in dirpaths
        assert f"s3://{bucket}/root/level1" in dirpaths
        assert f"s3://{bucket}/root/level1/level2" not in dirpaths

    def test_walk_maxdepth_unlimited(
        self, s3_filesystem: AetionS3FileSystem, bucket, s3_client
    ):
        """Test walk with maxdepth=None (unlimited depth)."""

        # Create deep nested structure
        s3_client.put_object(
            Bucket=bucket,
            Key="deep/level1/level2/level3/file.txt",
            Body=b"deep content",
        )

        s3_path = f"s3://{bucket}/deep/"
        results = list(s3_filesystem.walk(s3_path, maxdepth=None))

        # Should traverse all levels
        dirpaths = [result[0] for result in results]
        assert f"s3://{bucket}/deep" in dirpaths
        assert f"s3://{bucket}/deep/level1" in dirpaths
        assert f"s3://{bucket}/deep/level1/level2" in dirpaths
        assert f"s3://{bucket}/deep/level1/level2/level3" in dirpaths

    def test_walk_recursive_traversal(
        self, s3_filesystem: AetionS3FileSystem, bucket, s3_client
    ):
        """Test recursive traversal through complex directory structure."""

        # Create complex structure
        files_structure = [
            "project/src/main.py",
            "project/src/utils/helper.py",
            "project/tests/test_main.py",
            "project/docs/readme.txt",
            "project/config/settings.json",
        ]

        for file_path in files_structure:
            s3_client.put_object(Bucket=bucket, Key=file_path, Body=b"content")

        s3_path = f"s3://{bucket}/project/"
        results = list(s3_filesystem.walk(s3_path))

        # Verify all directories are visited
        dirpaths = [result[0] for result in results]
        expected_dirs = [
            f"s3://{bucket}/project",
            f"s3://{bucket}/project/src",
            f"s3://{bucket}/project/src/utils",
            f"s3://{bucket}/project/tests",
            f"s3://{bucket}/project/docs",
            f"s3://{bucket}/project/config",
        ]

        for expected_dir in expected_dirs:
            assert expected_dir in dirpaths

    def test_walk_nonexistent_path(
        self, s3_filesystem: AetionS3FileSystem, bucket
    ):
        """Test walk with non-existent path raises FileNotFoundError."""
        s3_path = f"s3://{bucket}/nonexistent/"

        with pytest.raises(FileNotFoundError, match="Path does not exist"):
            list(s3_filesystem.walk(s3_path))

    def test_walk_invalid_path_format(self, s3_filesystem: AetionS3FileSystem):
        """Test walk with invalid path format raises ValueError."""
        with pytest.raises(ValueError, match="Path cannot be empty"):
            list(s3_filesystem.walk(""))

    def test_walk_empty_directory(
        self, s3_filesystem: AetionS3FileSystem, bucket, s3_client
    ):
        """Test walk with empty directories."""

        # Create empty directory by creating a file and then deleting it
        # S3 doesn't have true empty directories, so we test with a directory that has subdirs but no files
        s3_client.put_object(
            Bucket=bucket, Key="empty/subdir/file.txt", Body=b"content"
        )

        s3_path = f"s3://{bucket}/empty/"
        results = list(s3_filesystem.walk(s3_path))

        # Should still traverse the structure
        assert len(results) >= 1
        root_result = next((r for r in results if r[0] == f"s3://{bucket}/empty"), None)
        assert root_result is not None
        dirpath, dirnames, filenames = root_result
        assert "subdir" in dirnames
        assert len(filenames) == 0  # No files in root

    def test_walk_s3_root_path(
        self, s3_filesystem: AetionS3FileSystem, bucket
    ):
        """Test walk with S3 root path (bucket only)."""
        s3_path = f"s3://{bucket}/"

        results = list(s3_filesystem.walk(s3_path))

        # Should work and return bucket contents
        assert len(results) >= 1
        root_result = results[0]
        dirpath, dirnames, filenames = root_result
        assert dirpath == f"s3://{bucket}"

    def test_walk_trailing_slash_handling(
        self, s3_filesystem: AetionS3FileSystem, bucket, s3_client
    ):
        """Test walk handles paths with and without trailing slashes correctly."""

        s3_client.put_object(Bucket=bucket, Key="test-dir/file.txt", Body=b"content")

        # Test both with and without trailing slash
        path_with_slash = f"s3://{bucket}/test-dir/"
        path_without_slash = f"s3://{bucket}/test-dir"

        results_with = list(s3_filesystem.walk(path_with_slash))
        results_without = list(s3_filesystem.walk(path_without_slash))

        # Should produce same results
        assert len(results_with) == len(results_without)
        assert results_with[0][0] == results_without[0][0]

    def test_walk_tuple_structure_compatibility(
        self, s3_filesystem: AetionS3FileSystem, bucket, s3_client
    ):
        """Test walk returns tuples compatible with s3fs interface."""

        # Create test structure
        s3_client.put_object(Bucket=bucket, Key="compat/file1.txt", Body=b"content1")
        s3_client.put_object(
            Bucket=bucket, Key="compat/subdir/file2.txt", Body=b"content2"
        )

        s3_path = f"s3://{bucket}/compat/"
        results = list(s3_filesystem.walk(s3_path))

        # Verify tuple structure matches s3fs expectations
        for result in results:
            assert len(result) == 3  # (dirpath, dirnames, filenames)
            dirpath, dirnames, filenames = result

            # Verify types
            assert isinstance(dirpath, str)
            assert isinstance(dirnames, list)
            assert isinstance(filenames, list)

            # Verify dirpath format
            assert dirpath.startswith(f"s3://{bucket}")

            # Verify names are strings
            assert all(isinstance(name, str) for name in dirnames)
            assert all(isinstance(name, str) for name in filenames)

    def test_walk_error_handling_continues(
        self,
        s3_filesystem: AetionS3FileSystem,
        bucket,
        s3_client,
        monkeypatch,
    ):
        """Test walk continues traversal even if errors occur in subdirectories."""

        # Create test structure
        s3_client.put_object(
            Bucket=bucket, Key="robust/good-dir/file1.txt", Body=b"content1"
        )
        s3_client.put_object(
            Bucket=bucket, Key="robust/another-good/file2.txt", Body=b"content2"
        )

        s3_path = f"s3://{bucket}/robust/"

        # Mock ls method to raise exception for one directory but not others
        original_ls = s3_filesystem.ls

        def mock_ls(path, detail=True, recursive=False):
            if "good-dir" in path:
                raise Exception("Simulated error")
            return original_ls(path, detail, recursive)

        monkeypatch.setattr(s3_filesystem, "ls", mock_ls)

        # Should still yield results for directories that don't error
        results = list(s3_filesystem.walk(s3_path))

        # Should have at least the root directory
        assert len(results) >= 1
        dirpaths = [result[0] for result in results]
        assert f"s3://{bucket}/robust" in dirpaths

    def test_cat_binary_data(
        self, s3_filesystem: AetionS3FileSystem, bucket
    ):
        """Test cat method with binary data (e.g., compressed files)."""
        test_key = "binary-data.gz"
        # Simulate gzip-compressed data (starts with 0x1f 0x8b magic number)
        binary_content = b"\x1f\x8b\x08\x00\x00\x00\x00\x00\x00\xff\x01\x05\x00\xfa\xffhello\x86\xa6\x10\x36\x05\x00\x00\x00"
        s3_path = f"s3://{bucket}/{test_key}"

        # Upload binary content
        s3_filesystem.put(binary_content, s3_path)

        # Test cat as bytes - should return raw binary data
        content_bytes = s3_filesystem.cat(s3_path, as_bytes=True)
        assert content_bytes == binary_content
        assert isinstance(content_bytes, bytes)

        # Test that cat as string would fail with UnicodeDecodeError
        with pytest.raises(UnicodeDecodeError):
            s3_filesystem.cat(s3_path, as_bytes=False)

    def test_cat_non_binary_chinese(
        self,
        s3_filesystem: AetionS3FileSystem,
        bucket: str,
        s3_client: S3Client,
    ):
        """Test cat method with non-binary data encode with non-UTF-8 characters ."""
        # Given: an existing bucket

        # And: some Chinese text encoded with GBK in some s3 path
        test_key = "chinese-text.txt"
        s3_path = f"s3://{bucket}/{test_key}"
        content = "你好，世界！"
        s3_client.put_object(Bucket=bucket, Key=test_key, Body=content.encode("gbk"))

        # When: I cat the content back as bytes=False and encoding=gbk
        actual_content = s3_filesystem.cat(s3_path, as_bytes=False, encoding="gbk")

        # Then: the returned content is a string
        assert isinstance(actual_content, str)

        # And: it is decoded correctly
        assert actual_content == content

    @pytest.mark.parametrize(
        "target_path",
        [
            pytest.param("bar/file1.csv", id="file"),
            pytest.param("bar/", id="target_folder"),
            pytest.param("bar/another_file_name.csv", id="changed_file_name"),

        ])
    def test_get_single_file(self,
                             s3_filesystem: AetionS3FileSystem,
                             bucket: str,
                             s3_client: S3Client,
                             tmp_path: Path,
                             target_path: str,
                             ):
        """Test get a s3 object and download into the local filesystem"""

        # Given: an existing s3 object
        s3_client.put_object(Bucket=bucket, Key="foo/bar/file1.csv", Body=b"idontcare")

        # And: the local target path
        target_full_parth = tmp_path / target_path

        # When: I get the s3 object into the local filesystem
        s3_filesystem.get(f's3://{bucket}/foo/bar/file1.csv', target_full_parth.absolute().as_posix())

        # Then: the local file exists
        assert os.path.exists(target_full_parth)

        # And: the content is download correctly
        with open(target_full_parth, "rb") as f:
            assert f.read() == b"idontcare"


    def test_get_recursive(self, s3_filesystem: AetionS3FileSystem, bucket: str, s3_client: S3Client, tmp_path: Path):
        """Test get recursively a s3 prefix and download into a subfolder of the local filesystem"""

        # Given: an existing s3 prefix with multiple objects
        s3_client.put_object(Bucket=bucket, Key="foo/bar/file1.csv", Body=b"idontcare")
        s3_client.put_object(Bucket=bucket, Key="foo/bar/file2.csv", Body=b"idontcare")
        s3_client.put_object(Bucket=bucket, Key="foo/bar/file3.csv", Body=b"idontcare")
        s3_client.put_object(Bucket=bucket, Key="foo/bar/nested/file4.csv", Body=b"idontcare")
        s3_client.put_object(Bucket=bucket, Key="foo/bar/nested/file5.csv", Body=b"idontcare")

        # And: the local target path
        target_full_path = tmp_path / "localsubfolder"

        # And: the local target folder might not exist
        assert not os.path.exists(target_full_path)
        #target_full_path.mkdir(parents=True, exist_ok=True)

        # When: I get the s3 prefix into the local filesystem
        s3_filesystem.get(f's3://{bucket}/foo/bar/', target_full_path.absolute().as_posix(), recursive=True)

        # Then: the local files exist
        assert os.path.exists(target_full_path / "file1.csv")
        assert os.path.exists(target_full_path / "file2.csv")
        assert os.path.exists(target_full_path / "file3.csv")
        assert os.path.exists(target_full_path / "nested" / "file4.csv")
        assert os.path.exists(target_full_path / "nested" / "file5.csv")

        # And: the content is download correctly
        assert (target_full_path / "file1.csv").read_bytes() == b"idontcare"
        assert (target_full_path / "file2.csv").read_bytes() == b"idontcare"
        assert (target_full_path / "file3.csv").read_bytes() == b"idontcare"
        assert (target_full_path / "nested" / "file4.csv").read_bytes() == b"idontcare"
        assert (target_full_path / "nested" / "file5.csv").read_bytes() == b"idontcare"

