import json
from unittest.mock import MagicMock

from googleapiclient.errors import HttpError

from dags.operators.file_copy_operator import <PERSON><PERSON><PERSON><PERSON><PERSON>er


def mock_http_error():
    error_reason = "fileNotExportable"
    mock_response = MagicMock()
    mock_response.reason.return_value = ""
    error_content = json.dumps({
        "error": {
            "code": 403,
            "message": error_reason,
            "details": [{"reason": error_reason}]
        }
    }).encode('utf-8')
    return HttpError(resp=mock_response,content=error_content)

def test_file_not_exportable_error(mocker):
    mock_service = MagicMock()

    # mock GDriveReader.iter_gdrive()
    mock_files = {"files": [{"id": "test_id", "name": "test_filename.xlsx"}]}
    mock_service.files.return_value.list.return_value.execute.return_value = mock_files

    # mock fileNotExportable error for service.files().export(...)
    mock_service.files.return_value.export.side_effect = mock_http_error()

    # mock return data for service.files().get_media(...)
    mock_service.files.return_value.get_media.return_value.execute.return_value = b'test data'

    mocker.patch("dags.operators.file_copy_operator.build", return_value=mock_service)

    # accessing an attribute is necessary to trigger @lazy execution
    GDriveReader(filename="test_filename").default_mimetype

    mock_service.files.return_value.export.assert_called_once()
    mock_service.files.return_value.get_media.assert_called_once()
