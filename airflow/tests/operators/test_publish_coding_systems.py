import json
import tempfile
from pathlib import Path
from unittest import mock
from unittest.mock import Mock, patch, MagicMock

import pytest
from airflow.exceptions import AirflowFailException, AirflowException

from aetion.adip.airflow.integrations.s3.s3_support import AetionS3FileSystem
from dags.operators.publish_coding_systems import (
    PublishCodingSystems,
    CodingSystemAttribute,
    CodingSystemBaseResource,
    CodingSystemInstanceParams,
    CodingSystemInstanceResource
)
from dbc_utilities.dbc_validator.rdc import RDCEntry, CodingSystem


class TestCodingSystemDataclasses:
    """Test the dataclasses used for coding system structures."""
    
    def test_coding_system_attribute(self):
        """Test CodingSystemAttribute dataclass."""
        attr = CodingSystemAttribute(
            dataType="CODE",
            name="CODE",
            description="Code column"
        )
        assert attr.dataType == "CODE"
        assert attr.name == "CODE"
        assert attr.description == "Code column"
    
    def test_coding_system_base_resource(self):
        """Test CodingSystemBaseResource dataclass."""
        attributes = [
            CodingSystemAttribute(dataType="CODE", name="CODE", description="Code"),
            CodingSystemAttribute(dataType="CODE", name="DESCRIPTION", description="Description")
        ]
        
        base_resource = CodingSystemBaseResource(
            name="test_coding_system",
            description="Test coding system",
            longDescription="Long description of test coding system",
            caseSensitiveCode=False,
            attributes=attributes
        )
        
        assert base_resource.name == "test_coding_system"
        assert base_resource.description == "Test coding system"
        assert base_resource.caseSensitiveCode is False
        assert len(base_resource.attributes) == 2
    
    def test_coding_system_instance_params(self):
        """Test CodingSystemInstanceParams dataclass."""
        params = CodingSystemInstanceParams(
            filename="test.csv.gz",
            codeCsvCol="code",
            descCsvCol="description",
            attrCsvCols={"attr1": "column1", "attr2": "column2"}
        )
        
        assert params.filename == "test.csv.gz"
        assert params.codeCsvCol == "code"
        assert params.descCsvCol == "description"
        assert params.attrCsvCols == {"attr1": "column1", "attr2": "column2"}
    
    def test_coding_system_instance_resource(self):
        """Test CodingSystemInstanceResource dataclass."""
        params = CodingSystemInstanceParams(
            filename="test.csv.gz",
            codeCsvCol="code",
            descCsvCol="description"
        )
        
        instance_resource = CodingSystemInstanceResource(
            name="test_coding_system_rev1",
            codingSystem="test_coding_system",
            date="2024-01-01",
            params=params
        )
        
        assert instance_resource.name == "test_coding_system_rev1"
        assert instance_resource.codingSystem == "test_coding_system"
        assert instance_resource.date == "2024-01-01"
        assert instance_resource.params == params


class TestPublishCodingSystems:
    """Test the PublishCodingSystems operator."""
    
    @pytest.fixture
    def operator_params(self):
        """Common parameters for the operator."""
        return {
            'task_id': 'test_publish_coding_systems',
            'client': 'test_client',
            'dataset': 'test_dataset',
            'revision': 'rev1',
            'rdc_url_s3': 's3://bucket/rdc.yaml',
            'etl_path': 's3://bucket/etl/test_client/test_dataset/rev1',
            'repo': 'ssh://**************/aetion/repo.git',
            'git_meta_repo': 'ssh://**************/aetion/metadatastore.git',
            'private_key': 'test_private_key',
            'git_default_branch': 'main',
            'branch': 'test_branch',
            'aws_conn_id': 'aws_default'
        }
    
    @pytest.fixture
    def mock_rdc_entry(self):
        """Mock RDC entry with coding systems."""
        rdc_entry = Mock(spec=RDCEntry)
        rdc_entry.coding_systems = [
            Mock(
                name="test_coding_system",
                metadata_folder="test_folder",
                description="Test coding system",
                long_description="Long description",
                case_sensitive=False
            ),
            Mock(
                name="another_coding_system",
                metadata_folder=None,  # Will use name as folder
                description="Another coding system",
                long_description="Another long description",
                case_sensitive=True
            )
        ]
        return rdc_entry
    
    @pytest.fixture
    def mock_fs(self):
        """Mock filesystem for S3 operations."""
        fs = Mock()
        fs.put = Mock()
        return fs
    
    def test_operator_initialization(self, operator_params):
        """Test operator initialization with all parameters."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            assert operator.client == 'test_client'
            assert operator.dataset == 'test_dataset'
            assert operator.revision == 'rev1'
            assert operator.rdc_url_s3 == 's3://bucket/rdc.yaml'
            assert operator.etl_path == 's3://bucket/etl/test_client/test_dataset/rev1'
            assert operator.git_meta_repo == 'ssh://**************/aetion/metadatastore.git'
            assert operator.private_key == 'test_private_key'
            assert operator.git_default_branch == 'main'
            assert operator.aws_conn_id == 'aws_default'
            # Test that fs property returns the mocked filesystem
            assert operator.fs == mock_fs
    
    def test_operator_initialization_without_github_token(self, operator_params):
        """Test operator initialization without GitHub token."""
        
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            assert operator.fs == mock_fs
    
    @patch('dags.operators.publish_coding_systems.get_catalog_from_s3')
    def test_load_raw_data_catalog_success(self, mock_get_catalog, operator_params):
        """Test successful loading of raw data catalog."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            # Override the fs to use our mock
            operator._fs = mock_fs
            
            # Mock the catalog data
            mock_catalog_data = {
                'coding_systems': [
                    {
                        'name': 'test_coding_system',
                        'metadata_folder': 'test_folder',
                        'description': 'Test coding system'
                    }
                ]
            }
            mock_get_catalog.return_value = mock_catalog_data
            
            # Mock RDCEntry.parse_rdc_entry_dict
            with patch('dags.operators.publish_coding_systems.RDCEntry') as mock_rdc_class:
                mock_rdc_entry = Mock()
                # Set up coding_systems as an iterable list of dictionaries
                mock_rdc_entry.coding_systems = [
                    {
                        'name': 'test_coding_system',
                        'metadata_folder': 'test_folder',
                        'description': 'Test coding system',
                        'code_column': 'code',
                        'description_column': 'description'
                    }
                ]
                mock_rdc_class.parse_rdc_entry_dict.return_value = mock_rdc_entry
                
                result = operator._load_raw_data_catalog()
                
                assert result == mock_rdc_entry
                mock_get_catalog.assert_called_once_with(
                    client_name='test_client',
                    dataset_name='test_dataset',
                    rdc_path='s3://bucket/rdc.yaml',
                    fs=mock_fs
                )
    
    @patch('dags.operators.publish_coding_systems.get_catalog_from_s3')
    def test_load_raw_data_catalog_failure(self, mock_get_catalog, operator_params):
        """Test failure when loading raw data catalog."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            mock_get_catalog.side_effect = Exception("S3 error")
            
            with pytest.raises(AirflowFailException, match="Failed to load raw data catalog"):
                operator._load_raw_data_catalog()
    
    def test_compress_file(self, operator_params):
        """Test file compression functionality."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                f.write("test,data\n1,value1\n2,value2\n")
                temp_file = Path(f.name)
            
            try:
                compressed_path = operator._compress_file(temp_file)
                
                assert compressed_path.exists()
                assert compressed_path.suffix == '.gz'
                assert compressed_path.name.endswith('.csv.gz')
                assert not temp_file.exists()  # Original file should be deleted
                
                # Verify the compressed file contains the original data
                import gzip
                with gzip.open(compressed_path, 'rt') as f:
                    content = f.read()
                    assert "test,data" in content
                    assert "1,value1" in content
                    assert "2,value2" in content
                    
            finally:
                if compressed_path.exists():
                    compressed_path.unlink()
    
    def test_read_csv_headers(self, operator_params):
        """Test reading CSV headers."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                f.write("code,description,attr1,attr2\n")
                temp_file = Path(f.name)
            
            try:
                headers = operator._read_csv_headers(temp_file)
                
                assert headers == ['code', 'description', 'attr1', 'attr2']
                
            finally:
                temp_file.unlink()
    
    def test_create_base_resource_file(self, operator_params):
        """Test creating base resource file for new coding system."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            # Use the real CodingSystem class from dbc-utilities
            coding_system = CodingSystem(
                name="test_coding_system",
                metadata_folder="test_folder",
                description="Test coding system",
                long_description="Long description",
                case_sensitive=False,
                code_column="code",
                description_column="description"
            )
            
            csv_columns = ['code', 'description', 'attr1', 'attr2']
            
            with tempfile.TemporaryDirectory() as temp_dir:
                coding_base_path = Path(temp_dir) / "coding_systems" / "test_folder"
                coding_base_path.mkdir(parents=True, exist_ok=True)
                
                operator._create_base_resource_file(coding_system, coding_base_path, csv_columns)
                
                # Check that base folder was created
                base_folder = coding_base_path / "base"
                assert base_folder.exists()
                
                # Check that base resource file was created
                base_resource_file = base_folder / "test_folder.json"
                assert base_resource_file.exists()
                
                # Verify the JSON content
                with open(base_resource_file, 'r') as f:
                    data = json.load(f)
                
                assert data['name'] == 'test_coding_system'
                assert data['description'] == 'Test coding system'
                assert data['longDescription'] == 'Long description'
                assert data['caseSensitiveCode'] is False
                assert len(data['attributes']) == 4  # All columns as attributes
    
    def test_create_instance_resource_file(self, operator_params):
        """Test creating instance resource file."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            # Use the real CodingSystem class from dbc-utilities
            coding_system = CodingSystem(
                name="test_coding_system",
                metadata_folder="test_folder",
                code_column="code",
                description_column="description"
            )
            
            csv_columns = ['code', 'description', 'attr1', 'attr2']
            
            with tempfile.TemporaryDirectory() as temp_dir:
                revision_folder = Path(temp_dir) / "revision"
                revision_folder.mkdir(parents=True, exist_ok=True)
                
                operator._create_instance_resource_file(coding_system, revision_folder, csv_columns)
                
                # Check that instance resource file was created
                instance_file = revision_folder / "test_folder_rev1.json"
                assert instance_file.exists()
                
                # Verify the JSON content
                with open(instance_file, 'r') as f:
                    data = json.load(f)
                
                assert data['name'] == 'test_coding_system_rev1'
                assert data['codingSystem'] == 'test_coding_system'
                assert data['params']['filename'] == 'test_folder_rev1.csv.gz'
                assert data['params']['codeCsvCol'] == 'code'
                assert data['params']['descCsvCol'] == 'description'
                assert 'attrCsvCols' in data['params']
                assert data['params']['attrCsvCols'] == {
                    'ATTR1': 'attr1',
                    'ATTR2': 'attr2',
                    'DESCRIPTION': 'description'
                }
    
    def test_create_base_resource_file_no_metadata_folder(self, operator_params):
        """Test creating base resource file for new coding system without metadata_folder (should use lowercase name)."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            # Use the real CodingSystem class from dbc-utilities - no metadata_folder
            coding_system = CodingSystem(
                name="TestCodingSystem",  # Uppercase name
                description="Test coding system",
                long_description="Long description",
                case_sensitive=False,
                code_column="code",
                description_column="description"
            )
            
            csv_columns = ['code', 'description', 'attr1', 'attr2']
            
            with tempfile.TemporaryDirectory() as temp_dir:
                coding_base_path = Path(temp_dir) / "coding_systems" / "testcodingsystem"  # Should be lowercase
                coding_base_path.mkdir(parents=True, exist_ok=True)
                
                operator._create_base_resource_file(coding_system, coding_base_path, csv_columns)
                
                # Check that base folder was created
                base_folder = coding_base_path / "base"
                assert base_folder.exists()
                
                # Check that base resource file was created with lowercase name
                base_resource_file = base_folder / "testcodingsystem.json"  # Should be lowercase
                assert base_resource_file.exists()
                
                # Verify the JSON content - name should be original case
                with open(base_resource_file, 'r') as f:
                    data = json.load(f)
                
                assert data['name'] == 'TestCodingSystem'  # Original case preserved
                assert data['description'] == 'Test coding system'
                assert data['longDescription'] == 'Long description'
                assert data['caseSensitiveCode'] is False
                assert len(data['attributes']) == 4  # All columns as attributes
    
    def test_create_instance_resource_file_no_metadata_folder(self, operator_params):
        """Test creating instance resource file without metadata_folder (should use lowercase name for files)."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            # Use the real CodingSystem class from dbc-utilities - no metadata_folder
            coding_system = CodingSystem(
                name="TestCodingSystem",  # Uppercase name
                code_column="code",
                description_column="description"
            )
            
            csv_columns = ['code', 'description', 'attr1', 'attr2']
            
            with tempfile.TemporaryDirectory() as temp_dir:
                revision_folder = Path(temp_dir) / "revision"
                revision_folder.mkdir(parents=True, exist_ok=True)
                
                operator._create_instance_resource_file(coding_system, revision_folder, csv_columns)
                
                # Check that instance resource file was created with lowercase name
                instance_file = revision_folder / "testcodingsystem_rev1.json"  # Should be lowercase
                assert instance_file.exists()
                
                # Verify the JSON content - name should use original coding system name
                with open(instance_file, 'r') as f:
                    data = json.load(f)
                
                assert data['name'] == 'TestCodingSystem_rev1'  # Original case + revision
                assert data['codingSystem'] == 'TestCodingSystem'  # Original case
                assert data['params']['filename'] == 'testcodingsystem_rev1.csv.gz'  # Lowercase for filename
                assert data['params']['codeCsvCol'] == 'code'
                assert data['params']['descCsvCol'] == 'description'
                assert 'attrCsvCols' in data['params']
                assert data['params']['attrCsvCols'] == {
                    'ATTR1': 'attr1',
                    'ATTR2': 'attr2',
                    'DESCRIPTION': 'description'
                }
    
    def test_create_instance_resource_file_with_custom_code_column(self, operator_params):
        """Test creating instance resource file with custom code column."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            # Use the real CodingSystem class from dbc-utilities
            # Test with custom code column
            coding_system = CodingSystem(
                name="test_coding_system",
                metadata_folder="test_folder",
                code_column="custom_code",
                description_column="description"
            )
            
            csv_columns = ['custom_code', 'description', 'attr1', 'attr2']
            
            with tempfile.TemporaryDirectory() as temp_dir:
                revision_folder = Path(temp_dir) / "revision"
                revision_folder.mkdir(parents=True, exist_ok=True)
                
                operator._create_instance_resource_file(coding_system, revision_folder, csv_columns)
                
                # Check that instance resource file was created
                instance_file = revision_folder / "test_folder_rev1.json"
                assert instance_file.exists()
                
                # Verify the JSON content
                with open(instance_file, 'r') as f:
                    data = json.load(f)
                
                assert data['name'] == 'test_coding_system_rev1'
                assert data['codingSystem'] == 'test_coding_system'
                assert data['params']['filename'] == 'test_folder_rev1.csv.gz'
                assert data['params']['codeCsvCol'] == 'custom_code'  # Should use custom code column
                assert data['params']['descCsvCol'] == 'description'
                assert 'attrCsvCols' in data['params']
                assert data['params']['attrCsvCols'] == {
                    'ATTR1': 'attr1',
                    'ATTR2': 'attr2',
                    'DESCRIPTION': 'description'
                }
    
    @patch('dags.operators.publish_coding_systems.ADIPGitRepoManager.create_pull_request_from_git_repo')
    def test_create_github_pr_success(self, mock_create_pr, operator_params):
        """Test successful GitHub PR creation."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            # Mock the PR object returned by create_pull_request_from_git_repo
            mock_pr = Mock()
            mock_pr.html_url = 'https://github.com/aetion/metadatastore/pull/123'
            mock_create_pr.return_value = mock_pr
            
            operator._create_github_pr('test_branch', 'Test commit message')
            
            # Verify the method was called with correct parameters
            mock_create_pr.assert_called_once_with(
                title='Test commit message',
                head='test_branch',
                base='main',
                body=f"Automated coding systems update for {operator_params['dataset']}-{operator_params['client']} revision {operator_params['revision']}",
                git_repo=operator_params['git_meta_repo'],
                git_token=operator_params['private_key'],
            )
    
    @patch('dags.operators.publish_coding_systems.ADIPGitRepoManager.create_pull_request_from_git_repo')
    def test_create_github_pr_no_token(self, mock_create_pr, operator_params):
        """Test GitHub PR creation without token."""
        operator_params['private_key'] = None
        
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            operator._create_github_pr('test_branch', 'Test commit message')
            
            # Should not call the method when no token is provided
            mock_create_pr.assert_not_called()
    
    def test_copy_coding_systems_to_s3(self, operator_params):
        """Test copying coding_systems.csv to S3."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            # Override the fs to use our mock
            operator._fs = mock_fs
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create the directory structure and file
                csv_dir = Path(temp_dir) / "metaDataStore" / "versioned" / "coding"
                csv_dir.mkdir(parents=True, exist_ok=True)
                csv_file = csv_dir / "coding_systems.csv"
                csv_file.write_text("test,data\n")
                
                print(f"DEBUG: temp_dir = {temp_dir}")
                print(f"DEBUG: csv_file = {csv_file}")
                print(f"DEBUG: csv_file.exists() = {csv_file.exists()}")
                
                # Also check what path the method will construct
                expected_path = Path(temp_dir) / "metaDataStore" / "versioned" / "coding" / "coding_systems.csv"
                print(f"DEBUG: expected_path = {expected_path}")
                print(f"DEBUG: expected_path.exists() = {expected_path.exists()}")
                
                # Create the file at the expected path as well
                expected_path.parent.mkdir(parents=True, exist_ok=True)
                expected_path.write_text("test,data\n")
                print(f"DEBUG: After creating at expected_path, exists() = {expected_path.exists()}")
                
                operator._copy_coding_systems_to_s3(temp_dir)
                
                # Verify S3 put was called
                mock_fs.put.assert_called_once_with(
                    str(expected_path),
                    's3://bucket/etl/test_client/test_dataset/rev1/artifacts/coding_systems.csv'
                )
    
    def test_copy_coding_systems_to_s3_file_not_found(self, operator_params):
        """Test copying coding_systems.csv when file doesn't exist."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            # Override the fs to use our mock
            operator._fs = mock_fs
            
            with tempfile.TemporaryDirectory() as temp_dir:
                operator._copy_coding_systems_to_s3(temp_dir)
                
                # Should not call S3 put when file doesn't exist
                mock_fs.put.assert_not_called()
    
    @patch('dags.operators.publish_coding_systems.GitHook')
    @patch('dags.operators.publish_coding_systems.get_catalog_from_s3')
    @patch('dags.operators.publish_coding_systems.AetionS3FileSystem')
    @patch('dags.operators.publish_coding_systems.tempfile.TemporaryDirectory')
    def test_publish_coding_systems_no_coding_systems(self, mock_temp_dir, mock_fs_class, mock_get_catalog, mock_git_hook, operator_params):
        """Test publishing when no coding systems are defined."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            # Mock RDC with no coding systems
            mock_catalog_data = {'coding_systems': []}
            mock_get_catalog.return_value = mock_catalog_data
            
            with patch('dags.operators.publish_coding_systems.RDCEntry') as mock_rdc_class:
                mock_rdc_entry = Mock()
                mock_rdc_entry.coding_systems = []
                mock_rdc_class.parse_rdc_entry_dict.return_value = mock_rdc_entry
                
                operator.publish_coding_systems()
                
                # Should not create Git hook or process coding systems
                mock_git_hook.assert_not_called()
    
    @patch('dags.operators.publish_coding_systems.GitHook')
    @patch('dags.operators.publish_coding_systems.get_catalog_from_s3')
    @patch('dags.operators.publish_coding_systems.AetionS3FileSystem')
    @patch('dags.operators.publish_coding_systems.tempfile.TemporaryDirectory')
    def test_publish_coding_systems_with_coding_systems(self, mock_temp_dir, mock_fs_class, mock_get_catalog, mock_git_hook, operator_params, mock_rdc_entry):
        """Test publishing with coding systems defined."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            # Override the fs to use our mock
            operator._fs = mock_fs
            
            # Mock catalog data
            mock_catalog_data = {
                'coding_systems': [
                    {'name': 'test_coding_system', 'metadata_folder': 'test_folder'},
                    {'name': 'another_coding_system', 'metadata_folder': None}
                ]
            }
            mock_get_catalog.return_value = mock_catalog_data
            
            with patch('dags.operators.publish_coding_systems.RDCEntry') as mock_rdc_class:
                mock_rdc_class.parse_rdc_entry_dict.return_value = mock_rdc_entry
                
                # Mock Git hook
                mock_git = Mock()
                mock_git_hook.return_value.__enter__.return_value = mock_git
                mock_git.any_changes.return_value = True
                
                # Mock the _process_coding_system method to avoid S3 operations
                with patch.object(operator, '_process_coding_system') as mock_process:
                    with patch.object(operator, '_update_coding_systems_registry') as mock_update_registry:
                        with patch.object(operator, '_create_github_pr') as mock_create_pr:
                            with patch.object(operator, '_copy_coding_systems_to_s3') as mock_copy_s3:
                                with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
                                    mock_temp_dir.return_value.__enter__.return_value = '/tmp/test'
                                    
                                    operator.publish_coding_systems()
                                    
                                    # Verify Git operations were called
                                    mock_git.clone.assert_called_once()
                                    mock_git.checkout_new.assert_called_once()
                                    mock_git.commit.assert_called_once()
                                    mock_git.push.assert_called_once()
                                    
                                    # Verify our mocked methods were called
                                    assert mock_process.call_count == 2  # One for each coding system
                                    mock_update_registry.assert_called_once()
                                    mock_create_pr.assert_called_once()
                                    mock_copy_s3.assert_called_once()
    
    def test_update_instance_resource_file(self, operator_params):
        """Test updating existing instance resource file."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            with tempfile.TemporaryDirectory() as temp_dir:
                json_file = Path(temp_dir) / "test.json"
                
                # Create initial JSON file
                initial_data = {
                    'name': 'test_coding_system_rev0',
                    'codingSystem': 'test_coding_system',
                    'date': '2024-01-01',
                    'params': {
                        'filename': 'test_folder_rev0.csv.gz',
                        'codeCsvCol': 'code',
                        'descCsvCol': 'description'
                    }
                }
                
                with open(json_file, 'w') as f:
                    json.dump(initial_data, f)
                
                operator._update_instance_resource_file(json_file, 'rev0')
                
                # Verify the file was updated
                with open(json_file, 'r') as f:
                    updated_data = json.load(f)
                
                assert updated_data['name'] == 'test_coding_system_rev1'
                assert updated_data['params']['filename'] == 'test_folder_rev1.csv.gz'
                # Verify that the date was updated to current date (not the old date)
                assert updated_data['date'] != '2024-01-01'
                # Check that the date is in the correct format (YYYY-MM-DD)
                from datetime import datetime
                expected_date = datetime.now().strftime("%Y-%m-%d")
                assert updated_data['date'] == expected_date
    
    def test_validate_csv_columns(self, operator_params):
        """Test CSV column validation."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create CSV file
                csv_file = Path(temp_dir) / "test.csv"
                csv_file.write_text("code,description,attr1\n1,desc1,val1\n2,desc2,val2\n")
                
                # Create instance resource file
                instance_file = Path(temp_dir) / "instance.json"
                instance_data = {
                    'params': {
                        'codeCsvCol': 'code',
                        'descCsvCol': 'description',
                        'attrCsvCols': {'attr1': 'attr1'}
                    }
                }
                
                with open(instance_file, 'w') as f:
                    json.dump(instance_data, f)
                
                # Should not raise exception for valid columns
                operator._validate_csv_columns(csv_file, instance_file)
    
    def test_validate_csv_columns_invalid(self, operator_params):
        """Test CSV column validation with invalid columns."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create CSV file
                csv_file = Path(temp_dir) / "test.csv"
                csv_file.write_text("code,description,attr1\n1,desc1,val1\n2,desc2,val2\n")
                
                # Create instance resource file with invalid column
                instance_file = Path(temp_dir) / "instance.json"
                instance_data = {
                    'params': {
                        'codeCsvCol': 'invalid_column',
                        'descCsvCol': 'description',
                        'attrCsvCols': {'attr1': 'attr1'}
                    }
                }
                
                with open(instance_file, 'w') as f:
                    json.dump(instance_data, f)
                
                # Should raise exception for invalid columns
                with pytest.raises(AirflowException, match="CSV columns do not match instance resource definition"):
                    operator._validate_csv_columns(csv_file, instance_file)
    
    def test_update_existing_coding_system_overwrite_same_revision(self, operator_params):
        """Test updating existing coding system when overwriting the same revision."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create existing coding system structure
                coding_base_path = Path(temp_dir) / "metaDataStore" / "versioned" / "coding" / "test_dataset_test_client" / "test_coding_system"
                coding_base_path.mkdir(parents=True, exist_ok=True)
                
                # Create base folder (as in real metadatastore structure)
                base_folder = coding_base_path / "base"
                base_folder.mkdir(parents=True, exist_ok=True)
                
                # Create existing revision folder (same as operator revision)
                revision_folder = coding_base_path / "rev1"  # Same as operator.revision
                revision_folder.mkdir(parents=True, exist_ok=True)
                
                # Create existing JSON file
                json_file = revision_folder / "test_coding_system_rev1.json"
                initial_data = {
                    'name': 'test_coding_system_rev1',
                    'codingSystem': 'test_coding_system',
                    'date': '2024-01-01',
                    'params': {
                        'filename': 'test_coding_system_rev1.csv.gz',
                        'codeCsvCol': 'code',
                        'descCsvCol': 'description'
                    }
                }
                with open(json_file, 'w') as f:
                    json.dump(initial_data, f)
                
                # Create existing CSV file
                csv_file = revision_folder / "test_coding_system_rev1.csv"
                csv_file.write_text("code,description\n1,desc1\n2,desc2\n")
                
                # Mock the coding system
                coding_system = Mock()
                coding_system.name = "test_coding_system"
                coding_system.metadata_folder = "test_coding_system"
                coding_system.code_column = None  # Use default "code"
                coding_system.description_column = None  # Use default "description"
                
                # Mock S3 download
                mock_fs.get.return_value = None
                
                # Mock the CSV content that would be downloaded
                mock_fs.get.side_effect = lambda s3_path, local_path: Path(local_path).write_text("code,description\n1,desc1\n2,desc2\n")
                
                # Call the method
                operator._update_existing_coding_system(coding_system, coding_base_path)
                
                # Verify that the existing folder was used (not a new one created)
                assert revision_folder.exists()
                assert len(list(coding_base_path.iterdir())) == 2  # Two folders: base and rev1
                
                # Verify that the JSON file was updated with current date
                with open(json_file, 'r') as f:
                    updated_data = json.load(f)
                
                # Verify that the date was updated (not the old date)
                assert updated_data['date'] != '2024-01-01'
                # Check that the date is in the correct format (YYYY-MM-DD)
                import re
                assert re.match(r'\d{4}-\d{2}-\d{2}', updated_data['date']) is not None
                
                # Verify that S3 download was called
                mock_fs.get.assert_called_once()
    
    def test_update_existing_coding_system_new_revision(self, operator_params):
        """Test updating existing coding system when creating a new revision."""
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_s3_fs_class:
            mock_fs = Mock()
            mock_s3_fs_class.return_value = mock_fs
            
            operator = PublishCodingSystems(**operator_params)
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create existing coding system structure
                coding_base_path = Path(temp_dir) / "metaDataStore" / "versioned" / "coding" / "test_dataset_test_client" / "test_coding_system"
                coding_base_path.mkdir(parents=True, exist_ok=True)
                
                # Create base folder (as in real metadatastore structure)
                base_folder = coding_base_path / "base"
                base_folder.mkdir(parents=True, exist_ok=True)
                
                # Create existing revision folder (different from operator revision)
                old_revision_folder = coding_base_path / "rev0"
                old_revision_folder.mkdir(parents=True, exist_ok=True)
                
                # Create existing JSON file
                json_file = old_revision_folder / "test_coding_system_rev0.json"
                initial_data = {
                    'name': 'test_coding_system_rev0',
                    'codingSystem': 'test_coding_system',
                    'date': '2024-01-01',
                    'params': {
                        'filename': 'test_coding_system_rev0.csv.gz',
                        'codeCsvCol': 'code',
                        'descCsvCol': 'description'
                    }
                }
                with open(json_file, 'w') as f:
                    json.dump(initial_data, f)
                
                # Create existing CSV file in the old revision folder
                csv_file = old_revision_folder / "test_coding_system_rev0.csv"
                csv_file.write_text("code,description\n1,desc1\n2,desc2\n")
                
                # Mock the coding system
                coding_system = Mock()
                coding_system.name = "test_coding_system"
                coding_system.metadata_folder = "test_coding_system"
                coding_system.code_column = None  # Use default "code"
                coding_system.description_column = None  # Use default "description"
                
                # Mock S3 download
                mock_fs.get.return_value = None
                
                # Mock the CSV content that would be downloaded
                mock_fs.get.side_effect = lambda s3_path, local_path: Path(local_path).write_text("code,description\n1,desc1\n2,desc2\n")
                
                # Call the method
                operator._update_existing_coding_system(coding_system, coding_base_path)
                
                # Verify that a new revision folder was created
                new_revision_folder = coding_base_path / "rev1"
                assert new_revision_folder.exists()
                assert len(list(coding_base_path.iterdir())) == 3  # Three folders: base, rev0, rev1
                
                # Verify that the JSON file was copied and updated
                new_json_file = new_revision_folder / "test_coding_system_rev1.json"
                assert new_json_file.exists()
                
                with open(new_json_file, 'r') as f:
                    updated_data = json.load(f)
                
                assert updated_data['name'] == 'test_coding_system_rev1'
                assert updated_data['params']['filename'] == 'test_coding_system_rev1.csv.gz'
                
                # Verify that S3 download was called
                mock_fs.get.assert_called_once()
    
    def test_csv_file_not_found_in_s3(self, operator_params):
        """Test handling when CSV file is not found in S3."""
        operator = PublishCodingSystems(**operator_params)
        
        with patch('dags.operators.publish_coding_systems.AetionS3FileSystem') as mock_fs_class:
            mock_fs = Mock()
            mock_fs_class.return_value = mock_fs
            mock_fs.put.side_effect = Exception("File not found")
            
            # Use the test resources directory
            test_resources_dir = Path(__file__).parent.parent / "resources" / "metadatastore"
            
            # The method should not raise an exception, just log a warning
            operator._copy_coding_systems_to_s3(str(test_resources_dir))
            
            # Verify that fs.put was called (even though it failed)
            mock_fs.put.assert_called_once()

    @patch('dags.operators.publish_coding_systems.GitHook')
    @patch('dags.operators.publish_coding_systems.get_catalog_from_s3')
    @patch('dags.operators.publish_coding_systems.AetionS3FileSystem')
    @patch('dags.operators.publish_coding_systems.tempfile.TemporaryDirectory')
    def test_publish_coding_systems_partial_failure(self, mock_temp_dir, mock_fs_class, mock_get_catalog, mock_git_hook, operator_params, mock_rdc_entry):
        """Test that the operator continues processing other coding systems when one fails."""
        operator = PublishCodingSystems(**operator_params)
        
        # Mock RDC loading with proper structure and correct name format
        mock_get_catalog.return_value = {
            'name': 'test_dataset-test_client',
            'coding_systems': [
                {'name': 'CS1', 'code_column': 'code', 'description_column': 'description'},
                {'name': 'CS2', 'code_column': 'code', 'description_column': 'description'},
                {'name': 'CS3', 'code_column': 'code', 'description_column': 'description'}
            ]
        }
        
        # Mock Git operations
        mock_git = Mock()
        mock_git_hook.return_value.__enter__.return_value = mock_git
        mock_git.any_changes.return_value = True
        
        # Mock temporary directory
        mock_temp_dir.return_value.__enter__.return_value = "/tmp/test_meta"
        
        # Mock S3 operations
        mock_fs = Mock()
        mock_fs_class.return_value = mock_fs
        
        # First coding system succeeds, second fails, third succeeds
        def mock_process_side_effect(coding_system, tmp_folder):
            if coding_system.name == 'CS2':
                raise Exception("S3 download failed for CS2")
            # Mock successful processing for CS1 and CS3
            return None
        
        with patch.object(operator, '_process_coding_system', side_effect=mock_process_side_effect):
            with patch.object(operator, '_update_coding_systems_registry') as mock_update_registry:
                with pytest.raises(AirflowException) as exc_info:
                    operator.publish_coding_systems()
                
                # Verify the error message contains details about the failure
                error_msg = str(exc_info.value)
                assert "Task completed with partial success: 2 coding system(s) processed successfully, 1 failed" in error_msg
                assert "CS2: S3 download failed for CS2" in error_msg
                
                # Verify that Git operations were still performed (for successful coding systems)
                mock_git.commit.assert_called_once()
                mock_git.push.assert_called_once()
                
                # Verify that registry was updated only with successful coding systems
                mock_update_registry.assert_called_once()
                call_args = mock_update_registry.call_args
                successful_coding_systems = call_args[0][1]  # Second argument is coding_systems
                
                # Should only contain CS1 and CS3 (not CS2)
                assert len(successful_coding_systems) == 2
                assert any(cs.name == 'CS1' for cs in successful_coding_systems)
                assert any(cs.name == 'CS3' for cs in successful_coding_systems)
                assert not any(cs.name == 'CS2' for cs in successful_coding_systems)

    @patch('dags.operators.publish_coding_systems.GitHook')
    @patch('dags.operators.publish_coding_systems.get_catalog_from_s3')
    @patch('dags.operators.publish_coding_systems.AetionS3FileSystem')
    @patch('dags.operators.publish_coding_systems.tempfile.TemporaryDirectory')
    def test_publish_coding_systems_all_failures(self, mock_temp_dir, mock_fs_class, mock_get_catalog, mock_git_hook, operator_params, mock_rdc_entry):
        """Test that the operator fails completely when all coding systems fail."""
        operator = PublishCodingSystems(**operator_params)
        
        # Mock RDC loading with proper structure and correct name format
        mock_get_catalog.return_value = {
            'name': 'test_dataset-test_client',
            'coding_systems': [
                {'name': 'CS1', 'code_column': 'code', 'description_column': 'description'},
                {'name': 'CS2', 'code_column': 'code', 'description_column': 'description'}
            ]
        }
        
        # Mock Git operations
        mock_git = Mock()
        mock_git_hook.return_value.__enter__.return_value = mock_git
        mock_git.any_changes.return_value = False  # No changes when all coding systems fail
        
        # Mock temporary directory
        mock_temp_dir.return_value.__enter__.return_value = "/tmp/test_meta"
        
        # Mock S3 operations
        mock_fs = Mock()
        mock_fs_class.return_value = mock_fs
        
        # All coding systems fail
        def mock_process_side_effect(coding_system, tmp_folder):
            raise Exception(f"Processing failed for {coding_system.name}")
        
        with patch.object(operator, '_process_coding_system', side_effect=mock_process_side_effect):
            with patch.object(operator, '_update_coding_systems_registry') as mock_update_registry:
                with pytest.raises(AirflowException) as exc_info:
                    operator.publish_coding_systems()
                
                # Verify the error message contains details about all failures
                error_msg = str(exc_info.value)
                assert "All coding systems failed to process:" in error_msg
                assert "CS1: Processing failed for CS1" in error_msg
                assert "CS2: Processing failed for CS2" in error_msg
                
                # Verify that Git operations were NOT performed (no successful coding systems)
                mock_git.commit.assert_not_called()
                mock_git.push.assert_not_called()
                
                # Verify that registry update was NOT called
                mock_update_registry.assert_not_called()

    @patch('dags.operators.publish_coding_systems.GitHook')
    @patch('dags.operators.publish_coding_systems.get_catalog_from_s3')
    @patch('dags.operators.publish_coding_systems.AetionS3FileSystem')
    @patch('dags.operators.publish_coding_systems.tempfile.TemporaryDirectory')
    def test_publish_coding_systems_registry_update_only_successful(self, mock_temp_dir, mock_fs_class, mock_get_catalog, mock_git_hook, operator_params, mock_rdc_entry):
        """Test that the registry is only updated with successful coding systems."""
        operator = PublishCodingSystems(**operator_params)
        
        # Mock RDC loading with proper structure and correct name format
        mock_get_catalog.return_value = {
            'name': 'test_dataset-test_client',
            'coding_systems': [
                {'name': 'CS1', 'code_column': 'code', 'description_column': 'description'},
                {'name': 'CS2', 'code_column': 'code', 'description_column': 'description'},
                {'name': 'CS3', 'code_column': 'code', 'description_column': 'description'}
            ]
        }
        
        # Mock Git operations
        mock_git = Mock()
        mock_git_hook.return_value.__enter__.return_value = mock_git
        mock_git.any_changes.return_value = True
        
        # Mock temporary directory
        mock_temp_dir.return_value.__enter__.return_value = "/tmp/test_meta"
        
        # Mock S3 operations
        mock_fs = Mock()
        mock_fs_class.return_value = mock_fs
        
        # Only CS2 fails
        def mock_process_side_effect(coding_system, tmp_folder):
            if coding_system.name == 'CS2':
                raise Exception("Processing failed for CS2")
            # Mock successful processing for CS1 and CS3
            return None
        
        with patch.object(operator, '_process_coding_system', side_effect=mock_process_side_effect):
            with patch.object(operator, '_update_coding_systems_registry') as mock_update_registry:
                with pytest.raises(AirflowException):
                    operator.publish_coding_systems()
                
                # Verify that registry was updated only with successful coding systems
                mock_update_registry.assert_called_once()
                call_args = mock_update_registry.call_args
                successful_coding_systems = call_args[0][1]  # Second argument is coding_systems
                
                # Should only contain CS1 and CS3 (not CS2)
                assert len(successful_coding_systems) == 2
                assert any(cs.name == 'CS1' for cs in successful_coding_systems)
                assert any(cs.name == 'CS3' for cs in successful_coding_systems)
                assert not any(cs.name == 'CS2' for cs in successful_coding_systems)

    @patch('dags.operators.publish_coding_systems.GitHook')
    @patch('dags.operators.publish_coding_systems.get_catalog_from_s3')
    @patch('dags.operators.publish_coding_systems.AetionS3FileSystem')
    @patch('dags.operators.publish_coding_systems.tempfile.TemporaryDirectory')
    def test_publish_coding_systems_no_successful_skip_registry(self, mock_temp_dir, mock_fs_class, mock_get_catalog, mock_git_hook, operator_params, mock_rdc_entry):
        """Test that registry update is skipped when no coding systems are successful."""
        operator = PublishCodingSystems(**operator_params)
        
        # Mock RDC loading with proper structure and correct name format
        mock_get_catalog.return_value = {
            'name': 'test_dataset-test_client',
            'coding_systems': [
                {'name': 'CS1', 'code_column': 'code', 'description_column': 'description'},
                {'name': 'CS2', 'code_column': 'code', 'description_column': 'description'}
            ]
        }
        
        # Mock Git operations
        mock_git = Mock()
        mock_git_hook.return_value.__enter__.return_value = mock_git
        mock_git.any_changes.return_value = False  # No changes when all coding systems fail
        
        # Mock temporary directory
        mock_temp_dir.return_value.__enter__.return_value = "/tmp/test_meta"
        
        # Mock S3 operations
        mock_fs = Mock()
        mock_fs_class.return_value = mock_fs
        
        # All coding systems fail
        def mock_process_side_effect(coding_system, tmp_folder):
            raise Exception(f"Processing failed for {coding_system.name}")
        
        with patch.object(operator, '_process_coding_system', side_effect=mock_process_side_effect):
            with patch.object(operator, '_update_coding_systems_registry') as mock_update_registry:
                with pytest.raises(AirflowException):
                    operator.publish_coding_systems()
                
                # Verify that registry update was NOT called
                mock_update_registry.assert_not_called()
                
                # Verify that Git operations were NOT performed
                mock_git.commit.assert_not_called()
                mock_git.push.assert_not_called() 