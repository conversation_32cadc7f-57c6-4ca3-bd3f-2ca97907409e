import os
import yaml
from dags.tools.dag_funcs import has_coding_systems
from unittest.mock import patch

# Paths to sample RDC YAML files
RESOURCE_DIR = os.path.join(os.path.dirname(__file__), '../resources/yaml')
RDC_NO_CODING_SYSTEMS_PATH = os.path.join(RESOURCE_DIR, 'rdc_no_coding_systems.yaml')
RDC_WITH_CODING_SYSTEMS_PATH = os.path.join(RESOURCE_DIR, 'rdc_with_coding_systems.yaml')
RDC_INHERITANCE_PATH = os.path.join(os.path.dirname(__file__), '../resources/rdc_inheritance_sample.yaml')

# Helper to mock get_catalog_from_s3 for a given YAML file
def make_mock_get_catalog_from_s3(yaml_path):
    def _mock(client_name, dataset_name, rdc_url, fs):
        with open(yaml_path, 'r') as f:
            yml = yaml.safe_load(f)
        dataset_client_name = dataset_name
        dataset = next((ds for ds in yml['datasets'] if ds['name'] == dataset_client_name), None)
        return dataset
    return _mock

def test_has_coding_systems_none():
    with patch('dags.tools.dag_funcs.get_catalog_from_s3', side_effect=make_mock_get_catalog_from_s3(RDC_NO_CODING_SYSTEMS_PATH)):
        assert has_coding_systems('any', 'no_coding_systems', 'dummy_rdc_url') is False

def test_has_coding_systems_present():
    with patch('dags.tools.dag_funcs.get_catalog_from_s3', side_effect=make_mock_get_catalog_from_s3(RDC_WITH_CODING_SYSTEMS_PATH)):
        assert has_coding_systems('any', 'with_coding_systems', 'dummy_rdc_url') is True

def test_has_coding_systems_inherited():
    with patch('dags.tools.dag_funcs.get_catalog_from_s3', side_effect=make_mock_get_catalog_from_s3(RDC_INHERITANCE_PATH)):
        # The child dataset inherits coding_systems from the parent via YAML anchor
        assert has_coding_systems('adip0', 'synpuf_subset-adip0', 'dummy_rdc_url') is True
        # The parent dataset also has coding_systems
        assert has_coding_systems('adn', 'synpuf-adn', 'dummy_rdc_url') is True 