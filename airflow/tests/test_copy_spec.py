import pytest
import os
import importlib

from tempfile import TemporaryDirectory
from git import cmd, Repo

from dags.operators.spec_copy_operator import SpecCopyOperator
from .spectools import MockFS, mock_fs_factory


def test_copy_from_s3(mock_fs_factory):
    with TemporaryDirectory() as tmpdir:
        copy_spec = SpecCopyOperator(
            global_artifacts_path="tests/resources",
            dataset_artifacts_path=tmpdir,
            client="client",
            dataset="dataset",
            revision="revision",
            use_copy_spec=True,
            task_id="copy_spec",
            fs_factory=mock_fs_factory
        )
        copy_spec.execute({})
        assert os.path.exists(os.path.join(tmpdir, "data_specification.xlsx"))