
from operators.file_copy_operator import <PERSON><PERSON><PERSON><PERSON>perator
from unittest.mock import <PERSON><PERSON><PERSON>
from unittest import TestCase
from airflow.models import DagBag
from random import randint


class TestFileCopyOperator(TestCase):
    def test_basic_copy(self):
        rng_bytes = bytes(randint(0,255) for _ in range(randint(50,200)))
        input_mock, output_mock = MagicMock(), MagicMock()
        input_mock.read = MagicMock(return_value = rng_bytes)
        fco = FileCopyOperator(
            task_id="test_task",
            input_stream = input_mock,
            output_stream = output_mock,
        )
        fco.execute(context={})
        input_mock.read.assert_called()
        output_mock.write.assert_called_once_with(rng_bytes)

    def test_copy_and_transform(self):
        sample_data_parts = ["one","two","three","four"]
        sample_input = "\r\n".join(sample_data_parts)
        sample_output = "\n".join(sample_data_parts)
        win_to_nix_linesep = lambda x: x.replace("\r\n","\n")
        input_mock, output_mock = MagicMock(), MagicMock()
        input_mock.read = MagicMock(return_value = sample_input)
        fco = FileCopyOperator(
            task_id="test_task",
            input_stream = input_mock,
            output_stream = output_mock,
            transformer = win_to_nix_linesep
        )
        fco.execute(context={})
        input_mock.read.assert_called()
        output_mock.write.assert_called_once_with(sample_output)

    def test_copy_read_error(self):
        input_mock, output_mock = MagicMock(), MagicMock()
        example_filename = "ExampleFileName.txt"
        input_mock.read.side_effect = FileNotFoundError(example_filename)
        fco = FileCopyOperator(
            task_id="test_task",
            input_stream = input_mock,
            output_stream = output_mock
        )
        with self.assertRaisesRegex(FileNotFoundError,example_filename):
            fco.execute(context={})

    def test_copy_transform_error(self):
        input_mock, output_mock = MagicMock(), MagicMock()
        transform_mock = MagicMock(side_effect = Exception())
        fco = FileCopyOperator(
            task_id="test_task",
            input_stream = input_mock,
            transformer = transform_mock,
            output_stream = output_mock,
        )
        with self.assertRaisesRegex(Exception, "Failed while calling transformer"):
            fco.execute(context={})

    def test_copy_write_error(self):
        input_mock, output_mock = MagicMock(), MagicMock()
        output_mock.write.side_effect = Exception()
        fco = FileCopyOperator(
            task_id="test_task",
            input_stream = input_mock,
            output_stream = output_mock,
        )
        with self.assertRaisesRegex(Exception,"writing to the output stream .* failed"):
            fco.execute(context={})

