datasets:
  - &synpuf-adn
    name: synpuf-adn
    dataset_description: {tag: "default", short_description: "Training Dataset - CMS SynPUF", long_description: "", family: "synpuf", coding_systems: "common"}
    table_groups: ["default"]
    default_format: csv
    partitioner: table
    default_partitioner_key: DESYNPUF_ID
    default_partitioner_num: 100
    default_options:
      header: true
      delimiter: ","
      lookup_tables:
      - {name: MEDCODE, file: "medcode.csv"}
    coding_systems:
      - {name: MEDCODE, lookup_table: "MECODE", sql_table: "MEDCODE"}
    tables:
      - {name: Carrier_Claims_Sample, glob: DE1_0_2008_to_2010_Carrier_Claims_Sample_*.csv, group: "default"}
      - {name: Inpatient_Claims_Sample, glob: DE1_0_2008_to_2010_Inpatient_Claims_Sample_*.csv, group: "default"}
      - {name: Outpatient_Claims_Sample, glob: DE1_0_2008_to_2010_Outpatient_Claims_Sample_*.csv, group: "default"}
      - {name: Prescription_Drug_Events_Sample, glob: DE1_0_2008_to_2010_Prescription_Drug_Events_Sample_*.csv, group: "default"}
      - {name: Beneficiary_Summary_File_Sample, glob: "DE1_0_????_Beneficiary_Summary_File_Sample_*.csv", group: "default"}

  - <<: *synpuf-adn
    name: synpuf_subset-adip0
    dataset_description: {tag: "default", short_description: "Testing Dataset - CMS SynPUF Subset", long_description: "ADIP 0 dev environment", family: "synpuf", coding_systems: "common"}
    legacy_ingredient_tables:
      - {event_name: PDE, ingredient_table: DRUG_INGREDIENT} 