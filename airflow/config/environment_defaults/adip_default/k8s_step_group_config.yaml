defaults:
  - /step_group_cluster_config/k8s_step_group_config@_here_
  - _self_
default:
  driver_conf:
    instance_type: "r5a.4xlarge"
    memory_on_disk: "128G"
  worker_conf:
    instance_type: "r5a.2xlarge"
    memory_on_disk: "128G"
  num_workers: 5
  autoscale:
    min_executors: 1
    initial_executors: 1
    max_executors: 20
  spark_conf:
    spark.driver.maxResultSize: "20g"
    spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
    spark.network.timeout: 800
    spark.sql.adaptive.coalescePartitions.enabled: True
    spark.sql.adaptive.enabled: True
    spark.sql.adaptive.localShuffleReader.enabled: True
    spark.sql.adaptive.skewJoin.enabled: True
    spark.sql.legacy.timeParserPolicy: "LEGACY"
    spark.sql.parquet.binaryAsString: True
full_job:
  spark_conf:
    spark.sql.shuffle.partitions: 6000
full_patient_job:
  spark_conf:
    spark.sql.parquet.columnarReaderBatchSize: 256
    spark.memory.storageFraction: 0.005
full_shard_job:
  autoscale:
    min_executors: 1
    initial_executors: 1
    max_executors: 30
  driver_conf:
    instance_type: "r5a.8xlarge"
    memory_on_disk: "128G"
  worker_conf:
    instance_type: "r5a.4xlarge"
    memory_on_disk: "128G"
  spark_conf:
    spark.sql.adaptive.coalescePartitions.enabled: False
    spark.memory.offHeap.enabled: False
    spark.hadoop.fs.s3a.connection.maximum: 500
    spark.hadoop.fs.s3a.threads.max: 256
    spark.task.cpus: 2
