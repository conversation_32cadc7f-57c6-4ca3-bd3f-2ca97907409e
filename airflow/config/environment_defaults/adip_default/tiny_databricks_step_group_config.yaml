defaults:
  - /step_group_cluster_config/databricks_step_group_config@_here_
  - _self_
default:
  aws_attributes:
    availability: "ON_DEMAND"
    zone_id: "${default_adip_overrides.databricks_job_cluster_zone_id}"
  driver_node_type_id: "${default_adip_overrides.databricks_job_cluster_node_type_id}"
  node_type_id: "${default_adip_overrides.databricks_job_cluster_node_type_id}"
  num_workers: 2
  spark_conf:
    spark.default.parallelism: 4
    spark.sql.shuffle.partitions: 4
    spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
