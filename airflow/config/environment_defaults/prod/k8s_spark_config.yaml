aetion.dataset.automaticNdcFormatting: "false"
spark.decommission.enabled: "true"
spark.driver.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
spark.driver.maxResultSize: "0"
spark.executor.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
spark.hadoop.fs.s3a.connection.maximum: 300
spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
spark.hadoop.fs.s3a.threads.max: 200
spark.memory.offHeap.enabled: "false"
spark.shuffle.io.maxRetries: 12
spark.shuffle.io.numConnectionsPerPeer: 8
spark.sql.adaptive.coalescePartitions.enabled: "true"
spark.sql.adaptive.enabled: "true"
spark.sql.adaptive.localShuffleReader.enabled: "true"
spark.sql.adaptive.skewJoin.enabled: "true"
spark.sql.legacy.timeParserPolicy: "LEGACY"
spark.sql.parquet.binaryAsString: "true"
spark.sql.shuffle.partitions: 2000
spark.storage.decommission.enabled: "true"
spark.storage.decommission.rddBlocks.enabled: "true"
spark.storage.decommission.shuffleBlocks.enabled: "true"
spark.storage.decommission.shuffleBlocks.maxThreads: 32
