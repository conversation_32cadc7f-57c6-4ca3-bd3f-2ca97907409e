defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_databricks_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_tiny_subset"
client: "default"
revision: "********"
is_k8s: False
alert_user: "@dev-alert-airflow"
deployment_config:
  aep-${clusterRequest.environment}: "${default_adip_overrides.s3_bucket}"
domain: "${default_adip_overrides.domain}"
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
iam_arn: "arn:aws:iam::${default_adip_overrides.account_id}:instance-profile/${default_adip_overrides.profile}"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "synpuf_tiny_subset/current"
upload_bucket: "s3://${default_adip_overrides.client}.${default_adip_overrides.domain}/upload/synpuf_tiny_subset/********"
validation_path: "synpuf_tiny_subset/current"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
skip_single_shard: True
dynamic_patient_table: True
dynamic_flat_tables: False
copy_spec: False
steps_spark_config:
  full_job:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "${default_adip_overrides.databricks_job_cluster_zone_id}"
    node_type_id: "${default_adip_overrides.databricks_job_cluster_node_type_id}"
    num_workers: 2
    spark_conf:
      spark.default.parallelism: 4
      spark.sql.shuffle.partitions: 4
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
  default:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "${default_adip_overrides.databricks_job_cluster_zone_id}"
    node_type_id: "${default_adip_overrides.databricks_job_cluster_node_type_id}"
    num_workers: 2
    spark_conf:
      spark.default.parallelism: 4
      spark.sql.shuffle.partitions: 4
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
