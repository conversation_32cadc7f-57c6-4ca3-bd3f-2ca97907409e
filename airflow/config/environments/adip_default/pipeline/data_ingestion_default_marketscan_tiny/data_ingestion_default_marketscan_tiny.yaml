defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_tiny"
client: "${default_adip_overrides.client}"
revision: "20240208"
is_k8s: False
alert_user: "@dev-alert-airflow"
deployment_config:
  aep-${clusterRequest.environment}: "${default_adip_overrides.s3_bucket}"
domain: "${default_adip_overrides.domain}"
skip_single_shard: True
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-12-31"
  LAB_START_DATE: "2013-12-31"
source_files_password: ""
transform_path: "marketscan_tiny/adn/current"
upload_bucket: "s3://${default_adip_overrides.client}.${default_adip_overrides.domain}/upload/marketscan_tiny/20240208"
use_smart_sampling: False
validation_path: "marketscan_tiny/adn/current"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
dynamic_flat_tables: False
dynamic_patient_table: True
copy_spec: False
