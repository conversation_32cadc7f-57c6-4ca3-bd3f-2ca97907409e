defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_subset"
client: "default"
revision: "********"
is_k8s: True
alert_user: "@dev-alert-airflow"
deployment_config:
  aep-${clusterRequest.environment}: "${default_adip_overrides.s3_bucket}"
domain: "${default_adip_overrides.domain}"
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
iam_arn: "arn:aws:iam::${default_adip_overrides.account_id}:instance-profile/${default_adip_overrides.profile}"
regenerate_all_flat_tables: False
skip_single_shard: False
source_files_password: ""
transform_path: "synpuf_subset/********"
upload_bucket: "s3://${default_adip_overrides.client}.${default_adip_overrides.domain}/upload/synpuf_subset/********"
validation_path: "synpuf_subset/********"
validation_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
dynamic_patient_table: True
dynamic_flat_tables: False
copy_spec: False
steps_spark_config:
  default:
    autoscale:
      min_executors: 1
      initial_executors: 1
      max_executors: 4
    worker_conf:
      cores: 15
      memory: "70g"
      memory_on_disk: "128G"
  full_job:
    spark_conf:
      spark.sql.shuffle.partitions: "200"
