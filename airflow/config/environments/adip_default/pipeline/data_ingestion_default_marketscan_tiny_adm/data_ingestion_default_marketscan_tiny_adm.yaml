defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_tiny_adm"
client: "default"
revision: "********"
is_k8s: False
alert_user: "@dev-alert-airflow"
deployment_config:
  aep-${clusterRequest.environment}: "${default_adip_overrides.s3_bucket}"
domain: "${default_adip_overrides.domain}"
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  MIN_DATE: "2015-10-01"
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
iam_arn: "arn:aws:iam::${default_adip_overrides.account_id}:instance-profile/${default_adip_overrides.profile}"
sampled_data_percentage: "50"
source_files_password: ""
transform_path: "marketscan_tiny_adm/demo/1.4"
upload_bucket: "s3://${default_adip_overrides.client}.${default_adip_overrides.domain}/upload/marketscan_tiny_adm/********"
use_smart_sampling: False
validation_path: "marketscan_adm/1.4"
validation_vars:
  MIN_DATE: "2015-10-01"
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
dynamic_flat_tables: True
dynamic_patient_table: True
native_data_for_adm_url: "s3://${default_adip_overrides.s3_bucket}/etl/marketscan_tiny/********"
prophecy_pipeline_prefix_override: "marketscan"
dataset_group: "LABS"
skip_single_shard: True
