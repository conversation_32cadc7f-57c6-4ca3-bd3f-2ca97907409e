defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_k8s_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_tiny_subset"
client: "default"
revision: "********"
is_k8s: True
alert_user: "@dev-alert-airflow"
deployment_config:
  aep-${clusterRequest.environment}: "${default_adip_overrides.s3_bucket}"
domain: "${default_adip_overrides.domain}"
skip_single_shard: True
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
service_account: "spark-operator-client-${default_adip_overrides.service_account_client_name}"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "synpuf_tiny_subset/current"
upload_bucket: "s3://${default_adip_overrides.client}.${default_adip_overrides.domain}/upload/synpuf_tiny_subset/********"
validation_path: "synpuf_tiny_subset/current"
rde_revision_check: "********"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
dynamic_patient_table: True
dynamic_flat_tables: False
copy_spec: False
celeborn_feature:
  enabled: "${default_adip_overrides.celeborn_enabled}"
