defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_k8s_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_tiny"
client: "default"
revision: "********"
is_k8s: True
alert_user: "@dev-alert-airflow"
celeborn_feature:
  enabled: True
  spark_conf:
    spark.shuffle.manager: "org.apache.spark.shuffle.celeborn.SparkShuffleManager"
deployment_config:
  aep-${clusterRequest.environment}: "${default_adip_overrides.s3_bucket}"
domain: "${default_adip_overrides.domain}"
dynamic_flat_tables: True
dynamic_patient_table: True
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-12-31"
  LAB_START_DATE: "2013-12-31"
sampled_data_percentage: "50"
skip_single_shard: True
source_files_password: ""
transform_path: "marketscan_tiny/adn/current"
upload_bucket: "s3://${default_adip_overrides.client}.${default_adip_overrides.domain}/upload/marketscan_tiny/********"
use_smart_sampling: False
service_account: "spark-operator-client-${default_adip_overrides.service_account_client_name}"
validation_path: "marketscan_tiny/adn/current"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
