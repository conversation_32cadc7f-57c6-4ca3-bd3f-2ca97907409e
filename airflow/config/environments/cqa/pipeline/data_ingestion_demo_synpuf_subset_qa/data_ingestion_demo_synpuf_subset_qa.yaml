defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_subset_qa"
client: "demo"
revision: "**********"
is_k8s: False
alert_user: "@michael.lvov"
copy_spec: False
dynamic_flat_tables: False
dynamic_patient_table: True
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2011-12-31"
  GDR_START_DATE: "2008-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/demo-databricks-etl-iam"
regenerate_all_flat_tables: True
skip_single_shard: False
source_files_password: ""
transform_path: "synpuf_subset/20211018"
upload_bucket: "s3://demo.aetion.com/upload/synpuf_subset_qa/**********"
validation_path: "synpuf_subset/20211018"
validation_vars:
  GDR_END_DATE: "2011-12-31"
  GDR_START_DATE: "2008-01-01"
