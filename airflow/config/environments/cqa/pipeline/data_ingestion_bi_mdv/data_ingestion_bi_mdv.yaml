defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "mdv"
client: "bi"
revision: "20200130"
is_k8s: False
upload_bucket: "bi.aetion.com/upload/mdv/20200130"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bi-databricks-etl-iam"
source_files_password: "Dh_30sh-lYgPOx"
data_cuts:
  - name: "default"
    hive_vars:
      GDR_START_DATE: "1900-01-01"
      GDR_END_DATE: "2019-09-30"
transform_path: "mdv/bi20200130"
validation_path: "mdv/bi20200130"
