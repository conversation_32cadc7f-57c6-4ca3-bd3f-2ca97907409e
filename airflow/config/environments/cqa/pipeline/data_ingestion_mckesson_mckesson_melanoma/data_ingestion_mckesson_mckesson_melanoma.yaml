defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "mckesson_melanoma"
client: "mckesson"
revision: "201908"
is_k8s: False
git_branch_override: "remotes/origin/mckesson-melanoma"
hive_vars:
  GDR_END_DATE: "2019-07-02"
  GDR_START_DATE: "1968-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/mckesson-databricks-etl-iam"
source_files_password: ""
transform_path: "mckesson/melanoma/201908"
upload_bucket: "mckesson.aetion.com/upload/mckesson_melanoma/201908"
validation_path: "mckesson/melanoma/201908"
