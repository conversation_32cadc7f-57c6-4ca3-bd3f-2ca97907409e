defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "amgen"
revision: "20210608"
is_k8s: False
git_branch_override: "remotes/origin/amgen_marketscan_20210608"
hive_vars:
  EV_END_DATE: "2021-05-31"
  EV_START_DATE: "2020-06-30"
  GDR_END_DATE: "2021-05-31"
  GDR_START_DATE: "2008-12-31"
  LAB_END_DATE: "2021-05-31"
  LAB_START_DATE: "2008-12-31"
  MAX_DATE: "9999-01-01"
  REVISION: "20210608"
iam_arn: "arn:aws:iam::627533566824:instance-profile/amgen-databricks-etl-iam"
source_files_password: ""
transform_path: "marketscan/amgen20210608"
upload_bucket: "amgen.aetion.com/upload/marketscan/20210608"
validation_path: "marketscan/amgen20210608"
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      spark.default.parallelism: 6000
      spark.executor.cores: 8
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 6000
  default:
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
