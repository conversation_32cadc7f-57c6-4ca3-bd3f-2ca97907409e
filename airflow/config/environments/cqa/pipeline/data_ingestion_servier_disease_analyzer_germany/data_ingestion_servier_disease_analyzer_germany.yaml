defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "disease_analyzer_germany"
client: "servier"
revision: "20200214"
is_k8s: False
git_branch_override: "servier-disease_analyzer_germany-20200214"
hive_vars:
  GDR_END_DATE: "2019-09-30"
  GDR_START_DATE: "1992-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/servier-databricks-etl-iam"
source_files_password: ""
transform_path: "disease-analyzer/germany/servier20200214"
upload_bucket: "servier.aetion.com/upload/disease_analyzer_germany/20200214"
validation_path: "disease-analyzer/germany/servier20200214"
