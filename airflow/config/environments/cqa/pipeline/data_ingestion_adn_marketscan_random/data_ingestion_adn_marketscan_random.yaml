defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_random"
client: "adn"
revision: "201910"
is_k8s: False
git_branch_override: "remotes/origin/marketscan_10mrandom"
hive_vars:
  GDR_END_DATE: "2017-12-31"
  GDR_START_DATE: "2012-12-31"
  LAB_END_DATE: "2017-12-31"
  LAB_START_DATE: "2012-12-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
source_files_password: ""
transform_path: "marketscan_random/adn201910"
upload_bucket: "adn.aetion.com/upload/marketscan_random/201910"
validation_path: "marketscan_random/adn201910"
