defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "allpayer-hcv"
client: "abbvie"
revision: "201907"
is_k8s: False
git_branch_override: "remotes/origin/default-allpayer-optum"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
source_files_password: ""
transform_path: "humedica/allpayer-hcv201907"
upload_bucket: "abbvie.aetion.com/upload/allpayer-hcv/201907"
validation_path: "humedica/allpayer-hcv201907"
