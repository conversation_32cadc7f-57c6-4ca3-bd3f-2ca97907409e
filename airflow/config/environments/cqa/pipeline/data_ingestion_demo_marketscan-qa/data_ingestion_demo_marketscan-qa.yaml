defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "MARKETSCAN"
client: "demo"
revision: "20221116"
is_k8s: False
alert_user: "@peter.imrich"
deployment_config:
  demo: "demo.aetion.com"
dynamic_flat_tables: False
dynamic_gdr_enabled: False
dynamic_patient_table: False
fail_stopgap: False
hive_vars:
  DUMMY_DATE: "2021-08-11"
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2003-12-31"
  LAB_END_DATE: "2018-09-30"
  LAB_START_DATE: "2003-12-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/demo-databricks-etl-iam"
rde_patient_id: "ENROLID"
regenerate_all_flat_tables: True
skip_single_shard: True
source_files_password: ""
transform_path: "marketscan/qa/current"
upload_bucket: "demo.aetion.com/upload/marketscan-qa/20221116"
use_smart_sampling: True
validation_path: "marketscan/qa/current"
validation_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2003-12-31"
