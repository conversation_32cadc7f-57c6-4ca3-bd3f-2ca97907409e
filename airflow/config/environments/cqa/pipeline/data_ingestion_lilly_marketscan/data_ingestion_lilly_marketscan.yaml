defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "lilly"
revision: "20201026_persist_flat_testing"
is_k8s: False
git_branch_override: "remotes/origin/lilly-marketscan-20201027-flat-events"
hive_vars:
  EV_END_DATE: "2020-08-31"
  EV_START_DATE: "2019-03-31"
  GDR_END_DATE: "2020-08-31"
  GDR_START_DATE: "2006-12-31"
  LAB_END_DATE: "2018-12-31"
  LAB_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/lilly-databricks-etl-iam"
pre_partitioned_data_url: "lilly.aetion.com/etl/marketscan/20201027/"
source_files_password: ""
transform_path: "marketscan/lilly20201026_flat_tables"
upload_bucket: "lilly.aetion.com/upload/marketscan/20201026_persist_flat_testing"
validation_path: "marketscan/lilly20201027"
steps_spark_config:
  full_job:
    spark_conf:
      spark.default.parallelism: 1000
      spark.sql.shuffle.partitions: 1000
