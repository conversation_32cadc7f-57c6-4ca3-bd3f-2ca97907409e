defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "healthverity_8"
client: "healthverity"
revision: "20200429"
is_k8s: False
git_branch_override: "remotes/origin/healthverity_b2_20200411"
hive_vars:
  GDR_END_DATE: "2020-04-29"
  GDR_START_DATE: "2018-12-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/healthverity-databricks-etl-iam"
source_files_password: ""
transform_path: "healthverity/healthverity_b2/20200429"
upload_bucket: "healthverity.aetion.com/upload/healthverity_8/20200429"
validation_path: "healthverity/healthverity_b2/20200429"
steps_spark_config:
  default:
    aws_attributes:
      availability: "ON_DEMAND"
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
      spot_bid_price_percent: 100
      zone_id: "us-east-1b"
    node_type_id: "rd-fleet.xlarge"
    num_workers: 30
    spark_conf:
      spark.io.compression.codec: "snappy"
      spark.sql.broadcastTimeout: 800
