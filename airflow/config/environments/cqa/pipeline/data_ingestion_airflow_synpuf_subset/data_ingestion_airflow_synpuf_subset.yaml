defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_subset"
client: "airflow"
revision: "202021101801"
is_k8s: False
alert_user: "@dev-alert-airflow"
deployment_config:
  aep-prod: "airflow.aetion.com"
domain: "aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
fail_stopgap: False
hive_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adip-cqa-databricks-etl-iam"
regenerate_all_flat_tables: False
skip_single_shard: False
source_files_password: ""
transform_path: "synpuf_subset/20211018"
upload_bucket: "s3://airflow.aetion.com/upload/synpuf_subset/202021101801"
validation_path: "synpuf_subset/20211018"
validation_vars:
  G<PERSON>_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "auto"
    spark_conf:
      spark.default.parallelism: 6000
      spark.executor.cores: 8
      spark.sql.shuffle.partitions: 6000
  default:
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
