defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "allpayer_womenshealth"
client: "abbvie"
revision: "20201021_persist_flat_testing"
is_k8s: False
git_branch_override: "remotes/origin/data-2818-womenshealth"
hive_vars:
  GDR_END_DATE: "2020-03-31"
  GDR_START_DATE: "2007-01-01"
  MASKED_BIRTH_DATE: "1930 and Earlier"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
pre_partitioned_data_url: "abbvie.aetion.com/etl/allpayer_womenshealth/20201022/"
source_files_password: "ND83MAL017"
transform_path: "humedica/allpayer_womenshealth20201021_persist_flat_testing"
upload_bucket: "abbvie.aetion.com/upload/allpayer_womenshealth/20201021_persist_flat_testing"
validation_path: "humedica/allpayer_womenshealth20201022"
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 40
    spark_conf:
      spark.default.parallelism: 2000
      spark.driver.maxResultSize: "20G"
      spark.executor.cores: 4
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
      spark.io.compression.codec: "snappy"
      spark.network.timeout: 1200
      spark.rpc.message.maxSize: 1536
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.autoBroadcastJoinThreshold: 209715200
      spark.sql.broadcastTimeout: 12000
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 2000
