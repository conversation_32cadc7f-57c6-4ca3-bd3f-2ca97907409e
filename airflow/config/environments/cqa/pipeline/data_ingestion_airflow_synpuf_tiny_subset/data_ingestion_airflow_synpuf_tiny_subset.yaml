defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_databricks_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_tiny_subset"
client: "airflow"
revision: "20221004"
is_k8s: False
alert_user: "@dev-alert-airflow"
deployment_config:
  aep-prod: "airflow.aetion.com"
domain: "aetion.com"
dynamic_flat_tables: False
dynamic_patient_table: True
fail_stopgap: False
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adip-cqa-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "synpuf_tiny_subset/current"
upload_bucket: "s3://airflow.aetion.com/upload/synpuf_tiny_subset/20221004"
validation_path: "synpuf_tiny_subset/current"
validation_vars:
  G<PERSON>_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "auto"
    node_type_id: "i3.2xlarge"
    num_workers: 2
    spark_conf:
      spark.default.parallelism: 4
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
      spark.sql.shuffle.partitions: 4
  default:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "auto"
    node_type_id: "i3.2xlarge"
    num_workers: 2
    spark_conf:
      spark.default.parallelism: 4
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
      spark.sql.shuffle.partitions: 4
