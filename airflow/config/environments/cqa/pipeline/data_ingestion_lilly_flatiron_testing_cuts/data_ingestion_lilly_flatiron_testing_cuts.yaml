defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "flatiron_testing_cuts"
client: "lilly"
revision: "20201005"
is_k8s: False
upload_bucket: "lilly.aetion.com/upload/flatiron_testing_cuts/20201005"
iam_arn: "arn:aws:iam::627533566824:instance-profile/lilly-databricks-etl-iam"
source_files_password: ""
hive_vars:
  GDR_START_DATE: "1990-01-01"
  GDR_END_DATE: "2020-08-31"
data_cuts:
  - name: "5y"
    hive_vars:
      GDR_START_DATE: "2015-08-31"
      GDR_END_DATE: "2020-08-31"
  - name: "3y"
    hive_vars:
      GDR_START_DATE: "2017-08-31"
      GDR_END_DATE: "2020-08-31"
transform_path: "flatiron/lilly/20201005/nsclc"
validation_path: "flatiron/lilly/20201005/nsclc"
steps_spark_config:
  default:
    num_workers: 4
  full_job:
    node_type_id: "rd-fleet.xlarge"
