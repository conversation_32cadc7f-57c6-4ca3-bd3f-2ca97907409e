defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "TEPOTINIB_MS2000950027"
client: "merckkgaares"
revision: "20200313"
is_k8s: False
git_branch_override: "remotes/origin/merk-kga-sample"
hive_vars:
  GDR_END_DATE: "2020-02-10"
  GDR_START_DATE: "1944-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/merckkgaares-databricks-etl-iam"
source_files_password: ""
transform_path: "merckkgaares/TEPOTINIB_MS2000950027/20200313"
upload_bucket: "merckkgaares.aetion.com/upload/TEPOTINIB_MS2000950027/20200313"
validation_path: "merckkgaares/TEPOTINIB_MS2000950027/20200313"
steps_spark_config:
  default:
    spark_conf:
      spark.sql.crossJoin.enabled: True
