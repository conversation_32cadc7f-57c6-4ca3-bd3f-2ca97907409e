defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "ehr-noclaims"
client: "optum"
revision: "201809"
is_k8s: False
hive_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/optum-databricks-etl-iam"
source_files_password: ""
transform_path: "humedica/ehr-noclaims201809"
upload_bucket: "optum.aetion.com/upload/ehr-noclaims/201809"
validation_path: "humedica/ehr-noclaims201809"
steps_spark_config:
  full_job:
    aws_attributes:
      availability: "SPOT"
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
      spot_bid_price_percent: 100
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 20
    spark_conf:
      spark.default.parallelism: 1000
      spark.executor.cores: 10
      spark.network.timeout: 800
      spark.sql.shuffle.partitions: 1000
