defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "veradigm_hiae"
client: "janssen"
revision: "20210415"
is_k8s: False
git_branch_override: "remotes/origin/veradigm_20210224"
hive_vars:
  GDR_END_DATE: "2021-01-31"
  GDR_START_DATE: "2015-08-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/janssen-databricks-etl-iam"
pre_partitioned_data_url: "janssen.aetion.com/etl/veradigm_hiae/20210415/"
source_files_password: ""
transform_path: "veradigm_hiae/20210415"
upload_bucket: "s3://janssen.aetion.com/upload/veradigm_hiae/20210415"
validation_path: "veradigm_hiae/20210415"
validation_vars:
  GDR_END_DATE: "2021-01-31"
  GDR_START_DATE: "2015-08-01"
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    driver_node_type_id: "rd-fleet.16xlarge"
    node_type_id: "rd-fleet.16xlarge"
    num_workers: 100
    spark_conf:
      spark.default.parallelism: 8000
      spark.driver.cores: 20
      spark.driver.maxResultSize: 0
      spark.executor.cores: 20
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
      spark.network.timeout: 1200
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.autoBroadcastJoinThreshold: 20971520
      spark.sql.broadcastTimeout: 1200
      spark.sql.objectHashAggregate.sortBased.fallbackThreshold: 1024
      spark.sql.shuffle.partitions: 16000
      spark.task.maxDirectResultSize: 2097152000
  default:
    driver_node_type_id: "rd-fleet.8xlarge"
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 60
    spark_conf:
      spark.driver.cores: 4
      spark.driver.maxResultSize: "40g"
      spark.executor.cores: 8
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
      spark.sql.shuffle.partitions: 2000
