defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "cprd_nested"
client: "lilly"
revision: "201907"
is_k8s: False
git_branch_override: "remotes/origin/lilly-cprd-201907-update"
hive_vars:
  GDR_END_DATE: "2019-06-30"
  GDR_START_DATE: "1987-11-21"
iam_arn: "arn:aws:iam::627533566824:instance-profile/lilly-databricks-etl-iam"
source_files_password: ""
transform_path: "cprd_nested/lilly201907"
upload_bucket: "lilly.aetion.com/upload/cprd_nested/201907"
validation_path: "cprd_nested/lilly201907"
steps_spark_config:
  full_shard_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    spark_conf:
      spark.sql.parquet.enableVectorizedReader: False
