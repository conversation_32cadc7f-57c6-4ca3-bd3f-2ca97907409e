defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum-cdm"
client: "abbvie"
revision: "20211201"
is_k8s: False
alert_user: "@katharine.fuzesi"
deployment_config:
  abbvie: "abbvie.aetion.com"
git_branch_override: "remotes/origin/optum-cdm-20211201-fix"
hive_vars:
  GDR_END_DATE: "2021-06-30"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
source_files_password: ""
transform_path: "optum/cdm/abbvie/current"
upload_bucket: "abbvie.aetion.com/upload/optum-cdm/20211201"
validation_path: "optum/cdm/abbvie"
validation_vars:
  GDR_END_DATE: "2021-06-30"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 60
    spark_conf:
      spark.executor.cores: 8
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.network.timeout: 800
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.parquet.enableVectorizedReader: False
  default:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
    spark_conf:
      spark.executor.cores: 8
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.network.timeout: 800
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.parquet.enableVectorizedReader: False
