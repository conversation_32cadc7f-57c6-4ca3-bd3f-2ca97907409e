defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "cmsra"
client: "bwh"
revision: "20200303"
is_k8s: False
git_branch_override: "remotes/origin/cmsra-20200303"
hive_vars:
  GDR_END_DATE: "2017-12-31"
  GDR_START_DATE: "2006-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
source_files_password: ""
transform_path: "cms/ra/20200303"
upload_bucket: "bwh.aetion.com/upload/cmsra/20200303"
validation_path: "cms/ra/20200303"
steps_spark_config:
  full_shard_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 512
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
  default:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 512
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
    spark_conf:
      spark.executor.cores: "8"
      spark.network.timeout: 800
      spark.sql.parquet.enableVectorizedReader: False
