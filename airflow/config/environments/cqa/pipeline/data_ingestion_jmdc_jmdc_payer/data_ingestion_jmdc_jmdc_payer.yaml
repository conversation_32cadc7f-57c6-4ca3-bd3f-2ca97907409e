defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "jmdc_payer"
client: "jmdc"
revision: "201906"
is_k8s: False
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2000-05-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/jmdc-databricks-etl-iam"
source_files_password: ""
transform_path: "jmdc_payer/201812"
upload_bucket: "jmdc.aetion.com/upload/jmdc_payer/201906"
validation_path: "jmdc_payer/201812"
