defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "flatiron_ge"
client: "lilly"
revision: "20210810"
is_k8s: False
git_branch_override: "lilly_flatiron_20210810"
hive_vars:
  GDR_END_DATE: "2021-07-31"
  GDR_START_DATE: "1990-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/lilly-databricks-etl-iam"
source_files_password: ""
transform_path: "flatiron/lilly/20210810/ge"
upload_bucket: "lilly.aetion.com/upload/flatiron_ge/20210810"
validation_path: "flatiron/lilly/20210810/ge"
validation_vars:
  GDR_END_DATE: "2021-07-31"
  GDR_START_DATE: "1990-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      spark.default.parallelism: 6000
      spark.executor.cores: 8
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 6000
  default:
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
