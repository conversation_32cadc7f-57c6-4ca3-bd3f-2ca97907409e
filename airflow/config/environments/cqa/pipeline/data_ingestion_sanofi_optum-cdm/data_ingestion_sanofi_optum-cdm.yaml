defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum-cdm"
client: "sanofi"
revision: "20210803"
is_k8s: False
deployment_config:
  astraz: "astraz.aetion.com"
  bi: "bi.aetion.com"
git_branch_override: "remotes/origin/sanofi_optum_cdm_20210803"
hive_vars:
  GDR_END_DATE: "2021-06-30"
  GDR_START_DATE: "2000-05-01"
  REVISION: "20210803"
iam_arn: "arn:aws:iam::627533566824:instance-profile/sanofi-databricks-etl-iam"
source_files_password: ""
transform_path: "optum/cdm/sanofi/20210706"
upload_bucket: "sanofi.aetion.com/upload/optum-cdm/20210803"
validation_path: "optum/cdm/sanofi20210706"
validation_vars:
  GDR_END_DATE: "2021-06-30"
  GDR_START_DATE: "2000-05-01"
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 60
    spark_conf:
      spark.default.parallelism: 1000
      spark.driver.maxResultSize: "20G"
      spark.executor.cores: 8
      spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
      spark.io.compression.codec: "snappy"
      spark.network.timeout: 1200
      spark.shuffle.file.buffer: "64k"
      spark.sql.autoBroadcastJoinThreshold: 20971520
      spark.sql.broadcastTimeout: 800
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 4000
  default:
    spark_conf:
      spark.driver.maxResultSize: "50g"
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 800
