defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "adn"
revision: "2019060102"
is_k8s: False
alert_user: "@Damian"
copy_spec: False
deployment_config:
  demo: "demo.aetion.com"
git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-09-30"
  LAB_START_DATE: "2013-12-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
pre_partitioned_data_url: "adn.aetion.com/etl/marketscan/20190601"
source_files_password: ""
transform_path: "marketscan/adn/current"
upload_bucket: "adn.aetion.com/upload/marketscan/2019060102"
validation_path: "marketscan/adn/current"
validation_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2013-12-31"
steps_spark_config:
  default:
    spark_env_vars:
      JNAME: "zulu17-ca-amd64"
    spark_version: "15.4.x-scala2.12"
