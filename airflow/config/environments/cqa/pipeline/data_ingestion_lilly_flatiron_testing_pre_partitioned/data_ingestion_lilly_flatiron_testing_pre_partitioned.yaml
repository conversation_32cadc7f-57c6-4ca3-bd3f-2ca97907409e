defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "flatiron_testing_pre_partitioned"
client: "lilly"
revision: "201906"
is_k8s: False
upload_bucket: "lilly.aetion.com/upload/flatiron_testing_pre_partitioned/201906"
iam_arn: "arn:aws:iam::627533566824:instance-profile/lilly-databricks-etl-iam"
pre_partitioned_data_url: "lilly.aetion.com/etl/flatiron_testing/201906/"
source_files_password: ""
hive_vars:
  GDR_START_DATE: "1990-01-01"
  GDR_END_DATE: "2019-04-30"
transform_path: "flatiron/201906/nsclc"
validation_path: "flatiron/201906/nsclc"
steps_spark_config:
  default:
    num_workers: 4
  full_job:
    node_type_id: "rd-fleet.xlarge"
