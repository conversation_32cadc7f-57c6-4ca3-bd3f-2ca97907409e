defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "healthverity_b1"
client: "healthverity-dev"
revision: "20200411"
is_k8s: False
git_branch_override: "remotes/origin/healthverity_b1_20200411"
hive_vars:
  GDR_END_DATE: "2020-04-10"
  GDR_START_DATE: "2018-12-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/healthverity-databricks-etl-iam"
source_files_password: ""
transform_path: "healthverity/healthverity_b1/20200411"
upload_bucket: "healthverity-dev.aetion.com/upload/healthverity_b1/20200411"
validation_path: "healthverity/healthverity_b1/20200401"
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 80
  default:
    aws_attributes:
      availability: "ON_DEMAND"
      ebs_volume_count: 1
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
      spot_bid_price_percent: 100
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 40
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.io.compression.codec: "snappy"
      spark.network.timeout: 1200
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: 1200
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 12000
      spark.task.maxDirectResultSize: **********
