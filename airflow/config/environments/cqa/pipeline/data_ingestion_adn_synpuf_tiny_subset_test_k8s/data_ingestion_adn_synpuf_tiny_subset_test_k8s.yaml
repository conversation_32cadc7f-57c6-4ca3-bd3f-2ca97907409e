defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_k8s_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_tiny_subset_test"
client: "adn"
revision: "********"
is_k8s: True
alert_user: "@dev-alert-airflow"
copy_spec: False
deployment_config:
  adn: "adn.aetion.com"
domain: "aetion.com"
dynamic_flat_tables: False
dynamic_patient_table: True
fail_stopgap: False
full_shard_stopgap: False
git_branch_override: "pe_4850_rdc_preprocessed_tables"
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
preprocess: True
regenerate_all_flat_tables: True
service_account: "spark-operator-client-adn"
source_files_password: ""
transform_path: "synpuf_tiny_subset_test/current"
upload_bucket: "adn.aetion.com/upload/synpuf_tiny_subset_test/********"
validation_path: "synpuf_tiny_subset_test/current"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
