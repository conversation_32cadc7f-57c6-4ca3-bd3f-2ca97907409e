defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "ehr-endo"
client: "abbvie"
revision: "201902"
is_k8s: False
artifacts_path_override: "s3://databricks.aetion.com/artifacts/automated_data_ingestion/datasets/optum_ehr_endo_201902/"
git_branch_override: "remotes/origin/optum_ehr_endo_201902"
hive_vars:
  GDR_END_DATE: "2018-06-30"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
source_files_password: "777rwykkxx"
transform_path: "humedica/ehrEndo201902"
upload_bucket: "abbvie.aetion.com/upload/ehr-endo/201902"
validation_path: "humedica/ehrEndo201902"
