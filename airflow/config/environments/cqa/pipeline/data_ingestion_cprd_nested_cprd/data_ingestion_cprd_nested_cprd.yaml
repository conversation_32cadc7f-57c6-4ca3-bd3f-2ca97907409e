defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "nested_cprd"
client: "cprd"
revision: "201812"
is_k8s: False
artifacts_path_override: "s3://databricks.aetion.com/artifacts/automated_data_ingestion/datasets/nested_cprd/"
git_branch_override: "remotes/origin/nested-cprd"
hive_vars:
  GDR_END_DATE: "2019-01-01"
  GDR_START_DATE: "1987-11-21"
iam_arn: "arn:aws:iam::627533566824:instance-profile/cprd-databricks-etl-iam"
source_files_password: ""
transform_path: "cprd/201807_nested"
upload_bucket: "cprd.aetion.com/upload/nested_cprd/201812"
validation_path: "cprd/201807_nested"
steps_spark_config:
  full_shard_job:
    aws_attributes:
      availability: "SPOT"
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
      spot_bid_price_percent: 100
      zone_id: "us-east-1b"
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 20
    spark_conf:
      spark.default.parallelism: 1000
      spark.sql.shuffle.partitions: 1000
  unarchiver_job_only:
    aws_attributes:
      ebs_volume_count: 1
      ebs_volume_size: 2048
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    init_scripts:
      - s3:
          destination: "s3://databricks.aetion.com/artifacts/automated_data_ingestion/prod/unarchiver_cluster_init.sh"
          region: ""
    num_workers: 3
    spark_conf:
      spark.executor.cores: "1"
    spark_env_vars:
      export TMPDIR: "/local_disk0/tmp"
