defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum-cdm"
client: "bwh"
revision: "201812"
is_k8s: False
git_branch_override: "remotes/origin/bwh-optum-cdm-pipeline"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
source_files_password: ""
transform_path: "optum/cdm/bwh/201812"
upload_bucket: "bwh.aetion.com/upload/optum-cdm/201812"
validation_path: "optum/cdm/bwh201812"
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 20
    spark_conf:
      spark.default.parallelism: 1000
      spark.sql.shuffle.partitions: 1000
