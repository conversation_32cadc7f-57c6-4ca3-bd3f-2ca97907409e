defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "transcelerate_ra"
client: "lilly"
revision: "2019070000"
is_k8s: False
git_branch_override: "remotes/origin/lilly-transcelerate-201907-sql-cut"
hive_vars:
  GDR_END_DATE: "2019-04-30"
  GDR_START_DATE: "1990-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/lilly-databricks-etl-iam"
source_files_password: ""
transform_path: "transcelerate/ra201907"
upload_bucket: "lilly.aetion.com/upload/transcelerate_ra/2019070000"
validation_path: "transcelerate/ra201907"
