defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "flatiron_testing"
client: "lilly"
revision: "20201005"
is_k8s: False
hive_vars:
  GDR_END_DATE: "2020-08-31"
  GDR_START_DATE: "1990-01-01"
pre_partitioned_data_url: "lilly.aetion.com/etl/flatiron/20201005/"
iam_arn: "arn:aws:iam::627533566824:instance-profile/lilly-databricks-etl-iam"
source_files_password: ""
transform_path: "flatiron/lilly/20201005/testing"
upload_bucket: "lilly.aetion.com/upload/flatiron_testing/20201005"
validation_path: "flatiron/lilly/20201005/testing"
steps_spark_config:
  default:
    num_workers: 4
  full_job:
    node_type_id: "rd-fleet.xlarge"
