defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "ms"
client: "om1"
revision: "20191009"
is_k8s: False
git_branch_override: "remotes/origin/om1-ms_deep_clinical-20191009"
hive_vars:
  GDR_END_DATE: "2019-07-31"
  GDR_START_DATE: "2013-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/om1-databricks-etl-iam"
source_files_password: ""
transform_path: "om1/ms/20191009"
upload_bucket: "s3://om1.aetion.com/upload/ms/20191009"
validation_path: "om1/ms/20191009"
