defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "cmsdm_test"
client: "bwh"
revision: "201911"
is_k8s: False
git_branch_override: "remotes/origin/bwh-cmsdm-201911-test-sqls"
hive_vars:
  GDR_END_DATE: "2017-12-31"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
source_files_password: ""
transform_path: "cms/dm/201911"
upload_bucket: "bwh.aetion.com/upload/cmsdm_test/201911"
validation_path: "cms/dm/201911"
steps_spark_config:
  full_shard_job:
    spark_conf:
      spark.driver.maxResultSize: "200g"
      spark.sql.shuffle.partitions: 1000
