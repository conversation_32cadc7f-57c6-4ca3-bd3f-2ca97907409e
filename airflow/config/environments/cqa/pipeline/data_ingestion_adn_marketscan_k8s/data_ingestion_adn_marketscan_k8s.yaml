defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "adn"
revision: "**********"
is_k8s: True
alert_user: "@peter.imrich"
deployment_config:
  demo: "demo.aetion.com"
git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-09-30"
  LAB_START_DATE: "2013-12-31"
iam_arn: "arn:aws:iam::************:instance-profile/adn-databricks-etl-iam"
pre_partitioned_data_url: "adn.aetion.com/etl/marketscan/********"
regenerate_all_flat_tables: True
service_account: "spark-operator-client-adn"
source_files_password: ""
transform_path: "marketscan/adn/current"
upload_bucket: "adn.aetion.com/upload/marketscan/**********"
validation_path: "marketscan/adn/current"
validation_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2013-12-31"
