defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "jazz"
revision: "********"
is_k8s: True
alert_user: "@peter.imrich"
deployment_config:
  jazz: "jazz.aetion.com"
dynamic_patient_table: True
git_branch_override: "remotes/origin/jazz-marketscan-remove-hive-table"
hive_vars:
  GDR_END_DATE: "2023-12-31"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2023-12-31"
  LAB_START_DATE: "2013-12-31"
regenerate_all_flat_tables: False
service_account: "spark-operator-client-jazz"
source_files_password: ""
transform_path: "marketscan/jazz/current"
upload_bucket: "jazz.aetion.com/upload/marketscan/********"
use_smart_sampling: False
validation_path: "marketscan/jazz/current"
validation_vars:
  GDR_END_DATE: "2023-12-31"
  GDR_START_DATE: "2013-12-31"
steps_spark_config:
  full_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 50
      min_executors: 1
    spark_conf:
      spark.sql.shuffle.partitions: 10000
  default:
    spark_conf:
      spark.driver.maxResultSize: "0"
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 800
    worker_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
