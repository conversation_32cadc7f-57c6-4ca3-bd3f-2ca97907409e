defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_tiny_adm"
client: "demo"
revision: "********"
is_k8s: True
alert_user: "@Damian"
service_account: "spark-operator-client-demo"
prophecy_pipeline_prefix_override: "marketscan"
hive_vars:
  MIN_DATE: "2015-10-01"
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
validation_vars:
  MIN_DATE: "2015-10-01"
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
deployment_config:
  dds: "dds.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
regenerate_all_flat_tables: True
skip_single_shard: False
git_branch_override: "master"
transform_path: "marketscan_tiny_adm/demo/1.4"
upload_bucket: "demo.aetion.com/upload/marketscan_tiny_adm/********"
use_smart_sampling: False
validation_path: "adm/1.4"
native_data_for_adm_url: "s3://demo.aetion.com/etl/marketscan_tiny/********"
