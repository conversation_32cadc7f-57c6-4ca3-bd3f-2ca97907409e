defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "thin-uk"
client: "sanofi"
revision: "20191001/"
is_k8s: False
git_branch_override: "remotes/origin/sanofi-thin-uk-201910001"
hive_vars:
  GDR_END_DATE: "2019-07-02"
  GDR_START_DATE: "1968-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/sanofi-databricks-etl-iam"
source_files_password: ""
transform_path: "thin-uk/sanofi/20191001"
upload_bucket: "s3://sanofi.aetion.com/upload/thin-uk/20191001/"
validation_path: "thin-uk/sanofi/20191001"
