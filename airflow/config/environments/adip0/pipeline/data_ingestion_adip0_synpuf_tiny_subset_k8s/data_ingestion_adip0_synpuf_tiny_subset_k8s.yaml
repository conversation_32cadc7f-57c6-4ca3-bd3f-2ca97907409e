defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_tiny_subset"
client: "adip0"
revision: "********"
is_k8s: True
alert_user: "@dev-alert-airflow"
celeborn_feature:
  enabled: True
copy_spec: False
deployment_config:
  aep-dev: "adip0.app.dev.aetion.com"
domain: "app.dev.aetion.com"
dynamic_flat_tables: False
dynamic_patient_table: True
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
rde_revision_check: "********"
regenerate_all_flat_tables: True
service_account: "spark-operator-client-common"
source_files_password: ""
transform_path: "synpuf_tiny_subset/current"
upload_bucket: "s3://adip0.app.dev.aetion.com/upload/synpuf_tiny_subset/********"
validation_path: "synpuf_tiny_subset/current"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
steps_spark_config:
  full_job:
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "45g"
      spark_memory_overhead: "45g"
    worker_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "45g"
      spark_memory_overhead: "45g"
  full_shard_job:
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "45g"
      spark_memory_overhead: "45g"
    worker_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "45g"
      spark_memory_overhead: "45g"
  default:
    hadoop_conf:
      fs.s3a.aws.credentials.provider: "com.amazonaws.auth.WebIdentityTokenCredentialsProvider"
    spark_conf:
      spark.driver.userClassPathFirst: False
      spark.executor.userClassPathFirst: False
