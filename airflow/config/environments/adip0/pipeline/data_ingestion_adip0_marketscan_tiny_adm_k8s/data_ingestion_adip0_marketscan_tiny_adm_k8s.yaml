defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /step_group_cluster_config/tiny_k8s_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_tiny_adm"
client: "adip0"
revision: "********"
is_k8s: True
alert_user: "@dev-alert-airflow"
celeborn_feature:
  enabled: True
  spark_conf:
    spark.shuffle.manager: "org.apache.spark.shuffle.celeborn.SparkShuffleManager"
deployment_config:
  aep-dev: "adip0.app.dev.aetion.com"
domain: "app.dev.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
  MIN_DATE: "2015-10-01"
native_data_for_adm_url: "s3://adip0.app.dev.aetion.com/etl/marketscan_tiny/********"
prophecy_pipeline_prefix_override: "marketscan"
dataset_group: "LABS"
sampled_data_percentage: "50"
service_account: "spark-operator-client-common"
skip_single_shard: True
source_files_password: ""
transform_path: "marketscan_tiny_adm/demo/1.4"
upload_bucket: "s3://adip0.app.dev.aetion.com/upload/marketscan_tiny_adm/********"
use_smart_sampling: False
validation_path: "marketscan_tiny_adm/1.4"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
  MIN_DATE: "2015-10-01"
