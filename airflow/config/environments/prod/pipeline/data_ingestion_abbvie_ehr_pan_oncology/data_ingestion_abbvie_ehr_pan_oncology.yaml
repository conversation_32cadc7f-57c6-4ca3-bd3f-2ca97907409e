defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "ehr_pan_oncology"
client: "abbvie"
revision: "20210928"
is_k8s: False
alert_user: "@mikhail.koryshov"
deployment_config:
  abbvie: "abbvie.aetion.com"

git_branch_override: "remotes/origin/abbvie_pan_onc_20210928"
hive_vars:
  GDR_END_DATE: "2020-12-31"
  GDR_START_DATE: "2007-01-01"
  MASKED_BIRTH_DATE: "1930 and Earlier"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
source_files_password: "HS812VB0A2"
transform_path: "humedica/ehr_pan_oncology/current"
upload_bucket: "abbvie.aetion.com/upload/ehr_pan_oncology/20210928"
validation_path: "humedica/ehr_pan_oncology/current"
validation_vars:
  GDR_END_DATE: "2020-12-31"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 3
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    driver_node_type_id: "rd-fleet.8xlarge"
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 80
    spark_conf:
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: True
      spark.default.parallelism: 8000
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions: "-Xloggc:/dev/null -XX:+UseG1GC -XX:+UseStringDeduplication -XX:ParallelGCThreads=20 -XX:ConcGCThreads=5 -XX:InitiatingHeapOccupancyPercent=70 -XX:MaxGCPauseMillis=200"
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
      spark.network.timeout: 1200
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.broadcastTimeout: 1200
      spark.sql.objectHashAggregate.sortBased.fallbackThreshold: 1024
      spark.task.maxDirectResultSize: 2097152000
  default:
    driver_node_type_id: "rd-fleet.8xlarge"
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 60
    spark_conf:
      spark.driver.maxResultSize: "40g"
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
      spark.sql.shuffle.partitions: 2000
