defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "trinetx_colorectal"
client: "care"
revision: "20230331"
is_k8s: False
alert_user: "@alexandra.soboleva"
git_branch_override: "care-trinetx-20230331-rdc"
iam_arn: "arn:aws:iam::627533566824:instance-profile/care-databricks-etl-iam"
transform_path: "trinetx_colorectal/current"
upload_bucket: "care.aetion.com/upload/trinetx_colorectal/20230331"
use_smart_sampling: False
validation_path: "trinetx_colorectal/current"
