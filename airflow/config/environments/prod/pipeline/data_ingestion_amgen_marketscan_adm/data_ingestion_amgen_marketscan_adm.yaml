defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /databricks_adm_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_adm"
client: "amgen"
revision: "**********"
is_k8s: False
alert_user: "@mikhail.skiba"
dataset_group: "Labs + EV"
deployment_config:
  amgen: "amgen.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True

git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "2015-10-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/amgen-databricks-etl-iam"
native_data_for_adm_url: "s3://amgen.aetion.com/etl/marketscan/20240906"
regenerate_all_flat_tables: True
sampled_data_percentage: ".5"
source_files_password: ""
transform_path: "marketscan_adm/amgen/1.3"
upload_bucket: "amgen.aetion.com/upload/marketscan_adm/**********"
use_smart_sampling: False
validation_path: "marketscan_adm/1.3"
validation_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "2015-10-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 50
      min_workers: 1
    node_type_id: "c5.9xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 10000
  default:
    spark_conf:
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: True
      spark.driver.maxResultSize: "0"
      spark.network.timeout: 1200
      spark.sql.parquet.enableVectorizedReader: False
