defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /databricks_adm_step_group_config@steps_spark_config
  - _self_
dataset: "mdv_adm"
client: "astellas"
revision: "**********"
is_k8s: False
alert_user: "@Damian"
deployment_config:
  astellas: "astellas.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True

git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2023-10-31"
  GDR_START_DATE: "1900-01-01"
  MIN_DATE: "2015-10-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/astellas-databricks-etl-iam"
pre_partitioned_data_url: "astellas.aetion.com/etl/mdv_adm/20240329/"
regenerate_all_flat_tables: False
sampled_data_percentage: ".5"
source_files_password: ""
transform_path: "mdv_adm/astellas/1.2"
upload_bucket: "astellas.aetion.com/upload/mdv_adm/**********"
use_smart_sampling: False
validation_path: "mdv_adm/1.2"
validation_vars:
  GDR_END_DATE: "2023-10-31"
  GDR_START_DATE: "1900-01-01"
  MIN_DATE: "2015-10-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.sql.shuffle.partitions: 8000
  default:
    spark_conf:
      spark.sql.shuffle.partitions: 200
