defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "cmsdm"
client: "bwh"
revision: "**********"
is_k8s: False
alert_user: "@mikhail.skiba"
deployment_config:
  bwhdope: "bwh-dope.partners.org/aetion"

git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2020-12-31"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
regenerate_all_flat_tables: False
flat_tables_to_regenerate:
  - inpatient_union_flat
  - snf_union_flat
  - outpatient_union_flat
pre_partitioned_data_url: "bwh.aetion.com/etl/cmsdm/20231110/"
source_files_password: ""
transform_path: "cms_diabetes/bwh/current"
validation_path: "cms_diabetes/bwh/current"
upload_bucket: "bwh.aetion.com/upload/cmsdm/**********"
use_smart_sampling: False
dynamic_patient_table: False
dynamic_flat_tables: False
validation_vars:
  GDR_END_DATE: "2020-12-31"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    driver_node_type_id: "r5a.4xlarge"
    node_type_id: "c5d.9xlarge"
    autoscale:
      max_workers: 40
      min_workers: 40
    spark_conf:
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: False
      spark.default.parallelism: 12000
      spark.driver.maxResultSize: "20g"
      spark.executor.extraJavaOptions: "-Xloggc:/dev/null"
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.memory.offHeap.enabled: False
      spark.sql.broadcastTimeout: -1
      spark.task.cpus: 1
      spark.task.maxDirectResultSize: **********
  full_patient_job:
    node_type_id: "i3en.6xlarge"
    autoscale:
      min_workers: 1
      max_workers: 80
  full_shard_job:
    driver_node_type_id: "r5a.4xlarge"
    node_type_id: "i3en.6xlarge"
    autoscale:
      min_workers: 1
      max_workers: 80
    spark_conf:
      spark.task.cpus: 2
  default:
    autoscale:
      min_workers: 1
      max_workers: 40
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.driver.maxResultSize: "40g"
      spark.memory.offHeap.enabled: False
      spark.sql.broadcastTimeout: -1
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 1
