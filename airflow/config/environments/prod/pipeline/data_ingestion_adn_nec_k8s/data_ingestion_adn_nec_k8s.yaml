defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "nec"
client: "adn"
revision: "********"
is_k8s: True
alert_user: "@denis.kozlov"
deployment_config:
  research: "demo.aetion.com"
dynamic_patient_table: True
git_branch_override: "adn-nec-********"
hive_vars:
  GDR_END_DATE: "2025-01-31"
  GDR_START_DATE: "1972-02-10"
regenerate_all_flat_tables: True
skip_single_shard: True
service_account: "spark-operator-client-adn"
source_files_password: ""
transform_path: "nec/adn/current"
upload_bucket: "adn.aetion.com/upload/nec/********"
use_smart_sampling: True
validation_path: "nec/adn/current"
validation_vars:
  GDR_END_DATE: "2025-01-31"
  GDR_START_DATE: "1972-02-10"
steps_spark_config:
  default:
    autoscale:
      enabled: True
      initial_executors: 2
      max_executors: 5
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "5000m"
      spark_memory_overhead: "5000m"
    worker_conf:
      instance_type: "c5.4xlarge"
