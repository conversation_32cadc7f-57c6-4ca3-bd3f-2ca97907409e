defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "optum_cdm_zip5"
client: "biogen"
revision: "********"
is_k8s: True
alert_user: "@denis.kozlov"
deployment_config:
  dds: "dds.aetion.com"
dynamic_patient_table: True
git_branch_override: "biogen-optum_cdm_zip5-********"
hive_vars:
  GDR_END_DATE: "2024-09-30"
  GDR_START_DATE: "2007-01-01"
regenerate_all_flat_tables: True
service_account: "spark-operator-client-biogen"
source_files_password: ""
transform_path: "optum/cdm_zip5/biogen/current"
upload_bucket: "biogen.aetion.com/upload/optum_cdm_zip5/********"
use_smart_sampling: False
validation_path: "optum/cdm_zip5/biogen/current"
validation_vars:
  GDR_END_DATE: "2024-09-30"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  default:
    autoscale:
      enabled: True
      initial_executors: 10
      max_executors: 20
      min_executors: 1
    driver_conf:
      instance_type: "r5a.8xlarge"
      pyspark_memory_overhead: "53000m"
      spark_memory_overhead: "53000m"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: True
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.catalogImplementation: "hive"
      spark.sql.constraintPropagation.enabled: False
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 3
    worker_conf:
      instance_type: "c5.4xlarge"
  unarchiver_job_only:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 16
      min_executors: 1
    driver_conf:
      instance_type: "r5a.xlarge"
      pyspark_memory_overhead: "7000m"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
    worker_conf:
      instance_type: "c5.4xlarge"
