defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /databricks_adm_step_group_config@steps_spark_config
  - _self_
dataset: "optum_cdm_adm"
client: "bayer"
revision: "**********"
is_k8s: False
alert_user: "@mikhail.skiba"
deployment_config:
  bayer: "bayer.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
flat_tables_to_regenerate:
  - pharmacy_cost_flat
  - medical_flat
git_branch_override: "bayer_optum_cdm_adm_**********"
hive_vars:
  GDR_END_DATE: "2023-12-31"
  GDR_START_DATE: "2015-10-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bayer-databricks-etl-iam"
pre_partitioned_data_url: "bayer.aetion.com/etl/optum_cdm_adm/**********/"
regenerate_all_flat_tables: False
sampled_data_percentage: ".5"
source_files_password: ""
transform_path: "optum/cdm_adm/bayer/1.3"
upload_bucket: "bayer.aetion.com/upload/optum_cdm_adm/**********"
validation_path: "optum/cdm_adm/1.3"
validation_vars:
  GDR_END_DATE: "2023-12-31"
  GDR_START_DATE: "2015-10-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: False
      spark.rpc.numRetries: 10
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      spark.sql.parquet.binaryAsString: True
      spark.sql.parquet.columnarReaderBatchSize: 256
      spark.sql.shuffle.partitions: 40000
  default:
    autoscale:
      max_workers: 10
      min_workers: 10
    node_type_id: "r-fleet.4xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 8000
