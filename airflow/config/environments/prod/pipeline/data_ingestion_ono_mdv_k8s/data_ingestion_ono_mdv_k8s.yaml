defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: mdv
client: ono
revision: "********"
is_k8s: true
alert_user: "@oksana.antropova"
deployment_config:
  ono: ono.aetion.com
dynamic_patient_table: true
git_branch_override: ono_mdv_********
hive_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "1952-08-20"
iam_arn: arn:aws:iam::************:instance-profile/ono-databricks-etl-iam
regenerate_all_flat_tables: false
service_account: spark-operator-client-ono
source_files_password: ""
transform_path: mdv/ono/current
upload_bucket: s3://ono.aetion.com/upload/mdv/********
validation_path: mdv/ono/current
validation_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "1952-08-20"
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 60
      max_executors: 60
      min_executors: 60
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      ignore.comment.aqe: Adaptive Query Execution (AQE)
      ignore.comment.jvm_memory: Memory & JVM Optimizations
      ignore.comment.network: Network & Timeout Settings
      ignore.comment.shuffle_optimizations: Shuffle Optimizations
      ignore.comment.shuffle_push: Shuffle Push to reduce memory pressure during
        shuffle operations
      ignore.comment.spill_shuffle_memory: Spill & Shuffle Memory Management
      spark.driver.extraJavaOptions: -XX:+UseG1GC -XX:MaxGCPauseMillis=200
        -XX:+PrintGCDetails -XX:InitiatingHeapOccupancyPercent=30
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions: -XX:+UseG1GC -XX:MaxGCPauseMillis=200
        -XX:+PrintGCDetails -XX:InitiatingHeapOccupancyPercent=30
      spark.executor.heartbeatInterval: 120s
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: zstd
      spark.memory.fraction: "0.85"
      spark.memory.offHeap.enabled: false
      spark.memory.offHeap.size: 0g
      spark.memory.storageFraction: "0.15"
      spark.network.timeout: 1000s
      spark.rdd.compress: true
      spark.shuffle.compress: true
      spark.shuffle.consolidateFiles: true
      spark.shuffle.file.buffer: 2m
      spark.shuffle.io.clientThreads: 256
      spark.shuffle.io.maxRetries: "20"
      spark.shuffle.io.numConnectionsPerPeer: "4"
      spark.shuffle.io.retryWait: 15s
      spark.shuffle.io.serverThreads: 256
      spark.shuffle.push.enabled: true
      spark.shuffle.push.maxBlockSizeToPush: 5MB
      spark.shuffle.push.minShuffleSizeToWait: 5GB
      spark.shuffle.sort.bypassMergeThreshold: "500"
      spark.shuffle.spill.compress: true
      spark.shuffle.unsafe.fastMergeEnabled: true
      spark.shuffle.unsafe.sorter.spill.read.ahead.enabled: true
      spark.sql.adaptive.coalescePartitions.enabled: true
      spark.sql.adaptive.enabled: true
      spark.sql.adaptive.localShuffleReader.enabled: true
      spark.sql.adaptive.shuffle.targetPostShuffleInputSize: 512MB
      spark.sql.adaptive.skewJoin.enabled: true
      spark.sql.adaptive.skewJoin.maxSplits: "50"
      spark.sql.adaptive.skewJoin.skewedPartitionThresholdInBytes: 20g
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.catalogImplementation: hive
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 64000
      spark.task.cpus: 1
    worker_conf:
      instance_type: i3en.6xlarge
  full_patient_job:
    spark_conf:
      spark.decommission.enabled: False
      spark.task.cpus: 2
    worker_conf:
      instance_type: i3en.6xlarge
