defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum-cdm"
client: "astraz"
revision: "20221206"
is_k8s: False
alert_user: "@bartlomiej.cielecki"
deployment_config:
  astraz: "astraz.aetion.com"

git_branch_override: "astraz_optum_cdm_20221206"
hive_vars:
  GDR_END_DATE: "2022-06-30"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/astraz-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "optum/cdm/astraz/current"
upload_bucket: "astraz.aetion.com/upload/optum-cdm/20221206"
validation_path: "optum/cdm/astraz/current"
validation_vars:
  GDR_END_DATE: "2022-06-30"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 60
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.broadcastTimeout: 800
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      spark.sql.parquet.binaryAsString: True
      spark.sql.parquet.enableVectorizedReader: False
  default:
    spark_conf:
      spark.sql.shuffle.partitions: 1000
