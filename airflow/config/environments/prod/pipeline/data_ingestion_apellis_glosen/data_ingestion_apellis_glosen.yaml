defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "glosen"
client: "apellis"
revision: "20240130"
is_k8s: False
alert_user: "@mikhail.skiba"
deployment_config:
  apellis: "apellis.aetion.com"
dynamic_patient_table: True

git_branch_override: "apellis_database_20231219"
hive_vars:
  GDR_END_DATE: "2023-06-30"
  GDR_START_DATE: "1911-07-22"
iam_arn: "arn:aws:iam::627533566824:instance-profile/apellis-databricks-etl-iam"
regenerate_all_flat_tables: False
source_files_password: ""
transform_path: "glosen/apellis/current"
upload_bucket: "s3://apellis.aetion.com/upload/glosen/20240130"
validation_path: "glosen/apellis/current"
validation_vars:
  GDR_END_DATE: "2023-06-30"
  GDR_START_DATE: "1911-07-22"
