defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "tempus_nsclc_bundle"
client: "adn"
revision: "20221024"
is_k8s: False
alert_user: "@bartlomiej.cielecki"
deployment_config:
  research: "demo.aetion.com"

git_branch_override: "remotes/origin/adn-tempus_nsclc_bundle-20221024v2"
hive_vars:
  GDR_END_DATE: "2022-10-31"
  GDR_START_DATE: "1940-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "tempus_nsclc_bundle/adn/current"
upload_bucket: "adn.aetion.com/upload/tempus_nsclc_bundle/20221024"
use_smart_sampling: False
validation_path: "tempus_nsclc_bundle/adn/current"
validation_vars:
  GDR_END_DATE: "2022-10-31"
  GDR_START_DATE: "1940-01-01"
steps_spark_config:
  default:
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
