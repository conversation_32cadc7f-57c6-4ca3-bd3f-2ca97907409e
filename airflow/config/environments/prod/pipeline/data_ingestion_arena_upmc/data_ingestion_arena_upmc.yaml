defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "upmc"
client: "arena"
revision: "2022033102"
is_k8s: False
alert_user: "@alexandra.soboleva"
deployment_config:
  arena: "arena.aetion.com"

git_branch_override: "arena-upmc-2022033102-fix"
hive_vars:
  GDR_END_DATE: "2021-12-31"
  GDR_START_DATE: "2015-06-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/arena-databricks-etl-iam"
pre_partitioned_data_url: "arena.aetion.com/etl/upmc/20220331/"
source_files_password: ""
transform_path: "upmc/arena/current"
upload_bucket: "arena.aetion.com/upload/upmc/2022033102"
validation_path: "upmc/arena/current"
validation_vars:
  GDR_END_DATE: "2021-12-31"
  GDR_START_DATE: "2015-06-01"
