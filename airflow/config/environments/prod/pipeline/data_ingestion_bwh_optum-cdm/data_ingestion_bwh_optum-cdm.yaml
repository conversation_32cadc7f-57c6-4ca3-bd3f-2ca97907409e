defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum-cdm"
client: "bwh"
revision: "20240926"
is_k8s: False
alert_user: "@denis.kozlov"
dynamic_patient_table: True
use_smart_sampling: False
deployment_config:
  bwhdope: "bwh-dope.partners.org/aetion"

git_branch_override: "bwh-optum_cdm-20240926"
hive_vars:
  GDR_END_DATE: "2024-08-31"
  GDR_START_DATE: "2004-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
regenerate_all_flat_tables: False
source_files_password: ""
transform_path: "optum/cdm/bwh/current"
upload_bucket: "bwh.aetion.com/upload/optum-cdm/20240926"
validation_path: "optum/cdm/bwh"
validation_vars:
  G<PERSON>_END_DATE: "2024-08-31"
  GDR_START_DATE: "2004-01-01"
validate_data_formats: True
steps_spark_config:
  full_shard_job:
    node_type_id: "rg-fleet.8xlarge"
  full_job:
    autoscale:
      max_workers: 40
      min_workers: 40
    driver_node_type_id: "r5a.4xlarge"
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: False
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
  full_patient_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: False
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
  default:
    autoscale:
      max_workers: 10
      min_workers: 10
    driver_node_type_id: "rg-fleet.4xlarge"
    node_type_id: "rg-fleet.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.shuffle.partitions: 1000
  unarchiver_job_only:
    autoscale:
      max_workers: 16
      min_workers: 1
    driver_node_type_id: "r5a.xlarge"
    node_type_id: "c5.4xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
