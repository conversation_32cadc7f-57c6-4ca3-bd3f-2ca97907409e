defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /k8s_adm_step_group_config@steps_spark_config
  - _self_
dataset: "optum_cdm_adm"
client: "amgen"
revision: "********"
is_k8s: True
pyspark_memory_overhead_factor: ".4"
spark_memory_overhead_factor: ".2"
alert_user: "@denis.kozlov"
hive_vars:
  GDR_START_DATE: "2015-10-01"
  GDR_END_DATE: "2024-09-30"
validation_vars:
  GDR_START_DATE: "2015-10-01"
  GDR_END_DATE: "2024-09-30"
deployment_config:
  dds: "dds.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
regenerate_all_flat_tables: True
dataset_group: ""
service_account: "spark-operator-client-amgen"
sampled_data_percentage: ".5"
source_files_password: ""
git_branch_override: "amgen-optum_cdm_adm-********"
upload_bucket: "amgen.aetion.com/upload/optum_cdm_adm/********"
native_data_for_adm_url: "s3://amgen.aetion.com/etl/optum_cdm/********"
use_smart_sampling: False
transform_path: "optum/cdm_adm/amgen/1.4"
validation_path: "optum/cdm_adm/1.4"
steps_spark_config:
  adm_full_job:
    autoscale:
      enabled: True
      min_executors: 40
      initial_executors: 40
      max_executors: 80
    worker_conf:
      instance_type: "i3en.6xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: False
      spark.task.cpus: 1
      spark.sql.shuffle.partitions: 30000
  full_job:
    autoscale:
      enabled: True
      min_executors: 30
      initial_executors: 30
      max_executors: 30
    worker_conf:
      instance_type: "r5ad.8xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: True
      spark.memory.offHeap.size: "60000m"
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: False
      spark.task.cpus: 1
      spark.sql.shuffle.partitions: 40000
  full_patient_job:
    autoscale:
      enabled: True
      min_executors: 1
      initial_executors: 1
      max_executors: 60
    worker_conf:
      instance_type: "r5ad.8xlarge"
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 3
      spark.sql.parquet.enableVectorizedReader: False
      spark.memory.offHeap.enabled: True
      spark.memory.offHeap.size: "60000m"
  full_shard_job:
    autoscale:
      enabled: True
      min_executors: 1
      initial_executors: 1
      max_executors: 60
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: "r5ad.8xlarge"
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
  default:
    autoscale:
      enabled: True
      min_executors: 10
      initial_executors: 10
      max_executors: 30
    worker_conf:
      instance_type: "r5ad.xlarge"
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.sql.shuffle.partitions: 10000
      spark.task.cpus: 1
