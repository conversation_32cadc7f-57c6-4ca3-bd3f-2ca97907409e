defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "mdv"
client: "astellas"
revision: "20240111"
is_k8s: False
alert_user: "@mikhail.skiba"
deployment_config:
  astellas: "astellas.aetion.com"
dynamic_patient_table: True

git_branch_override: "astellas_mdv_encoding"
hive_vars:
  GDR_END_DATE: "2023-10-31"
  GDR_START_DATE: "1911-07-22"
iam_arn: "arn:aws:iam::627533566824:instance-profile/astellas-databricks-etl-iam"
regenerate_all_flat_tables: False
source_files_password: ""
transform_path: "mdv/astellas/current"
upload_bucket: "s3://astellas.aetion.com/upload/mdv/20240111"
use_smart_sampling: False
validation_path: "mdv/astellas/current"
validation_vars:
  <PERSON><PERSON>_END_DATE: "2023-10-31"
  GDR_START_DATE: "1911-07-22"
steps_spark_config:
  full_patient_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 8000
  unarchiver_job_only:
    autoscale:
      max_workers: 0
      min_workers: 0
    custom_tags:
      - key: "ResourceClass"
        value: "SingleNode"
    driver_node_type_id: "r6g.4xlarge"
    node_type_id: "r6g.4xlarge"
    num_workers: 0
    spark_conf:
      spark.databricks.cluster.profile: "singleNode"
      spark.hadoop.fs.s3a.connection.maximum: 100
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.master: "local[*, 4]"
