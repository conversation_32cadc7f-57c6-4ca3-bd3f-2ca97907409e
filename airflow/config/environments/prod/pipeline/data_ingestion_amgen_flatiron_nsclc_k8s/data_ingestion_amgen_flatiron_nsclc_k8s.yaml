defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: flatiron_nsclc
client: amgen
revision: "********"
is_k8s: true
alert_user: "@oksana.antropova"
alert_scientist: "@Talia"
deployment_config:
  amgen: amgen.aetion.com
dynamic_patient_table: true
git_branch_override: amgen_flatiron_nsclc_********
hive_vars:
  GDR_END_DATE: "2025-06-06"
  GDR_START_DATE: "1900-01-01"
service_account: spark-operator-client-amgen
regenerate_all_flat_tables: true
source_files_password: ""
transform_path: flatiron/amgen/nsclc/current
upload_bucket: s3://amgen.aetion.com/upload/flatiron_nsclc/********
validation_path: flatiron/amgen/nsclc/current
validation_vars:
  GDR_END_DATE: "2025-06-06"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  default:
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
  full_job:
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.memory.offHeap.enabled: false
      spark.sql.parquet.enableVectorizedReader: false
      spark.task.cpus: 2
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
    autoscale:
      enabled: true
      min_executors: 1
      initial_executors: 1
      max_executors: 20
