defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "concertai_breast"
client: "care"
revision: "**********"
is_k8s: False
alert_user: "@Damian"
deployment_config:
  care: "care.aetion.com"
dynamic_patient_table: False

git_branch_override: "care_concertai_breast_2023100901"
hive_vars:
  GDR_END_DATE: "2023-08-06"
  GDR_START_DATE: "1934-07-08"
iam_arn: "arn:aws:iam::627533566824:instance-profile/care-databricks-etl-iam"
pre_partitioned_data_url: "care.aetion.com/etl/concertai_breast/20231009"
source_files_password: ""
transform_path: "concertai_breast/care/current"
upload_bucket: "care.aetion.com/upload/concertai_breast/**********"
validation_path: "concertai_breast/care/current"
validation_vars:
  <PERSON><PERSON>_<PERSON>ND_DATE: "2023-08-06"
  GDR_START_DATE: "1934-07-08"
