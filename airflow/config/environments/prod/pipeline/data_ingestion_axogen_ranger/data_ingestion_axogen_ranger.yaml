defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "ranger"
client: "axogen"
revision: "20221011"
is_k8s: False
deployment_config:
  axogen: "axogen.aetion.com"
git_branch_override: "axogen_ranger_20221011"
hive_vars:
  GDR_END_DATE: "2022-10-04"
  GDR_START_DATE: "1900-01-01"
  REVISION: "20221011"
iam_arn: "arn:aws:iam::627533566824:instance-profile/axogen-databricks-etl-iam"
regenerate_all_flat_tables: True
skip_single_shard: True
source_files_password: ""
transform_path: "axogen/ranger/current"
upload_bucket: "axogen.aetion.com/upload/ranger/20221011"
validation_path: "axogen/ranger/current"
validation_vars:
  GDR_END_DATE: "2022-10-04"
  GDR_START_DATE: "1900-01-01"
