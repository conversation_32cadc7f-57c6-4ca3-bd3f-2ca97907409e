defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /databricks_adm_step_group_config@steps_spark_config
  - _self_
dataset: "iqvia_pharmetrics_adm"
client: "astellas"
revision: "20240918"
is_k8s: False
alert_user: "@Demian"
deployment_config:
  astellas: "astellas.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True

sampled_data_percentage: ".5"
regenerate_all_flat_tables: False
hive_vars:
  MIN_DATE: "2015-10-01"
  GDR_START_DATE: "2010-01-01"
  GDR_END_DATE: "2023-09-30"
iam_arn: "arn:aws:iam::627533566824:instance-profile/astellas-databricks-etl-iam"
pre_partitioned_data_url: "astellas.aetion.com/etl/iqvia_pharmetrics_adm/20240918/"
source_files_password: ""
git_branch_override: "astellas_iqvia_pharmetrics_adm_20240918"
transform_path: "iqvia_pharmetrics_adm/astellas/1.2"
upload_bucket: "astellas.aetion.com/upload/iqvia_pharmetrics_adm/20240918"
use_smart_sampling: False
validation_path: "iqvia_pharmetrics_adm/1.2"
validation_vars:
  MIN_DATE: "2015-10-01"
  GDR_START_DATE: "2010-01-01"
  GDR_END_DATE: "2023-09-30"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 100
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.sql.shuffle.partitions: 40000
  full_patient_job:
    autoscale:
      max_workers: 100
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.sql.shuffle.partitions: 40000
  full_shard_job:
    autoscale:
      max_workers: 100
      min_workers: 1
