defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "xcures_aml"
client: "adn"
revision: "**********"
is_k8s: False
alert_user: "@nikolay.kharin"
deployment_config:
  research: "demo.aetion.com"
dynamic_patient_table: True

git_branch_override: "adn_xcures_aml_fix"
hive_vars:
  GDR_END_DATE: "2023-08-19"
  GDR_START_DATE: "1953-02-11"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
pre_partitioned_data_url: "adn.aetion.com/etl/xcures_aml/20231103/"
regenerate_all_flat_tables: True
skip_single_shard: True
source_files_password: ""
transform_path: "xcures_aml/adn/current"
upload_bucket: "adn.aetion.com/upload/xcures_aml/**********"
use_smart_sampling: False
validation_path: "xcures_aml/adn/current"
validation_vars:
  GDR_END_DATE: "2023-08-19"
  GDR_START_DATE: "1953-02-11"
