defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: marketscan
client: amgen
revision: '********'
is_k8s: true
preprocess: True
alert_user: '@evgeniy.varganov'
deployment_config:
  amgen: amgen.aetion.com
dynamic_patient_table: true
git_branch_override: amgen-marketscan-********
hive_vars:
  EV_END_DATE: '2025-04-30'
  EV_START_DATE: '2024-10-01'
  GDR_END_DATE: '2025-04-30'
  GDR_START_DATE: '2004-12-31'
  LAB_END_DATE: '2024-04-30'
  LAB_START_DATE: '2010-01-01'
  MAX_DATE: '9999-01-01'
pyspark_memory_overhead_factor: '.4'
regenerate_all_flat_tables: true
service_account: spark-operator-client-amgen
source_files_password: ''
spark_memory_overhead_factor: '.2'
transform_path: marketscan/amgen/current
upload_bucket: amgen.aetion.com/upload/marketscan/********
use_smart_sampling: false
validation_path: marketscan/amgen/current
validation_vars:
  GDR_END_DATE: '2025-04-30'
  GDR_START_DATE: '2004-12-31'
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 60
      max_executors: 80
      min_executors: 60
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: snappy
      spark.memory.offHeap.enabled: false
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ''
  full_patient_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.task.cpus: 1
      spark.decommission.enabled: False
      spark.memory.offHeap.enabled: True
      spark.shuffle.io.maxRetries: 8
      spark.shuffle.io.numConnectionsPerPeer: 2
      spark.sql.shuffle.partitions: 2000
      spark.storage.decommission.enabled: False
      spark.storage.decommission.rddBlocks.enabled: False
      spark.storage.decommission.shuffleBlocks.enabled: False
      spark.memory.offHeap.size: "40g"
      spark.memory.fraction: 0.85
      spark.memory.storageFraction: 0.1
      spark.sql.adaptive.coalescePartitions.minPartitionSize: "512MB"
      spark.sql.adaptive.coalescePartitions.initialPartitionNum: 1200
      spark.sql.adaptive.coalescePartitions.parallelismFirst: False
      spark.sql.adaptive.skewJoin.skewedPartitionThresholdInBytes: "2GB"
      spark.sql.adaptive.skewJoin.skewedPartitionFactor: 3
      spark.sql.adaptive.optimize.skewJoin.enabled: True
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.kryo.registrationRequired: False
      spark.kryo.unsafe: True
      spark.kryo.referenceTracking: False
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
      spark.shuffle.compress: True
      spark.rdd.compress: True
      spark.shuffle.spill.compress: True
      spark.io.compression.codec: "zstd"
      spark.io.compression.zstd.level: 6
      spark.shuffle.io.clientThreads: 16
      spark.shuffle.io.serverThreads: 16
      spark.shuffle.io.threads: 64
      spark.shuffle.io.retryWait: "30s"
      spark.shuffle.file.buffer: "256k"
      spark.shuffle.unsafe.file.output.buffer: "1m"
      spark.network.timeout: "600s"
      spark.shuffle.io.connectionTimeout: "300s"
    worker_conf:
      cores: 20
      memory: "100g"
      spark_memory_overhead: "30g"
      instance_type: "i3en.6xlarge"
      memory_on_disk: "750G"
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ''
