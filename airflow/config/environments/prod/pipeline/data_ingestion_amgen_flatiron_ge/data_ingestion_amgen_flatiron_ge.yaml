defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "flatiron_ge"
client: "amgen"
revision: "20240831"
is_k8s: False
alert_user: "@evgeniy.varganov"
deployment_config:
  amgen: "amgen.aetion.com"
dynamic_patient_table: True

git_branch_override: "amgen-flatiron_ge-20240831"
hive_vars:
  GDR_END_DATE: "2024-07-31"
  GDR_START_DATE: "1990-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/amgen-databricks-etl-iam"
source_files_password: ""
transform_path: "flatiron/amgen/ge/current"
upload_bucket: "amgen.aetion.com/upload/flatiron_ge/20240831"
validation_path: "flatiron/amgen/gastric/current"
validation_vars:
  GDR_END_DATE: "2024-07-31"
  GDR_START_DATE: "1990-01-01"
steps_spark_config:
  full_patient_job:
    spark_conf:
      spark.default.parallelism: 6000
      spark.memory.offHeap.enabled: False
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 6000
      spark.task.cpus: 2
  full_shard_job:
    spark_conf:
      spark.default.parallelism: 6000
      spark.memory.offHeap.enabled: False
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 6000
      spark.task.cpus: 2
  default:
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
