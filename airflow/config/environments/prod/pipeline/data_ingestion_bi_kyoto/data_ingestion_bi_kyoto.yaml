defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "kyoto"
client: "bi"
revision: "20200206"
is_k8s: False
git_branch_override: "remotes/origin/bi_kyoto_20200206v2"
hive_vars:
  GDR_END_DATE: "2019-10-31"
  GDR_START_DATE: "1985-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bi-databricks-etl-iam"
source_files_password: "eT3GxXC6"
transform_path: "kyoto/bi20200206"
upload_bucket: "bi.aetion.com/upload/kyoto/20200206"
validation_path: "kyoto/bi20200206"
