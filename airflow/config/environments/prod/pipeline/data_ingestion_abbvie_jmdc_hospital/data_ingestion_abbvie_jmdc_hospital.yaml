defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "jmdc_hospital"
client: "abbvie"
revision: "20240927"
is_k8s: False
alert_user: "@nikolay.kharin"
deployment_config:
  abbvie: "abbvie.aetion.com"
dynamic_patient_table: True

git_branch_override: "abbvie-jmdc_hospital-20240927"
hive_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "1911-10-12"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
regenerate_all_flat_tables: True
transform_path: "jmdc_hospital/abbvie/current"
upload_bucket: "abbvie.aetion.com/upload/jmdc_hospital/20240927"
use_smart_sampling: False
validation_path: "jmdc_hospital/abbvie/current"
validation_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "1911-10-12"
steps_spark_config:
  full_patient_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "i3en.12xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 8000
  full_shard_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    driver_node_type_id: "r-fleet.4xlarge"
    node_type_id: "r-fleet.4xlarge"
    spark_conf:
      spark.task.cpus: 2
  default:
    num_workers: 10
    spark_conf:
      spark.default.parallelism: 1000
      spark.driver.maxResultSize: 0
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.sql.shuffle.partitions: 1000
    spark_version: "14.3.x-scala2.12"
  unarchiver_job_only:
    autoscale:
      max_workers: 0
      min_workers: 0
    custom_tags:
      - key: "ResourceClass"
        value: "SingleNode"
    driver_node_type_id: "r6g.4xlarge"
    node_type_id: "r6g.4xlarge"
    num_workers: 0
    spark_conf:
      spark.databricks.cluster.profile: "singleNode"
      spark.hadoop.fs.s3a.connection.maximum: 100
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.master: "local[*, 4]"
