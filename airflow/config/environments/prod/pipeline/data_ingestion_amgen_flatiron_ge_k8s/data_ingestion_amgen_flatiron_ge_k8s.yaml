defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: flatiron_ge
client: amgen
revision: "********"
is_k8s: true
alert_user: "@oksana.antropova"
alert_scientist: "@Lacey"
deployment_config:
  amgen: amgen.aetion.com
dynamic_patient_table: true
git_branch_override: amgen_flatiron_ge_********
hive_vars:
  GDR_END_DATE: "2025-06-04"
  GDR_START_DATE: "1990-01-01"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: true
service_account: spark-operator-client-amgen
skip_single_shard: true
source_files_password: ""
transform_path: flatiron_ge/amgen/current
upload_bucket: amgen.aetion.com/upload/flatiron_ge/********
validation_path: flatiron/amgen/gastric/current
validation_vars:
  GDR_END_DATE: "2025-06-04"
  GDR_START_DATE: "1990-01-01"
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 20
      min_executors: 1
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.memory.offHeap.enabled: false
      spark.sql.parquet.enableVectorizedReader: false
      spark.task.cpus: 2
  default:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 20
      min_executors: 1
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.driver.maxResultSize: 0g
      spark.task.cpus: 2
    worker_conf:
      instance_type: r5.4xlarge
      memory_on_disk: 128G
