defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "concertai_nsclc"
client: "care"
revision: "20240415"
is_k8s: False
alert_user: "@evgeniy.varganov"
deployment_config:
  care: "care.aetion.com"
dynamic_patient_table: True

git_branch_override: "care-concertai_nsclc-20240415"
hive_vars:
  GDR_END_DATE: "2023-08-04"
  GDR_START_DATE: "1934-07-08"
iam_arn: "arn:aws:iam::627533566824:instance-profile/care-databricks-etl-iam"
regenerate_all_flat_tables: True
transform_path: "concerto/nsclc/care/current"
upload_bucket: "care.aetion.com/upload/concertai_nsclc/20240415"
use_smart_sampling: False
validation_path: "concerto/nsclc/care/current"
validation_vars:
  G<PERSON>_END_DATE: "2023-08-04"
  GDR_START_DATE: "1934-07-08"
