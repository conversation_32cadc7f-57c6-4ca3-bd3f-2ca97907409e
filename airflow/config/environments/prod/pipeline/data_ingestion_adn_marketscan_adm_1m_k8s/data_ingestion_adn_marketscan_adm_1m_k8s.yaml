defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /k8s_adm_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_adm_1m"
client: "adn"
revision: "********"
is_k8s: True
alert_user: "@evgeniy.varganov"
deployment_config:
  dds: "dds.aetion.com"

git_branch_override: "adn-marketscan_adm_1m-********"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
iam_arn: "arn:aws:iam::************:instance-profile/adn-databricks-etl-iam"
sampled_data_percentage: ".5"
service_account: "spark-operator-client-adn"
skip_single_shard: True
transform_path: "marketscan_adm_1m/adn/current_filter"
upload_bucket: "adn.aetion.com/upload/marketscan_adm_1m/********"
validation_path: "marketscan_adm_1m/adn/current_filter"
