defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "adn"
revision: "20190601"
is_k8s: False
alert_user: "@mikhail.skiba"
deployment_config:
  demo: "demo.aetion.com"
git_branch_override: "remotes/origin/demo_marketscan_pl_20190601"
hive_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-09-30"
  LAB_START_DATE: "2013-12-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
source_files_password: ""
transform_path: "marketscan/adn/current"
upload_bucket: "adn.aetion.com/upload/marketscan/20190601"
validation_path: "marketscan/adn/current"
validation_vars:
  G<PERSON>_<PERSON>ND_DATE: "2018-09-30"
  GDR_START_DATE: "2013-12-31"
