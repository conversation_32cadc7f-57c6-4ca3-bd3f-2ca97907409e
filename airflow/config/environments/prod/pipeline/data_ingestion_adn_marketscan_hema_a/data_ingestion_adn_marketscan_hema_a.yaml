defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_hema_a"
client: "adn"
revision: "2021110502"
is_k8s: False
alert_user: "@aleksei.beltyukov"
deployment_config:
  biomarin: "biomarin.aetion.com"
git_branch_override: "remotes/origin/biomarin_set_a_new_dataset"
hive_vars:
  GDR_END_DATE: "2020-12-31"
  GDR_START_DATE: "2015-12-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
pre_partitioned_data_url: "adn.aetion.com/etl/marketscan_hema/2021100101"
source_files_password: ""
transform_path: "marketscan_hema/set_a/bmrn20211105"
upload_bucket: "s3://adn.aetion.com/upload/marketscan_hema_a/2021110502"
validation_path: "marketscan_hema/bmrn20211105"
validation_vars:
  GDR_END_DATE: "2020-12-31"
  GDR_START_DATE: "2015-12-31"
steps_spark_config:
  full_job:
    num_workers: 40
  full_shard_job:
    spark_conf:
      spark.executor.cores: "8"
  default:
    spark_conf:
      spark.network.timeout: 1200
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
