defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "integrated_10sample"
client: "abbvie"
revision: "20210108"
is_k8s: False
git_branch_override: "remotes/origin/integrated_10sample_20210108"
hive_vars:
  GDR_END_DATE: "2020-06-30"
  GDR_START_DATE: "2007-01-01"
  MASKED_BIRTH_DATE: "1930 and Earlier"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
source_files_password: "QW02BD83M0"
transform_path: "humedica/ehr_integrated_10_sample/abbvie/20210108"
upload_bucket: "abbvie.aetion.com/upload/integrated_10sample/20210108"
validation_path: "humedica/ehr_integrated_10_sample/abbvie/20210108"
steps_spark_config:
  full_job:
    num_workers: 40
