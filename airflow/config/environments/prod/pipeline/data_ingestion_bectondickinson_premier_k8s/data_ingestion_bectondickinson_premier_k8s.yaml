defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "premier"
client: "bectondickinson"
revision: "********"
is_k8s: True
alert_user: "@evgeniy.varganov"
deployment_config:
  bectondickinson: "bectondickinson.aetion.com"
dynamic_patient_table: True

git_branch_override: "bectondickinson-premier-********"
hive_vars:
  GDR_END_DATE: "2023-12-31"
  GDR_START_DATE: "2016-01-11"
iam_arn: "arn:aws:iam::************:instance-profile/bectondickinson-databricks-etl-iam"
regenerate_all_flat_tables: True
service_account: "spark-operator-client-bectondickinson"
source_files_password: ""
transform_path: "premier/bectondickinson/current"
upload_bucket: "s3://bectondickinson.aetion.com/upload/premier/********"
use_smart_sampling: False
validation_path: "premier/bectondickinson/current"
validation_vars:
  GDR_END_DATE: "2023-12-31"
  GDR_START_DATE: "2016-01-11"
steps_spark_config:
  full_job:
    autoscale:
      enabled: True
      initial_executors: 60
      max_executors: 60
      min_executors: 60
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "i3en.6xlarge"
