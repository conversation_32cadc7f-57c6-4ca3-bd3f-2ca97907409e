defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "komodo"
client: "bwhdope"
revision: "********"
is_k8s: True
alert_user: "@mikhail.skiba"
deployment_config:
  bwhdope: "bwhdope.aetion.com"
dynamic_patient_table: True

git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2024-05-30"
  GDR_START_DATE: "2010-01-01"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: True
service_account: "spark-operator-client-bwhdope"
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: "komodo/bwh/current"
upload_bucket: "s3://bwhdope.aetion.com/upload/komodo/********"
use_smart_sampling: False
validation_path: "komodo/bwh/current"
validation_vars:
  GDR_END_DATE: "2024-05-30"
  GDR_START_DATE: "2010-01-01"
steps_spark_config:
  full_job:
    autoscale:
      enabled: True
      initial_executors: 60
      max_executors: 60
      min_executors: 60
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      ignore.comment.aqe: "Adaptive Query Execution (AQE)"
      ignore.comment.jvm_memory: "Memory & JVM Optimizations"
      ignore.comment.network: "Network & Timeout Settings"
      ignore.comment.shuffle_optimizations: "Shuffle Optimizations"
      ignore.comment.shuffle_push: "Shuffle Push to reduce memory pressure during shuffle operations"
      ignore.comment.spill_shuffle_memory: "Spill & Shuffle Memory Management"
      spark.driver.extraJavaOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+PrintGCDetails -XX:InitiatingHeapOccupancyPercent=30"
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+PrintGCDetails -XX:InitiatingHeapOccupancyPercent=30"
      spark.executor.heartbeatInterval: "120s"
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "zstd"
      spark.memory.fraction: "0.85"
      spark.memory.offHeap.enabled: False
      spark.memory.offHeap.size: "0g"
      spark.memory.storageFraction: "0.15"
      spark.network.timeout: "1000s"
      spark.rdd.compress: True
      spark.shuffle.compress: True
      spark.shuffle.consolidateFiles: True
      spark.shuffle.file.buffer: "2m"
      spark.shuffle.io.clientThreads: 256
      spark.shuffle.io.maxRetries: "20"
      spark.shuffle.io.numConnectionsPerPeer: "4"
      spark.shuffle.io.retryWait: "15s"
      spark.shuffle.io.serverThreads: 256
      spark.shuffle.push.enabled: True
      spark.shuffle.push.maxBlockSizeToPush: "5MB"
      spark.shuffle.push.minShuffleSizeToWait: "5GB"
      spark.shuffle.sort.bypassMergeThreshold: "500"
      spark.shuffle.spill.compress: True
      spark.shuffle.unsafe.fastMergeEnabled: True
      spark.shuffle.unsafe.sorter.spill.read.ahead.enabled: True
      spark.sql.adaptive.coalescePartitions.enabled: True
      spark.sql.adaptive.enabled: True
      spark.sql.adaptive.localShuffleReader.enabled: True
      spark.sql.adaptive.shuffle.targetPostShuffleInputSize: "512MB"
      spark.sql.adaptive.skewJoin.enabled: True
      spark.sql.adaptive.skewJoin.maxSplits: "50"
      spark.sql.adaptive.skewJoin.skewedPartitionThresholdInBytes: "20g"
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.catalogImplementation: "hive"
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 64000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "i3en.6xlarge"
      spark_memory_overhead: "20g"
  default:
    spark_conf:
      JNAME: "zulu17-ca-amd64"
      aetion.dataset.automaticGdrFiltering: False
  unarchiver_job_only:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 16
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
    worker_conf:
      instance_type: "m5d.4xlarge"
