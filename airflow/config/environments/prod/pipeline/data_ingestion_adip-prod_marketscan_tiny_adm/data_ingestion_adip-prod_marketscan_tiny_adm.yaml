defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /databricks_adm_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_tiny_adm"
client: "adip-prod"
revision: "20231114"
is_k8s: False
alert_user: "@dev-alert-airflow"
deployment_config:
  aep-prod: "adip-prod.app.us-east-1.aetion.com"
domain: "app.us-east-1.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adip-prod-infra-prod-g-databricks-etl-iam"
pre_partitioned_data_url: "adip-prod.app.us-east-1.aetion.com/upload/marketscan_tiny_adm/20231114"
sampled_data_percentage: "50"
source_files_password: ""
transform_path: "marketscan_adm/adn/1.1"
upload_bucket: "s3://adip-prod.app.us-east-1.aetion.com/upload/marketscan_tiny_adm/20231114"
use_smart_sampling: False
validation_path: "marketscan_adm/1.1"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
