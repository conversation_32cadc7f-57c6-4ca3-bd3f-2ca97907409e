defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "abbvie"
revision: "20250205"
is_k8s: False
alert_user: "@denis.kozlov"
deployment_config:
  abbvie: "abbvie.aetion.com"
dynamic_patient_table: True

hive_vars:
  EV_END_DATE: "2024-01-31"
  EV_START_DATE: "2023-06-30"
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "1999-12-31"
  LAB_END_DATE: "2024-06-30"
  LAB_START_DATE: "1999-12-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "marketscan/abbvie/current"
upload_bucket: "abbvie.aetion.com/upload/marketscan/20250205"
use_smart_sampling: False
validation_path: "marketscan/abbvie/current"
validation_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "1999-12-31"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 30
      min_workers: 30
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 10000
  default:
    spark_conf:
      spark.driver.maxResultSize: "40g"
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
