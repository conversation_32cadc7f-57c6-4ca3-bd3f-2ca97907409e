defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "verana_qdata_namd"
client: "adn"
revision: "20230726"
is_k8s: False
alert_user: "@mikhail.skiba"
deployment_config:
  research: "demo.aetion.com"

git_branch_override: "adn-verana_qdata_namd-20230726"
hive_vars:
  GDR_END_DATE: "2021-04-30"
  GDR_START_DATE: "2016-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
source_files_password: ""
transform_path: "bdata/current"
upload_bucket: "adn.aetion.com/upload/verana_qdata_namd/20230726"
use_smart_sampling: False
validation_path: "bdata/current"
validation_vars:
  GDR_END_DATE: "2021-04-30"
  GDR_START_DATE: "2016-01-01"
