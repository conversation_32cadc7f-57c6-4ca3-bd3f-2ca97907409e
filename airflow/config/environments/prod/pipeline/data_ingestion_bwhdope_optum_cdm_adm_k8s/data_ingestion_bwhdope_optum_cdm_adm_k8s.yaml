defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "optum_cdm_adm"
client: "bwhdope"
revision: "********"
is_k8s: True
alert_user: "@denis.kozlov"
dataset_group: ""
deployment_config:
  dds: "dds.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
git_branch_override: "bwhdope-optum_cdm_adm-********"
hive_vars:
  GDR_END_DATE: "2024-11-30"
  GDR_START_DATE: "2015-10-01"
native_data_for_adm_url: "s3://bwhdope.aetion.com/etl/optum_cdm/********"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: True
sampled_data_percentage: ".5"
service_account: "spark-operator-client-bwhdope"
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: "optum/cdm_adm/bwh/1.4"
upload_bucket: "bwhdope.aetion.com/upload/optum_cdm_adm/********"
use_smart_sampling: False
validation_path: "optum/cdm_adm/1.4"
validation_vars:
  GDR_END_DATE: "2024-11-30"
  GDR_START_DATE: "2015-10-01"
steps_spark_config:
  adm_full_job:
    autoscale:
      enabled: True
      initial_executors: 40
      max_executors: 80
      min_executors: 40
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 30000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "r5ad.8xlarge"
  full_job:
    autoscale:
      enabled: True
      initial_executors: 40
      max_executors: 40
      min_executors: 40
    driver_conf:
      instance_type: "r5a.8xlarge"
      pyspark_memory_overhead: "53000m"
      spark_memory_overhead: "53000m"
    spark_conf:
      ignore.comment.aqe: "Adaptive Query Execution (AQE)"
      ignore.comment.jvm_memory: "Memory & JVM Optimizations"
      ignore.comment.network: "Network & Timeout Settings"
      ignore.comment.shuffle_optimizations: "Shuffle Optimizations"
      ignore.comment.spill_shuffle_memory: "Spill & Shuffle Memory Management"
      spark.driver.extraJavaOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=500 -XX:InitiatingHeapOccupancyPercent=40"
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=500 -XX:InitiatingHeapOccupancyPercent=40"
      spark.executor.heartbeatInterval: "120s"
      spark.memory.fraction: "0.7"
      spark.memory.storageFraction: "0.3"
      spark.network.timeout: "1000s"
      spark.rdd.compress: True
      spark.shuffle.compress: True
      spark.shuffle.consolidateFiles: True
      spark.shuffle.io.clientThreads: 256
      spark.shuffle.io.maxRetries: "20"
      spark.shuffle.io.retryWait: "15s"
      spark.shuffle.io.serverThreads: 256
      spark.shuffle.sort.bypassMergeThreshold: "1000"
      spark.shuffle.spill.compress: True
      spark.sql.adaptive.coalescePartitions.enabled: True
      spark.sql.adaptive.enabled: True
      spark.sql.adaptive.localShuffleReader.enabled: True
      spark.sql.adaptive.shuffle.targetPostShuffleInputSize: "256MB"
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: "5000"
    worker_conf:
      instance_type: "i3en.6xlarge"
      spark_memory_overhead: "70g"
  full_shard_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: "r5ad.8xlarge"
  default:
    autoscale:
      enabled: True
      initial_executors: 10
      max_executors: 30
      min_executors: 10
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.sql.shuffle.partitions: 10000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "r5ad.xlarge"
