defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum-cdm"
client: "bayer"
revision: "20240515"
is_k8s: False
alert_user: "@denis.kozlov"
deployment_config:
  bayerriverhf: "bayerriverhf.aetion.com"
dynamic_patient_table: True

flat_tables_to_regenerate:
  - confinement_flat
  - medical_flat
hive_vars:
  GDR_END_DATE: "2023-03-31"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bayer-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "optum/cdm/bibayer/current"
upload_bucket: "bayer.aetion.com/upload/optum-cdm/20240515"
validation_path: "optum/cdm/bibayer/current"
validation_vars:
  GDR_END_DATE: "2023-03-31"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 40
      min_workers: 40
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: False
      spark.rpc.numRetries: 10
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      spark.sql.parquet.binaryAsString: True
      spark.sql.parquet.columnarReaderBatchSize: 256
      spark.sql.shuffle.partitions: 20000
  full_patient_job:
    node_type_id: "i3en.6xlarge"
    autoscale:
      min_workers: 1
      max_workers: 80
  full_shard_job:
    autoscale:
      min_workers: 1
      max_workers: 80
    spark_conf:
      spark.task.cpus: 2
  default:
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.shuffle.partitions: 1000
