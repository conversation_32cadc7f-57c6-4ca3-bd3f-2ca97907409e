defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "bdata"
client: "bdata"
revision: "2023032303"
is_k8s: False
alert_user: "@mikhail.skiba"
deployment_config:
  research: "demo.aetion.com"

git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2022-07-01"
  GDR_START_DATE: "2016-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bdata-databricks-etl-iam"
pre_partitioned_data_url: "bdata.aetion.com/etl/bdata/20230323"
regenerate_all_flat_tables: False
skip_single_shard: True
source_files_password: ""
transform_path: "bdata/current"
upload_bucket: "bdata.aetion.com/upload/bdata/2023032303"
use_smart_sampling: False
validation_path: "bdata/current"
validation_vars:
  GDR_END_DATE: "2022-07-01"
  GDR_START_DATE: "2016-01-01"
