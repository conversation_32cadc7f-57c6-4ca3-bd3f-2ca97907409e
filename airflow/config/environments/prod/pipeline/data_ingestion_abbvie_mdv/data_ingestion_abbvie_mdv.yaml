defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "mdv"
client: "abbvie"
revision: "20241213"
is_k8s: False
alert_user: "@evgeniy.varganov"
deployment_config:
  abbvie: "abbvie.aetion.com"
dynamic_patient_table: True
git_branch_override: "abbvie-mdv-20241213"
hive_vars:
  GDR_END_DATE: "2024-05-31"
  GDR_START_DATE: "1911-07-22"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
regenerate_all_flat_tables: False
source_files_password: ""
transform_path: "mdv/abbvie/current"
upload_bucket: "s3://abbvie.aetion.com/upload/mdv/20241213"
use_smart_sampling: False
validation_path: "mdv/abbvie/current"
validation_vars:
  GDR_END_DATE: "2024-05-31"
  GDR_START_DATE: "1911-07-22"
steps_spark_config:
  full_patient_job:
    node_type_id: "r-fleet.4xlarge"
  unarchiver_job_only:
    autoscale:
      max_workers: 0
      min_workers: 0
    custom_tags:
      - key: "ResourceClass"
        value: "SingleNode"
    driver_node_type_id: "r6g.4xlarge"
    node_type_id: "r6g.4xlarge"
    num_workers: 0
    spark_conf:
      spark.databricks.cluster.profile: "singleNode"
      spark.hadoop.fs.s3a.connection.maximum: 100
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.master: "local[*, 4]"
