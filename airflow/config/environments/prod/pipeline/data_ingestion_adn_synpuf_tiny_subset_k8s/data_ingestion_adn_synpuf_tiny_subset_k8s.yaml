defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_k8s_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_tiny_subset"
client: "adn"
revision: "********"
is_k8s: True
alert_user: "@Damian"
deployment_config:
  ci: "ci.aetion.com"
  qa: "cms.dev.aetion.com"
dynamic_flat_tables: False
dynamic_patient_table: True
git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
iam_arn: "arn:aws:iam::************:instance-profile/adn-databricks-etl-iam"
regenerate_all_flat_tables: True
service_account: "spark-operator-client-adn"
skip_single_shard: False
source_files_password: ""
transform_path: "synpuf_tiny_subset/current"
upload_bucket: "adn.aetion.com/upload/synpuf_tiny_subset/********"
validation_path: "synpuf_tiny_subset/current"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
