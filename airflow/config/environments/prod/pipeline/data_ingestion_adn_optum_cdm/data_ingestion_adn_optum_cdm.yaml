defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum_cdm"
client: "adn"
revision: "20241106"
is_k8s: False
alert_user: "@oksana.antropova"
deployment_config:
  research: "demo.aetion.com"
  bayerriverhf: "bayerriverhf.aetion.com"
  gilead: "realtgilead.aetion.com"

regenerate_all_flat_tables: True
dynamic_patient_table: True
git_branch_override: "adn_optum_cdm_20241106"
hive_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
transform_path: "optum/cdm/adn/current"
upload_bucket: "adn.aetion.com/upload/optum_cdm/20241106"
validation_path: "optum/cdm/adn/current"
validation_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    autoscale:
      min_workers: 80
      max_workers: 80
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: False
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: False
      spark.task.cpus: 1
      spark.sql.shuffle.partitions: 20000
  full_patient_job:
    node_type_id: "i3en.6xlarge"
    autoscale:
      min_workers: 1
      max_workers: 80
  full_shard_job:
    autoscale:
      min_workers: 1
      max_workers: 80
    spark_conf:
      spark.task.cpus: 2
  default:
    autoscale:
      min_workers: 10
      max_workers: 10
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.shuffle.partitions: 1000
  unarchiver_job_only:
    autoscale:
      min_workers: 1
      max_workers: 16
    driver_node_type_id: "r5a.xlarge"
    node_type_id: "c5.4xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
