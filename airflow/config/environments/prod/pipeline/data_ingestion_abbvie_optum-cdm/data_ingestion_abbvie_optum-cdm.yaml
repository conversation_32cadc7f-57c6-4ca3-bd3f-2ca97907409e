defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum-cdm"
client: "abbvie"
revision: "20241001"
is_k8s: False
alert_user: "@oksana.antropova"
deployment_config:
  abbvie: "abbvie.aetion.com"
dynamic_patient_table: True

git_branch_override: "abbvie_optum_cdm_20241001"
hive_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "2000-05-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "optum/cdm/abbvie/current"
upload_bucket: "abbvie.aetion.com/upload/optum-cdm/20241001"
use_smart_sampling: False
validation_path: "optum/cdm/abbvie/current"
validation_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "2000-05-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 60
      min_workers: 60
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: False
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: False
      spark.task.cpus: 1
  full_patient_job:
    autoscale:
      max_workers: 60
      min_workers: 1
    node_type_id: "i3en.6xlarge"
  full_shard_job:
    autoscale:
      max_workers: 60
      min_workers: 1
    spark_conf:
      spark.task.cpus: 2
  default:
    autoscale:
      max_workers: 10
      min_workers: 10
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.shuffle.partitions: 1000
  unarchiver_job_only:
    autoscale:
      max_workers: 16
      min_workers: 1
    driver_node_type_id: "r5a.xlarge"
    node_type_id: "c5.4xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
