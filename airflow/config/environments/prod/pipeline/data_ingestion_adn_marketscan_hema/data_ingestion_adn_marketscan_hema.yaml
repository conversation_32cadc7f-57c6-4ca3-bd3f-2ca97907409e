defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_hema"
client: "adn"
revision: "20220318"
is_k8s: False
alert_user: "@nikolay.kharin"
deployment_config:
  biomarin: "biomarin.aetion.com"

git_branch_override: "remotes/origin/biomarin_marketscan_hema_20220318"
hive_vars:
  GDR_END_DATE: "2021-06-30"
  GDR_START_DATE: "2016-06-30"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
pre_partitioned_data_url: "adn.aetion.com/etl/marketscan/20220318/"
source_files_password: ""
transform_path: "marketscan_hema/biomarin/current"
upload_bucket: "s3://adn.aetion.com/upload/marketscan_hema/20220318"
validation_path: "marketscan_hema/biomarin/current"
validation_vars:
  G<PERSON>_END_DATE: "2021-06-30"
  GDR_START_DATE: "2016-06-30"
steps_spark_config:
  full_job:
    num_workers: 40
  full_shard_job:
    spark_conf:
      spark.memory.offHeap.enabled: False
      spark.task.cpus: 2
  default:
    num_workers: 40
    spark_conf:
      spark.network.timeout: 1200
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
