defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /databricks_adm_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_adm"
client: "adn"
revision: "20241206"
is_k8s: False
alert_user: "@mikhail.skiba"
dataset_group: "LABS"
deployment_config:
  dds: "dds.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
git_branch_override: "adn-maketscan_adm_14-20241206"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
native_data_for_adm_url: "s3://adn.aetion.com/etl/marketscan/20190601"
regenerate_all_flat_tables: False
sampled_data_percentage: ".5"
source_files_password: ""
transform_path: "marketscan_adm/adn/1.4"
upload_bucket: "adn.aetion.com/upload/marketscan_adm/20241206"
use_smart_sampling: False
validation_path: "marketscan_adm/1.4"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 90
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions: "-Xloggc:/dev/null -XX:+UseG1GC -XX:+UseStringDeduplication -XX:ParallelGCThreads=20 -XX:ConcGCThreads=5 -XX:InitiatingHeapOccupancyPercent=70 -XX:MaxGCPauseMillis=200"
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.maxDirectResultSize: 2097152000
  default:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 10000
