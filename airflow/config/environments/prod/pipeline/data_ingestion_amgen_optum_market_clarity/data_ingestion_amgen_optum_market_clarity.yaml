defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum_market_clarity"
client: "amgen"
revision: "20230824"
is_k8s: False
alert_user: "@bartlomiej.cielecki"
deployment_config:
  amgen: "amgen.aetion.com"
dynamic_patient_table: True

git_branch_override: "amgen-optum_market_clarity-20230824"
hive_vars:
  GDR_END_DATE: "2022-12-31"
  GDR_START_DATE: "2007-01-01"
  MASKED_BIRTH_DATE: "1932 and Earlier"
iam_arn: "arn:aws:iam::627533566824:instance-profile/amgen-databricks-etl-iam"
regenerate_all_flat_tables: False
source_files_password: ""
transform_path: "humedica/optum_market_clarity/amgen/current"
upload_bucket: "amgen.aetion.com/upload/optum_market_clarity/20230824"
use_smart_sampling: False
validation_path: "humedica/optum_market_clarity/amgen/current"
validation_vars:
  GDR_END_DATE: "2022-12-31"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "c5.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: True
      spark.default.parallelism: 8000
      spark.executor.extraJavaOptions: "-Xloggc:/dev/null -XX:+UseG1GC -XX:+UseStringDeduplication -XX:ParallelGCThreads=24 -XX:ConcGCThreads=10 -XX:InitiatingHeapOccupancyPercent=70 -XX:MaxGCPauseMillis=10000"
      spark.memory.offHeap.enabled: False
      spark.sql.adaptive.autoBroadcastJoinThreshold: -1
      spark.sql.adaptive.forceOptimizeSkewedJoin: True
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
      spark.sql.shuffle.partitions: 8000
      spark.task.cpus: 1
  full_patient_job:
    autoscale:
      max_workers: 80
      min_workers: 1
    enable_elastic_disk: True
    node_type_id: "i4i.8xlarge"
    spark_conf:
      spark.task.cpus: 1
  default:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "c5.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: True
      spark.default.parallelism: 2000
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions: "-Xloggc:/dev/null -XX:+UseG1GC -XX:+UseStringDeduplication -XX:ParallelGCThreads=24 -XX:ConcGCThreads=10 -XX:InitiatingHeapOccupancyPercent=70 -XX:MaxGCPauseMillis=10000"
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.memory.offHeap.enabled: False
      spark.sql.adaptive.autoBroadcastJoinThreshold: -1
      spark.sql.adaptive.forceOptimizeSkewedJoin: True
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
      spark.sql.maxMetadataStringLength: 200
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 1
  unarchiver_job_only:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "c5.4xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
