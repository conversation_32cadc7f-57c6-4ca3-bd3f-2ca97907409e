defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "iqvia_pharmetrics"
client: "astellas"
revision: "20240111"
is_k8s: False
alert_user: "@rafal.kwi<PERSON>kowski"
deployment_config:
  astellas: "astellas.aetion.com"
dynamic_patient_table: True

git_branch_override: "astellas_iqvia_pharmetrics_20240111_native"
hive_vars:
  GDR_END_DATE: "2023-09-30"
  GDR_START_DATE: "2010-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/astellas-databricks-etl-iam"
source_files_password: ""
transform_path: "iqvia_pharmetrics/astellas/current"
upload_bucket: "astellas.aetion.com/upload/iqvia_pharmetrics/20240111"
use_smart_sampling: False
validation_path: "iqvia_pharmetrics/astellas/current"
validation_vars:
  GDR_END_DATE: "2023-09-30"
  GDR_START_DATE: "2010-01-01"
