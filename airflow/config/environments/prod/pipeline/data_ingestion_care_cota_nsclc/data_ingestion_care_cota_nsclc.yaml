defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "cota_nsclc"
client: "care"
revision: "20240415"
is_k8s: False
alert_user: "@evgeniy.varganov"
deployment_config:
  care: "care.aetion.com"
dynamic_patient_table: True

git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2023-07-02"
  GDR_START_DATE: "1955-07-02"
iam_arn: "arn:aws:iam::627533566824:instance-profile/care-databricks-etl-iam"
regenerate_all_flat_tables: False
skip_single_shard: True
source_files_password: ""
transform_path: "cota_nsclc/care/current"
upload_bucket: "care.aetion.com/upload/cota_nsclc/20240415"
use_smart_sampling: False
validation_path: "cota_nsclc/care/current"
validation_vars:
  GDR_END_DATE: "2023-07-02"
  GDR_START_DATE: "1955-07-02"
steps_spark_config:
  default:
    num_workers: 2
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
