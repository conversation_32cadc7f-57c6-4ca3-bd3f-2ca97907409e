defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum_market_clarity_bone_health"
client: "amgen"
revision: "20241023"
is_k8s: False
alert_user: "@evgeniy.varganov"
deployment_config:
  amgen: "amgen.aetion.com"
dynamic_patient_table: True

flat_tables_to_regenerate:
  - procedure_flat
  - medical_services_flat
git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2024-07-31"
  GDR_START_DATE: "2007-01-01"
  MASKED_BIRTH_DATE: "1930 and Earlier"
iam_arn: "arn:aws:iam::627533566824:instance-profile/amgen-databricks-etl-iam"
regenerate_all_flat_tables: False
source_files_password: ""
transform_path: "humedica/optum_market_clarity_bone_health/amgen/current"
upload_bucket: "amgen.aetion.com/upload/optum_market_clarity_bone_health/20241023"
use_smart_sampling: False
validation_path: "humedica/optum_market_clarity_bone_health/amgen/current"
validation_vars:
  GDR_END_DATE: "2024-07-31"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 60
      min_workers: 60
    driver_node_type_id: "r5a.8xlarge"
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 40000
  full_patient_job:
    autoscale:
      max_workers: 60
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.sql.parquet.columnarReaderBatchSize: 32
      spark.sql.shuffle.partitions: 60000
  full_shard_job:
    autoscale:
      max_workers: 80
      min_workers: 1
  default:
    autoscale:
      max_workers: 10
      min_workers: 10
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: True
      spark.driver.maxResultSize: 0
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
      spark.sql.shuffle.partitions: 2000
  unarchiver_job_only:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "c5.4xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 1200000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
