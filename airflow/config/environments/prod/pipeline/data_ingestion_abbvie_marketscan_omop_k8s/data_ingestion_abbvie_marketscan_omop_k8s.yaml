defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_omop"
client: "abbvie"
revision: "********"
is_k8s: True
alert_user: "@mikhail.skiba"
deployment_config:
  abbvie: "abbvie.aetion.com"
dynamic_patient_table: True

git_branch_override: "abbvie_marketscanomop_********"
hive_vars:
  GDR_END_DATE: "2024-12-23"
  GDR_START_DATE: "1911-10-12"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: True
service_account: "spark-operator-client-abbvie"
spark_memory_overhead_factor: ".2"
transform_path: "jmdc_hospital/abbvie/current"
upload_bucket: "abbvie.aetion.com/upload/marketscan_omop/********"
use_smart_sampling: False
validation_path: "jmdc_hospital/abbvie/current"
validation_vars:
  GDR_END_DATE: "2024-12-23"
  GDR_START_DATE: "1911-10-12"
