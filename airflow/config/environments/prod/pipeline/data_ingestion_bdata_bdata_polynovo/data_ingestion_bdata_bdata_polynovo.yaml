defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "bdata_polynovo"
client: "bdata"
revision: "20231219"
is_k8s: False
alert_user: "@mikhail.skiba"
deployment_config:
  nestcc: "nestcc.aetion.com"
dynamic_patient_table: True

git_branch_override: "bdata-bdata_polynovo-20231219"
hive_vars:
  GDR_END_DATE: "2023-12-19"
  GDR_START_DATE: "2016-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bdata-databricks-etl-iam"
rde_revision_check: "20231219"
regenerate_all_flat_tables: True
skip_single_shard: True
source_files_password: ""
transform_path: "bdata/polynovo/current"
upload_bucket: "bdata.aetion.com/upload/bdata_polynovo/20231219"
use_smart_sampling: False
validation_path: "bdata/polynovo/current"
validation_vars:
  GDR_END_DATE: "2023-12-19"
  GDR_START_DATE: "2016-01-01"
steps_spark_config:
  default:
    node_type_id: "rd-fleet.xlarge"
    num_workers: 0
    spark_conf:
      aetion.dataset.automaticGdrFiltering: True
      spark.databricks.cluster.profile: "singleNode"
      spark.default.parallelism: 10
      spark.master: "local[*, 4]"
      spark.sql.shuffle.partitions: 10
