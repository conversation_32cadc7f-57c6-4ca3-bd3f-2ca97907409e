defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_databricks_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_tiny_subset"
client: "adn"
revision: "20221004"
is_k8s: False
alert_user: "@Damian"
deployment_config:
  ci: "ci.aetion.com"
  qa: "cms.dev.aetion.com"
dynamic_flat_tables: False
dynamic_patient_table: True
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
regenerate_all_flat_tables: True
skip_single_shard: False
source_files_password: ""
transform_path: "synpuf_tiny_subset/current"
upload_bucket: "adn.aetion.com/upload/synpuf_tiny_subset/20221004"
validation_path: "synpuf_tiny_subset/current"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    node_type_id: "rd-fleet.xlarge"
    spark_conf:
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 1
  default:
    node_type_id: "rd-fleet.xlarge"
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
