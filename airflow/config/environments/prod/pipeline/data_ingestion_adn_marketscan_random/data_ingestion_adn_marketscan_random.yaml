defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_random"
client: "adn"
revision: "20220119"
is_k8s: False
alert_user: "@denis.kozlov"
hive_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2016-03-31"
  LAB_END_DATE: "2021-03-31"
  LAB_START_DATE: "2016-03-31"
validation_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2016-03-31"
deployment_config:
  demo: "demo.aetion.com"

git_branch_override: "adn_ms-random_20220119"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
source_files_password: ""
transform_path: "marketscan_random/current"
upload_bucket: "adn.aetion.com/upload/marketscan_random/20220119"
validation_path: "marketscan_random/current"
