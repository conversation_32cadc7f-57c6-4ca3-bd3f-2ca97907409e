defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "iqvia_laad"
client: "amgen"
revision: "********"
is_k8s: True
alert_user: "@bartlomiej.cielecki"
deployment_config:
  amgen: "amgen.aetion.com"
dynamic_patient_table: True

git_branch_override: "amgen_iqvia_laad_********"
hive_vars:
  GDR_END_DATE: "2025-02-10"
  GDR_START_DATE: "1900-01-01"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: False
service_account: "spark-operator-client-amgen"
source_files_password: ""
transform_path: "iqvia_laad/amgen/current"
upload_bucket: "amgen.aetion.com/upload/iqvia_laad/********"
validation_path: "iqvia_laad/amgen/current"
validation_vars:
  GDR_END_DATE: "2025-02-10"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  full_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 120
      min_executors: 1
    driver_conf:
      instance_type: "r5a.8xlarge"
      pyspark_memory_overhead: "53000m"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 40000
    worker_conf:
      instance_type: "i3.8xlarge"
  full_patient_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 120
      min_executors: 1
    driver_conf:
      instance_type: "r5a.8xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.shuffle.partitions: 30000
    worker_conf:
      instance_type: "i3.8xlarge"
  full_shard_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 100
      min_executors: 1
    driver_conf:
      instance_type: "r5ad.8xlarge"
      spark_memory_overhead: "26000m"
    spark_conf:
      spark.memory.offHeap.enabled: False
      spark.task.cpus: 2
    worker_conf:
      instance_type: "r5ad.8xlarge"
      spark_memory_overhead: "26000m"
  default:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 40
      min_executors: 40
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
      spark.sql.shuffle.partitions: 1000
    worker_conf:
      instance_type: "r5.4xlarge"
