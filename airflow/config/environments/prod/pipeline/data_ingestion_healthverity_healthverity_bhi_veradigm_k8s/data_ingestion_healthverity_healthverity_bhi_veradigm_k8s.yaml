defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: healthverity_bhi_veradigm
client: healthverity
revision: "********"
is_k8s: true
alert_user: "@denis.kozlov"
catalog_overrides:
  tableOverrides:
    bhi_enrollment:
      baseUrl: &bhi_base_url s3a://healthverity.aetion.com/upload/HV005585/20241130/
      path: enrollment
    bhi_medical_claims:
      baseUrl: *bhi_base_url
      path: medical_claims
    bhi_npi_taxonomy_code_crosswalk:
      baseUrl: *bhi_base_url
      path: npi_taxonomy_code_crosswalk
    bhi_pharmacy_claims:
      baseUrl: *bhi_base_url
      path: pharmacy_claims
    emr_clin_obsn:
      baseUrl: &veradigm_base_url s3a://healthverity.aetion.com/upload/moderna/HV006034/veradigm/********/
      path: emr_clin_obsn
    emr_diag:
      baseUrl: *veradigm_base_url
      path: emr_diag
    emr_enc:
      baseUrl: *veradigm_base_url
      path: emr_enc
    emr_lab_test:
      baseUrl: *veradigm_base_url
      path: emr_lab_test
    emr_medctn:
      baseUrl: *veradigm_base_url
      path: emr_medctn
    emr_proc:
      baseUrl: *veradigm_base_url
      path: emr_proc
    emr_prov_ord:
      baseUrl: *veradigm_base_url
      path: emr_prov_ord
    medicare_provider_taxonomy_crosswalk:
      baseUrl: s3a://healthverity.aetion.com/etl/
      path: medicare_provider_taxonomy_crosswalk
    patient_too_large_exclusion_list:
      baseUrl: s3a://healthverity.aetion.com/etl/healthverity_bhi_veradigm/********/
      path: patient_too_large_exclusion_list
deployment_config:
  moderna: moderna.aetion.com
git_branch_override: healthverity-healthverity_bhi_veradigm-********
hive_vars:
  GDR_END_DATE: "2025-06-30"
  GDR_START_DATE: "2015-10-01"
iam_arn: arn:aws:iam::************:instance-profile/moderna-databricks-etl-iam
partition_full_parquet_url: moderna.aetion.com/health_verity_aetion/upload/healthverity/
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: true
service_account: spark-operator-client-healthverity
source_files_password: ""
transform_path: healthverity/healthverity_bhi_veradigm/current
upload_bucket: healthverity.aetion.com/upload/healthverity_bhi_veradigm/********
validation_path: healthverity/healthverity_bhi_veradigm/current
validation_vars:
  GDR_END_DATE: "2025-06-30"
  GDR_START_DATE: "2015-10-01"
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 100
      min_executors: 100
    driver_conf:
      instance_type: r5a.8xlarge
      pyspark_memory_overhead: 53000m
    spark_conf:
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: false
      spark.hadoop.fs.s3a.connection.maximum: 300
      spark.hadoop.fs.s3a.threads.max: 512
      spark.shuffle.io.clientThreads: 512
      spark.shuffle.io.serverThreads: 512
      spark.shuffle.io.threads: 1024
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 40000
      spark.sql.adaptive.shuffle.targetPostShuffleInputSize: 256MB
      spark.sql.adaptive.coalescePartitions.enabled: true
      spark.memory.storageFraction: 0.05
      spark.memory.offHeap.enabled: false
    worker_conf:
      instance_type: i3.8xlarge
      memory_on_disk: 800G
  full_patient_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 100
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      spark_memory_overhead: 13100m
    spark_conf:
      spark.sql.shuffle.partitions: 50000
      spark.task.cpus: 3
    worker_conf:
      instance_type: i3.8xlarge
      memory_on_disk: 800G
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    driver_conf:
      instance_type: r5ad.8xlarge
      spark_memory_overhead: 26000m
    spark_conf:
      spark.memory.offHeap.enabled: false
      spark.task.cpus: 2
    worker_conf:
      instance_type: r5ad.8xlarge
      spark_memory_overhead: 26000m
      memory_on_disk: 128G
  default:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 20
      min_executors: 20
    spark_conf:
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: false
      spark.driver.maxResultSize: "0"
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: LEGACY
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: CORRECTED
      spark.sql.shuffle.partitions: 10000
      spark.hadoop.parquet.enable.summary-metadata: false
      spark.sql.parquet.mergeSchema: false
      spark.sql.parquet.filterPushdown: true
      spark.sql.hive.metastorePartitionPruning: true
      spark.rdd.compress: true
      spark.shuffle.compress: true
      spark.shuffle.consolidateFiles: true
      spark.network.io.connectionTimeout: "600s"
      spark.network.io.maxRetries: "12"
      spark.network.io.preferDirectBufs: True
      spark.network.io.retryWait: "5s"
      spark.network.timeout: "600s"
      spark.rpc.askTimeout: "300s"
      spark.rpc.lookupTimeout: "300s"
    worker_conf:
      instance_type: r5.4xlarge
      memory_on_disk: 128G
