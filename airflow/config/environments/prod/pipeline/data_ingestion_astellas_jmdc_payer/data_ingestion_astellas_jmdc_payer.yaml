defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "jmdc_payer"
client: "astellas"
revision: "20240105"
is_k8s: False
alert_user: "@rafal.kwiatkowski"
deployment_config:
  astellas: "astellas.aetion.com"
dynamic_patient_table: True

git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2023-09-30"
  GDR_START_DATE: "1900-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/astellas-databricks-etl-iam"
regenerate_all_flat_tables: True
transform_path: "jmdc_payer/astellas/current"
upload_bucket: "astellas.aetion.com/upload/jmdc_payer/20240105"
use_smart_sampling: False
validation_path: "jmdc_payer/astellas/current"
validation_vars:
  GDR_END_DATE: "2023-09-30"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 30
      min_workers: 1
    aws_attributes:
      ebs_volume_count: 8
      ebs_volume_size: 100
  full_patient_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 8000
  full_shard_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    driver_node_type_id: "r5a.4xlarge"
    node_type_id: "r-fleet.4xlarge"
