defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "tempus_nsclc"
client: "adn"
revision: "20210827"
is_k8s: False
alert_user: "@bartlomiej.cielecki"
deployment_config:
  demo: "demo.aetion.com"
  tempus: "tempus.aetion.com"
git_branch_override: "remotes/origin/adn_tempus_20210827"
hive_vars:
  GDR_END_DATE: "2021-11-30"
  GDR_START_DATE: "1940-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "tempus_nsclc/adn/20210827"
upload_bucket: "adn.aetion.com/upload/tempus_nsclc/20210827"
use_smart_sampling: False
validation_path: "tempus_nsclc/adn/20210827"
validation_vars:
  GDR_END_DATE: "2021-11-30"
  GDR_START_DATE: "1940-01-01"
