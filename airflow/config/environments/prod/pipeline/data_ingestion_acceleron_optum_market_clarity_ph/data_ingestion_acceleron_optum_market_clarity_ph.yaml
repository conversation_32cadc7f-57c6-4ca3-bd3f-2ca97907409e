defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum_market_clarity_ph"
client: "acceleron"
revision: "20230310"
is_k8s: False
alert_user: "@denis.kozlov"
deployment_config:
  acceleron: "acceleron.aetion.com"
git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2022-03-31"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/acceleron-databricks-etl-iam"
source_files_password: ""
transform_path: "humedica/optum_market_clarity_ph/acceleron/current"
upload_bucket: "acceleron.aetion.com/upload/optum_market_clarity_ph/20230310"
use_smart_sampling: False
validation_path: "humedica/optum_market_clarity_ph/acceleron/current"
validation_vars:
  GDR_END_DATE: "2022-03-31"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
      region: ""
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 60
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions: "-Xloggc:/dev/null -XX:+UseG1GC -XX:+UseStringDeduplication -XX:ParallelGCThreads=20 -XX:ConcGCThreads=5 -XX:InitiatingHeapOccupancyPercent=70 -XX:MaxGCPauseMillis=200"
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
      spark.network.timeout: 1200
      spark.sql.autoBroadcastJoinThreshold: 262144000
      spark.sql.broadcastTimeout: 1200
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 10000
      spark.task.maxDirectResultSize: 2097152000
  default:
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: 20971520
      spark.sql.broadcastTimeout: 600
    spark_version: "14.3.x-scala2.12"
  unarchiver_job_only:
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 20
    spark_env_vars:
      export TMPDIR: "/local_disk0/tmp"
