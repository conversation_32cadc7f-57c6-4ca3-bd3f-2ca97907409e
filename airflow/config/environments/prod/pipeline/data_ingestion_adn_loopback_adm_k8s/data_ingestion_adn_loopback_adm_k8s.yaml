defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /k8s_adm_step_group_config@steps_spark_config
  - _self_
dataset: "loopback_adm"
client: "adn"
revision: "**********"
is_k8s: True
alert_user: "@albert.pitarch"
dataset_group: "LABS"
deployment_config:
  dds: "dds.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
git_branch_override: "adn_loopback_adm_**********"
hive_vars:
  GDR_END_DATE: "2023-10-24"
  GDR_START_DATE: "2015-10-01"
native_data_for_adm_url: "s3://adn.aetion.com/etl/loopback/********"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: False
sampled_data_percentage: ".5"
service_account: "spark-operator-client-adn"
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: "loopback_adm/adn/1.4"
upload_bucket: "adn.aetion.com/upload/loopback_adm/**********"
use_smart_sampling: False
validation_path: "adm/1.4"
validation_vars:
  GDR_END_DATE: "2023-10-24"
  GDR_START_DATE: "2015-10-01"
steps:
  unarchiver:
    step_group: unarchiver_job_only
steps_spark_config:
  adm_full_job:
    autoscale:
      enabled: True
      initial_executors: 40
      max_executors: 40
      min_executors: 40
    driver_conf:
      instance_type: "r5ad.8xlarge"
      pyspark_memory_overhead: "94g"
    spark_conf:
      ignore.comment.aqe: "Adaptive Query Execution (AQE)"
      ignore.comment.jvm_memory: "Memory & JVM Optimizations"
      ignore.comment.network: "Network & Timeout Settings"
      ignore.comment.spill_shuffle_memory: "Spill & Shuffle Memory Management"
      spark.decommission.enabled: False
      spark.default.parallelism: "2500"
      spark.driver.extraJavaOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=500 -XX:InitiatingHeapOccupancyPercent=40"
      spark.driver.maxResultSize: 0
      spark.eventLog.enabled: True
      spark.executor.extraJavaOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=500 -XX:InitiatingHeapOccupancyPercent=40"
      spark.executor.heartbeatInterval: "120s"
      spark.logConf: True
      spark.memory.fraction: "0.7"
      spark.memory.offHeap.enabled: False
      spark.memory.storageFraction: "0.3"
      spark.network.timeout: "1000s"
      spark.rdd.compress: True
      spark.reducer.maxSizeInFlight: "96m"
      spark.shuffle.compress: True
      spark.shuffle.file.buffer: "256k"
      spark.shuffle.io.clientThreads: 256
      spark.shuffle.io.maxRetries: "20"
      spark.shuffle.io.numConnectionsPerPeer: "16"
      spark.shuffle.io.retryWait: "10s"
      spark.shuffle.io.serverThreads: 256
      spark.shuffle.spill.compress: True
      spark.sql.adaptive.coalescePartitions.enabled: True
      spark.sql.adaptive.enabled: True
      spark.sql.adaptive.localShuffleReader.enabled: True
      spark.sql.adaptive.shuffle.targetPostShuffleInputSize: "256MB"
      spark.sql.adaptive.skewJoin.enabled: True
      spark.sql.allowMultipleContexts: False
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: "2500"
    worker_conf:
      instance_type: "i3en.6xlarge"
      pyspark_memory_overhead: "70g"
  full_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "i3en.6xlarge"
  full_patient_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 140000
      spark.task.cpus: 2
    worker_conf:
      instance_type: "i3en.6xlarge"
  full_shard_job:
    autoscale:
      enabled: True
      initial_executors: 60
      max_executors: 60
      min_executors: 60
    driver_conf:
      instance_type: "r5ad.8xlarge"
      spark_memory_overhead: "94g"
    spark_conf:
      ignore.comment.aqe: "Adaptive Query Execution (AQE)"
      ignore.comment.jvm_memory: "Memory & JVM Optimizations"
      ignore.comment.network: "Network & Timeout Settings"
      ignore.comment.spill_shuffle_memory: "Spill & Shuffle Memory Management"
      spark.decommission.enabled: False
      spark.default.parallelism: "20000"
      spark.driver.extraJavaOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=500 -XX:InitiatingHeapOccupancyPercent=40"
      spark.driver.maxResultSize: 0
      spark.eventLog.enabled: True
      spark.executor.extraJavaOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=500 -XX:InitiatingHeapOccupancyPercent=40"
      spark.executor.heartbeatInterval: "60s"
      spark.logConf: True
      spark.memory.fraction: "0.7"
      spark.memory.offHeap.enabled: False
      spark.memory.storageFraction: "0.3"
      spark.network.timeout: "1000s"
      spark.rdd.compress: True
      spark.reducer.maxSizeInFlight: "96m"
      spark.shuffle.compress: True
      spark.shuffle.file.buffer: "256k"
      spark.shuffle.io.maxRetries: "20"
      spark.shuffle.io.numConnectionsPerPeer: "16"
      spark.shuffle.io.retryWait: "10s"
      spark.shuffle.spill.compress: True
      spark.sql.adaptive.coalescePartitions.enabled: True
      spark.sql.adaptive.enabled: True
      spark.sql.adaptive.localShuffleReader.enabled: True
      spark.sql.adaptive.shuffle.targetPostShuffleInputSize: "256MB"
      spark.sql.adaptive.skewJoin.enabled: True
      spark.sql.allowMultipleContexts: False
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: "20000"
      spark.task.cpus: 1
    worker_conf:
      instance_type: "i3en.6xlarge"
      spark_memory_overhead: "70g"
  unarchiver_job_only:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 16
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
    worker_conf:
      instance_type: "c5.4xlarge"
