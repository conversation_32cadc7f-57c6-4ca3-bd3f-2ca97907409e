defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /databricks_adm_step_group_config@steps_spark_config
  - _self_
dataset: "optum_cdm_adm"
client: "amgen"
revision: "20240729"
is_k8s: False
alert_user: "@katharine.fuzesi"
deployment_config:
  amgen: "amgen.aetion.com"

regenerate_all_flat_tables: True
hive_vars:
  GDR_END_DATE: "2024-03-31"
  GDR_START_DATE: "2015-10-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/amgen-databricks-etl-iam"
transform_path: "optum/cdm_adm/amgen/1.3"
upload_bucket: "amgen.aetion.com/upload/optum_cdm_adm/20240729"
validation_path: "optum/cdm_adm/1.3"
validation_vars:
  GDR_END_DATE: "2024-03-31"
  GDR_START_DATE: "2015-10-01"
dynamic_flat_tables: True
dynamic_patient_table: True
native_data_for_adm_url: "s3://amgen.aetion.com/etl/optum-cdm/20240729"
prophecy_pipeline_prefix_override: "optum_cdm"
steps_spark_config:
  full_job:
    autoscale:
      min_workers: 60
      max_workers: 60
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: False
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: False
      spark.task.cpus: 1
      spark.sql.shuffle.partitions: 20000
  full_patient_job:
    node_type_id: "i3en.6xlarge"
    autoscale:
      min_workers: 1
      max_workers: 60
  full_shard_job:
    autoscale:
      min_workers: 1
      max_workers: 60
  default:
    autoscale:
      min_workers: 10
      max_workers: 10
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.shuffle.partitions: 1000
  unarchiver_job_only:
    autoscale:
      max_workers: 16
      min_workers: 1
    driver_node_type_id: "r5a.xlarge"
    node_type_id: "c5.4xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
