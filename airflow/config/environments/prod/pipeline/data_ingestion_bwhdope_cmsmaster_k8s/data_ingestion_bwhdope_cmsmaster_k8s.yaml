defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: cmsmaster
client: bwhdope
revision: "**********"
is_k8s: true
alert_user: "@rafal.kwiatkowski"
deployment_config:
  bwhdope: bwhdope.aetion.com
dynamic_patient_table: true
git_branch_override: bwh_cmsmaster_**********
hive_vars:
  GDR_END_DATE: "2022-12-31"
  GDR_START_DATE: "2013-01-01"
pre_partitioned_data_url: bwhdope.aetion.com/etl/cmsmaster/********
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: true
service_account: spark-operator-client-bwhdope
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: cmsmaster/bwh/current
upload_bucket: bwhdope.aetion.com/upload/cmsmaster/**********
use_smart_sampling: false
validation_path: cmsmaster/bwh/current
validation_vars:
  GDR_END_DATE: "2022-12-31"
  GDR_START_DATE: "2013-01-01"
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 40
      max_executors: 40
      min_executors: 40
    driver_conf:
      instance_type: r5a.8xlarge
      pyspark_memory_overhead: 53000m
      spark_memory_overhead: 53000m
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      ignore.comment.aqe: Adaptive Query Execution (AQE)
      ignore.comment.jvm_memory: Memory & JVM Optimizations
      ignore.comment.network: Network & Timeout Settings
      ignore.comment.shuffle_optimizations: Shuffle Optimizations
      ignore.comment.spill_shuffle_memory: Spill & Shuffle Memory Management
      spark.driver.extraJavaOptions:
        -XX:+UseG1GC -XX:MaxGCPauseMillis=500 -XX:+PrintGCDetails
        -XX:InitiatingHeapOccupancyPercent=40
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions:
        -XX:+UseG1GC -XX:MaxGCPauseMillis=500 -XX:+PrintGCDetails
        -XX:InitiatingHeapOccupancyPercent=40
      spark.executor.heartbeatInterval: 120s
      spark.memory.fraction: "0.7"
      spark.memory.storageFraction: "0.3"
      spark.network.timeout: 1000s
      spark.rdd.compress: true
      spark.shuffle.compress: true
      spark.shuffle.consolidateFiles: true
      spark.shuffle.io.clientThreads: 256
      spark.shuffle.io.maxRetries: "20"
      spark.shuffle.io.retryWait: 15s
      spark.shuffle.io.serverThreads: 256
      spark.shuffle.sort.bypassMergeThreshold: "1000"
      spark.shuffle.spill.compress: true
      spark.sql.adaptive.coalescePartitions.enabled: true
      spark.sql.adaptive.enabled: true
      spark.sql.adaptive.localShuffleReader.enabled: true
      spark.sql.adaptive.shuffle.targetPostShuffleInputSize: 256MB
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: "5000"
    worker_conf:
      instance_type: i3en.6xlarge
      spark_memory_overhead: 70g
      memory_on_disk: ""
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: r5ad.8xlarge
      spark_memory_overhead: 26000m
    spark_conf:
      spark.memory.offHeap.enabled: false
      spark.task.cpus: 2
    worker_conf:
      instance_type: r5ad.8xlarge
      spark_memory_overhead: 26000m
      memory_on_disk: ""
  default:
    autoscale:
      enabled: true
      initial_executors: 10
      max_executors: 30
      min_executors: 10
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.driver.maxResultSize: 0
      spark.sql.shuffle.partitions: 1000
      spark.task.cpus: 1
    worker_conf:
      instance_type: r5ad.xlarge
      memory_on_disk: ""
  unarchiver_job_only:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 16
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
    worker_conf:
      instance_type: m5d.4xlarge
