defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: mdv
client: amgen
revision: "********"
is_k8s: true
alert_user: "@bartlomiej.cielecki"
deployment_config:
  amgen: amgen.aetion.com
dynamic_patient_table: true
git_branch_override: amgen_mdv_********
hive_vars:
  GDR_END_DATE: "2025-04-30"
  GDR_START_DATE: "1911-07-22"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: false
service_account: spark-operator-client-amgen
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: mdv/amgen/current
upload_bucket: s3://amgen.aetion.com/upload/mdv/********
use_smart_sampling: false
validation_path: mdv/amgen/current
validation_vars:
  GDR_END_DATE: "2025-04-30"
  GDR_START_DATE: "1911-07-22"
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 60
      max_executors: 60
      min_executors: 60
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
  full_patient_job:
    autoscale:
      enabled: true
      initial_executors: 80
      max_executors: 80
      min_executors: 80
    spark_conf:
      aetion.dataset.automaticNdcFormatting: false
      spark.decommission.enabled: true
      spark.driver.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.driver.maxResultSize: "0g"
      spark.executor.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.hadoop.fs.s3a.connection.maximum: 300
      spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
      spark.hadoop.fs.s3a.threads.max: 200
      spark.memory.offHeap.enabled: true
      spark.memory.offHeap.size: "40g"
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.kryo.registrationRequired: false
      spark.kryo.unsafe: true
      spark.kryo.referenceTracking: false
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
      spark.shuffle.compress: true
      spark.rdd.compress: true
      spark.shuffle.spill.compress: true
      spark.io.compression.codec: "zstd"
      spark.io.compression.zstd.level: "6"
      spark.sql.adaptive.coalescePartitions.enabled: true
      spark.sql.adaptive.coalescePartitions.initialPartitionNum: "1200"
      spark.sql.adaptive.coalescePartitions.minPartitionSize: "512MB"
      spark.sql.adaptive.coalescePartitions.parallelismFirst: false
      spark.sql.adaptive.enabled: true
      spark.sql.adaptive.localShuffleReader.enabled: true
      spark.sql.adaptive.optimize.skewJoin.enabled: true
      spark.sql.adaptive.skewJoin.enabled: true
      spark.sql.adaptive.skewJoin.skewedPartitionFactor: "3"
      spark.sql.adaptive.skewJoin.skewedPartitionThresholdInBytes: "2GB"
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      spark.sql.parquet.binaryAsString: true
      spark.sql.shuffle.partitions: 40000
      spark.storage.decommission.enabled: false
      spark.storage.decommission.rddBlocks.enabled: false
      spark.storage.decommission.shuffleBlocks.enabled: false
      spark.storage.decommission.shuffleBlocks.maxThreads: 32
      spark.network.io.connectionTimeout: "600s"
      spark.network.io.maxRetries: "12"
      spark.network.io.preferDirectBufs: true
      spark.network.io.retryWait: "5s"
      spark.network.timeout: "600s"
      spark.reducer.maxSizeInFlight: "128m"
      spark.rpc.askTimeout: "300s"
      spark.rpc.lookupTimeout: "300s"
      spark.rpc.message.maxSize: "1024"
      spark.task.cpus: 1
      spark.shuffle.io.maxRetries: "8"
      spark.shuffle.io.numConnectionsPerPeer: "2"
      spark.shuffle.io.clientThreads: "16"
      spark.shuffle.io.serverThreads: "16"
      spark.shuffle.io.threads: "64"
      spark.shuffle.io.retryWait: "30s"
      spark.shuffle.file.buffer: "256k"
      spark.shuffle.unsafe.file.output.buffer: "1m"
      spark.shuffle.io.connectionTimeout: "300s"
    worker_conf:
      # Change manually in _V2 variable
      cores: 10
      instance_type: i3en.6xlarge
      memory_on_disk: "500G"
      spark_memory_overhead: "20g"
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
  default:
    autoscale:
      enabled: true
      initial_executors: 10
      max_executors: 10
      min_executors: 10
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.constraintPropagation.enabled: false
      spark.sql.shuffle.partitions: 200
      spark.task.cpus: 2
    worker_conf:
      instance_type: c5.4xlarge
      memory_on_disk: 128G
  enums_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
      spark.sql.parquet.int96RebaseModeInRead: "CORRECTED"
      spark.sql.parquet.int96RebaseModeInWrite: "CORRECTED"
      spark.sql.parquet.datetimeRebaseModeInRead: "CORRECTED"
      spark.sql.parquet.datetimeRebaseModeInWrite: "CORRECTED"
    worker_conf:
      instance_type: r5a.4xlarge
      memory_on_disk: ""
steps:
  generate_full_enums_and_dictionaries:
    step_group: "enums_job"
  create_single_partition:
    step_group: "enums_job"
  create_full_partition:
    step_group: "enums_job"
