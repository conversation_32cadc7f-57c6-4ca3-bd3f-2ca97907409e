defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_multidate"
client: "adn"
revision: "20221004"
is_k8s: False
alert_user: "@aleksei.beltyukov"
deployment_config:
  ci: "ci.aetion.com"
  qa: "cms.dev.aetion.com"
hive_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
source_files_password: ""
transform_path: "synpuf_multidate/current"
upload_bucket: "s3://adn.aetion.com/upload/synpuf_multidate/20221004"
validation_path: "synpuf_multidate/current"
validation_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      spark.default.parallelism: 6000
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 6000
  default:
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
