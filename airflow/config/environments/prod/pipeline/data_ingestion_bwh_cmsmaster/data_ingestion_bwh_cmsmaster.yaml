defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "cmsmaster"
client: "bwh"
revision: "**********"
is_k8s: False
alert_user: "@mikhail.skiba"
deployment_config:
  bwhdope: "bwh-dope.partners.org/aetion"
dynamic_patient_table: True
git_branch_override: "remotes/origin/bwh_cmsmaster_**********"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
pre_partitioned_data_url: "bwh.aetion.com/etl/cmsmaster/**********"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "cmsmaster/bwh/current"
upload_bucket: "bwh.aetion.com/upload/cmsmaster/**********"
use_smart_sampling: False
validation_path: "cmsmaster/bwh/current"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-01-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 60
      min_workers: 10
    driver_node_type_id: "r5.4xlarge"
    node_type_id: "i4i.8xlarge"
    spark_conf:
      spark.default.parallelism: 2000
      spark.driver.maxResultSize: "20g"
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.memory.offHeap.enabled: False
      spark.sql.autoBroadcastJoinThreshold: 20971520
      spark.sql.broadcastTimeout: 800
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 2
      spark.task.maxDirectResultSize: "0"
  full_shard_job:
    spark_conf:
      spark.default.parallelism: 10000
      spark.executor.extraJavaOptions: "-Xloggc:/dev/null -XX:+UseG1GC -XX:+UseStringDeduplication -XX:ParallelGCThreads=18 -XX:ConcGCThreads=10 -XX:InitiatingHeapOccupancyPercent=70 -XX:MaxGCPauseMillis=200"
      spark.shuffle.io.maxRetries: 5
      spark.shuffle.reduceLocality.enabled: True
      spark.sql.adaptive.advisoryPartitionSizeInBytes: 134217728
      spark.sql.adaptive.coalescePartitions.parallelismFirst: False
      spark.sql.shuffle.partitions: 10000
  default:
    autoscale:
      max_workers: 20
      min_workers: 1
    node_type_id: "i4i.8xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.driver.maxResultSize: "0"
      spark.network.timeout: 1200
      spark.sql.broadcastTimeout: 1200
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
      spark.sql.shuffle.partitions: 2000
  unarchiver_job_only:
    autoscale:
      max_workers: 10
      min_workers: 1
    driver_node_type_id: "m-fleet.xlarge"
    node_type_id: "c5.4xlarge"
    spark_conf:
      spark.task.cpus: 1
