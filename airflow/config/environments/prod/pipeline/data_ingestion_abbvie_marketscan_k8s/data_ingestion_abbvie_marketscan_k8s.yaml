defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: marketscan
client: abbvie
revision: "********"
is_k8s: true
alert_user: "@oksana.antropova"
alert_scientist: "@daniel.thomas"
deployment_config:
  abbvie: abbvie.aetion.com
dynamic_patient_table: true
git_branch_override: abbvie_marketscan_********
hive_vars:
  EV_END_DATE: "2025-04-30"
  EV_START_DATE: "2024-09-30"
  GDR_END_DATE: "2025-04-30"
  GDR_START_DATE: "1999-12-31"
  LAB_END_DATE: "2025-04-30"
  LAB_START_DATE: "1999-12-31"
preprocess: true
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: true
service_account: spark-operator-client-abbvie
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: marketscan/abbvie/current
upload_bucket: abbvie.aetion.com/upload/marketscan/********
use_smart_sampling: false
validation_path: marketscan/abbvie/current
validation_vars:
  GDR_END_DATE: "2025-04-30"
  GDR_START_DATE: "1999-12-31"
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 60
      max_executors: 80
      min_executors: 60
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: snappy
      spark.memory.offHeap.enabled: false
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 10000
      spark.task.cpus: 1
      spark.decommission.enabled: false
      spark.storage.decommission.enabled: false
      spark.storage.decommission.rddBlocks.enabled: false
      spark.storage.decommission.shuffleBlocks.enabled: false
    worker_conf:
      cores: 10
      instance_type: i3en.6xlarge
      memory_on_disk: "200G"
      memory: "108658m"
  full_patient_job:
    autoscale:
      enabled: true
      initial_executors: 20
      max_executors: 80
      min_executors: 20
    worker_conf:
      cores: 15
      instance_type: "r5ad.8xlarge"
      memory: "190g"
      spark_memory_overhead: "40g"
      memory_on_disk: "500G"
    spark_conf:
      spark.memory.fraction: "0.5"
      spark.memory.storageFraction: "0.1"
      spark.memory.offHeap.enabled: true
      spark.memory.offHeap.size: "40G"
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.kryo.registrationRequired: false
      spark.kryo.unsafe: false
      spark.kryo.referenceTracking: false
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "512m"
      spark.shuffle.compress: true
      spark.rdd.compress: true
      spark.shuffle.spill.compress: true
      spark.io.compression.codec: "zstd"
      spark.io.compression.zstd.level: 6
      spark.task.cpus: 1
      spark.decommission.enabled: false
      spark.storage.decommission.enabled: false
      spark.storage.decommission.rddBlocks.enabled: false
      spark.storage.decommission.shuffleBlocks.enabled: false
      spark.sql.parquet.columnarReaderBatchSize: 256
      spark.shuffle.io.maxRetries: 8
      spark.shuffle.io.numConnectionsPerPeer: 2
      spark.shuffle.io.clientThreads: 16
      spark.shuffle.io.serverThreads: 16
      spark.shuffle.io.threads: 64
      spark.shuffle.io.retryWait: "30s"
      spark.shuffle.file.buffer: "256k"
      spark.shuffle.unsafe.file.output.buffer: "1m"
      spark.network.timeout: "600s"
      spark.shuffle.io.connectionTimeout: "300s"
      spark.dynamicAllocation.executorIdleTimeout: "1800s"
      spark.dynamicAllocation.cachedExecutorIdleTimeout: "3600s"
      spark.dynamicAllocation.shuffleTracking.timeout: "1800s"
      spark.dynamicAllocation.sustainedSchedulerBacklogTimeout: "150s"
      spark.dynamicAllocation.schedulerBacklogTimeout: "10s"
      spark.dynamicAllocation.executorAllocationRatio: "1.0"
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
