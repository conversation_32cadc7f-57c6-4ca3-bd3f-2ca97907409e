defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "flatiron_nsclc"
client: "amgen"
revision: "20240412"
is_k8s: False
alert_user: "@oksana.antropova"
deployment_config:
  amgen: "amgen.aetion.com"
dynamic_patient_table: True

git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2024-02-29"
  GDR_START_DATE: "1900-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/amgen-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "flatiron/amgen/nsclc/current"
upload_bucket: "s3://amgen.aetion.com/upload/flatiron_nsclc/20240412"
validation_path: "flatiron/amgen/nsclc/current"
validation_vars:
  GDR_END_DATE: "2024-02-29"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      spark.default.parallelism: 6000
      spark.memory.offHeap.enabled: False
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 6000
      spark.task.cpus: 2
  default:
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
