defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_databricks_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_tiny_subset"
client: "adip2"
revision: "20221004"
is_k8s: False
alert_user: "@dev-alert-airflow"
copy_spec: False
deployment_config:
  aep-dev: "adip2.app.dev.aetion.com"
domain: "app.dev.aetion.com"
skip_single_shard: True
dynamic_flat_tables: False
dynamic_patient_table: True
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
iam_arn: "arn:aws:iam::179392497636:instance-profile/adip2-dev1-b-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "synpuf_tiny_subset/current"
upload_bucket: "s3://adip2.app.dev.aetion.com/upload/synpuf_tiny_subset/20221004"
validation_path: "synpuf_tiny_subset/current"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "auto"
    node_type_id: "i3.2xlarge"
    num_workers: 2
    spark_conf:
      spark.default.parallelism: 4
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
      spark.sql.shuffle.partitions: 4
  default:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "auto"
    node_type_id: "i3.2xlarge"
    num_workers: 2
    spark_conf:
      spark.default.parallelism: 4
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
      spark.sql.shuffle.partitions: 4
