defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_k8s_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_tiny_subset"
client: "adip2"
revision: "********"
is_k8s: True
alert_user: "@dev-alert-airflow"
copy_spec: False
deployment_config:
  aep:
    dev: "adip2.app.dev.aetion.com"
domain: "app.dev.aetion.com"
skip_single_shard: True
dynamic_flat_tables: False
dynamic_patient_table: True
fail_stopgap: False
full_shard_stopgap: False
git_branch_override: "adip2-synpuf_tiny_subset-********"
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
rde_revision_check: "********"
regenerate_all_flat_tables: True
service_account: "spark-operator-client-common"
source_files_password: ""
transform_path: "synpuf_tiny_subset/current"
upload_bucket: "s3://adip2.app.dev.aetion.com/upload/synpuf_tiny_subset/********"
validation_path: "synpuf_tiny_subset/current"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
