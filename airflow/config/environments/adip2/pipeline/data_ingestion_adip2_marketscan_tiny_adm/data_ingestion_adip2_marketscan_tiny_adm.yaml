defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_tiny_adm"
client: "adip2"
revision: "20240716"
is_k8s: False
alert_user: "@dev-alert-airflow"
deployment_config:
  aep-dev: "adip2.app.dev.aetion.com"
domain: "app.dev.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
  MIN_DATE: "2015-10-01"
iam_arn: "arn:aws:iam::179392497636:instance-profile/adip2-dev1-b-databricks-etl-iam"
native_data_for_adm_url: "s3://adip2.app.dev.aetion.com/etl/marketscan_tiny/20240208"
prophecy_pipeline_prefix_override: "marketscan"
dataset_group: "LABS"
sampled_data_percentage: "50"
skip_single_shard: True
source_files_password: ""
transform_path: "marketscan_tiny_adm/demo/1.4"
upload_bucket: "s3://adip2.app.dev.aetion.com/upload/marketscan_tiny_adm/20240716"
use_smart_sampling: False
validation_path: "marketscan_tiny_adm/1.4"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
  MIN_DATE: "2015-10-01"
