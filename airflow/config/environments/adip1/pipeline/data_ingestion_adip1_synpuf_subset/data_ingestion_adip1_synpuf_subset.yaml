defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_subset"
client: "adip1"
revision: "20211018"
is_k8s: False
alert_user: "@dev-alert-airflow"
copy_spec: False
deployment_config:
  aep-dev: "adip1.app.dev.aetion.com"
domain: "app.dev.aetion.com"
dynamic_flat_tables: False
dynamic_patient_table: True
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
iam_arn: "arn:aws:iam::179392497636:instance-profile/adip1-dev1-b-databricks-etl-iam"
regenerate_all_flat_tables: False
skip_single_shard: False
source_files_password: ""
transform_path: "synpuf_subset/20211018"
upload_bucket: "s3://adip1.app.dev.aetion.com/upload/synpuf_subset/20211018"
validation_path: "synpuf_subset/20211018"
validation_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "auto"
    spark_conf:
      spark.default.parallelism: 6000
      spark.executor.cores: 8
      spark.sql.shuffle.partitions: 6000
  default:
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
    spark_env_vars:
      JNAME: "zulu17-ca-amd64"
    spark_version: "14.3.x-scala2.12"
