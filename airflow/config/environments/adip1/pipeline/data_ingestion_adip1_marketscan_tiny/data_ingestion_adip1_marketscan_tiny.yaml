defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_tiny"
client: "adip1"
revision: "20240208"
is_k8s: False
alert_user: "@dev-alert-airflow"
copy_spec: False
deployment_config:
  aep-dev: "adip1.app.dev.aetion.com"
domain: "app.dev.aetion.com"
skip_single_shard: True
dynamic_flat_tables: False
dynamic_patient_table: True
fail_stopgap: False
full_shard_stopgap: False
hive_vars:
  GDR_END_DATE: "2018-12-30"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-12-31"
  LAB_START_DATE: "2013-12-31"
source_files_password: ""
transform_path: "marketscan_tiny/adn/current"
upload_bucket: "s3://adip1.app.dev.aetion.com/upload/marketscan_tiny/20240208"
use_smart_sampling: False
validation_path: "marketscan_tiny/adn/current"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
