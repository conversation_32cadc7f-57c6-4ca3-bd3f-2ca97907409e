# FileValidator Airflow Operator - Developer Documentation

## Overview & Purpose

The **FileValidator** is a critical Airflow operator designed to validate raw data files in S3 against predefined expectations before downstream processing begins. It acts as a quality gate in data ingestion pipelines, ensuring data completeness, proper file organization, and adherence to defined schemas.


## Core Functionality

The FileValidator performs comprehensive validation through the following workflow:

1. **File Discovery**: Recursively scans S3 paths to inventory all available files
2. **Catalog Loading**: Loads YAML-based catalog definitions with table specifications
3. **Pattern Matching**: Maps files to tables using glob patterns with advanced features
4. **Validation Logic**: Applies business rules for completeness and consistency
5. **Inventory Generation**: Creates detailed CSV summaries for auditing
6. **Error Reporting**: Provides actionable error messages for validation failures

### Key Validation Features

- **Pattern Matching**: Advanced glob patterns including `**` (globstar) and `{}` (brace expansion)
- **Completeness Checking**: Ensures all expected tables have matching files
- **Ambiguity Detection**: Identifies files that match multiple table patterns
- **Ignore Rules**: Supports excluding specific files or patterns from validation
- **Flexible Options**: Per-table configuration like `allowMissingOrEmpty`
- **Group Filtering**: Table group-based inclusion/exclusion logic

## Key Components

### Filesystem Integration

#### `fs` Property
```python
@property
def fs(self) -> AetionS3FileSystem
```
- **Purpose**: Lazy-initialized S3 filesystem interface
- **Implementation**: Creates `AetionS3FileSystem` instance on first access
- **Configuration**: Uses `aws_conn_id` for AWS connection management
- **Caching**: Reuses the same instance across multiple calls

### Catalog Management

#### `yaml_catalog` Property
```python
@property
def yaml_catalog(self) -> dict
```
- **Purpose**: Loads and parses YAML catalog for client/dataset combination
- **Source**: Reads from `rdc_path` S3 location
- **Processing**: Uses `catalog_loader.get_catalog()` for parsing
- **Error Handling**: Raises `AirflowException` for missing or invalid catalogs

#### `yaml_catalog_tables` Property
```python
@property
def yaml_catalog_tables(self) -> list[dict]
```
- **Purpose**: Extracts table definitions from the loaded catalog
- **Structure**: List of dictionaries with `name`, `glob`, `group`, and `options`
- **Filtering**: Returns all tables before group/skip filtering is applied

### File Inventory Management

#### `inventory` Property
```python
@property
def inventory(self) -> list[tuple[str, int, datetime]]
```
- **Purpose**: Complete inventory of all files in the raw data path
- **Implementation**: Uses `fs.walk_all()` for recursive S3 scanning
- **Return Format**: Tuples of `(file_path, size, last_modified)`. The file path is relative to the `raw_data_path`. For example: if `raw_data_path` is `bucket/client/dataset/raw/` and the file is `s3://bucket/client/dataset/raw/dir/file.csv`, the returned file path will be `dir/file.csv`.
- **Performance**: Cached after first execution to avoid repeated S3 calls

#### `inventory_files` Property
```python
@property
def inventory_files(self) -> list[str]
```
- **Purpose**: Extracts file paths from the complete inventory
- **Implementation**: Maps over inventory tuples using lambda function
- **Usage**: Primary input for validation logic and pattern matching

### Validation Logic

#### `expected_files` Property
```python
@property
def expected_files(self) -> list[str]
```
- **Purpose**: Determines which table glob patterns should be validated
- **Filtering Logic**:
  - Excludes tables in `skip_loading_table_groups`
  - Excludes tables in `skip_loading_tables`
  - Includes only tables in specified `table_groups` (if defined)
- **Return Format**: List of glob patterns that must have matching files

#### `file_options` Property
```python
@property
def file_options(self) -> dict[str, dict]
```
- **Purpose**: Maps glob patterns to their validation options
- **Inheritance**: Table options inherit from `default_options` when not specified
- **Key Options**:
  - `allowMissingOrEmpty`: Skip validation if no files match pattern
  - `header`: Indicates if files have header rows
  - `delimiter`: CSV delimiter character

#### `validate()` Method
```python
def validate(self) -> None
```
- **Purpose**: Core validation logic that enforces all business rules
- **Validation Steps**:
  1. Filter out ignored files using `ignored_globs`
  2. Map remaining files to glob patterns
  3. Check for files without associated tables (unexpected files)
  4. Check for missing files (unless `allowMissingOrEmpty=true`)
  5. Check for ambiguous mappings (files matching multiple patterns)
- **Error Handling**: Raises `AirflowException` with detailed error messages

### Output Generation

#### `write_inventory_summary()` Method
```python
def write_inventory_summary(self) -> None
```
- **Purpose**: Generates comprehensive CSV inventory for auditing
- **Output Location**: Written to `raw_data_inventory_summary_path`
- **CSV Columns**:
  - `key`: File path
  - `size`: File size in bytes
  - `last_modified`: Last modification timestamp
  - `table`: Table name(s) (semicolon-separated if multiple)
  - `glob`: Matching glob pattern(s) (semicolon-separated)
  - `ignored`: Boolean indicating if file is ignored

## Configuration Options

### Constructor Parameters

#### Required Parameters
```python
def __init__(self,
             raw_data_path: str,                    # schemaless S3 path containing raw data files
             raw_data_inventory_summary_path: str,  # schemaless S3 path for inventory CSV output
             rdc_path: str,                         # S3 path to YAML catalog file
             client: str,                           # Client identifier for catalog lookup
             dataset: str,                          # Dataset identifier for catalog lookup
             **kwargs)
```

#### Optional Parameters
```python
aws_conn_id: str = "aws_default"  # Airflow connection ID for AWS/S3 access
```

#### Legacy Parameters (Deprecated)
```python
repo: str = None                  # Git repository URL (legacy)
git_meta_repo: str = None        # Git metadata repository URL (legacy)
private_key: str = None          # Git private key (legacy)
git_default_branch: str = None   # Default git branch (legacy)
branch: str = None               # Specific git branch (legacy)
```

### Template Fields

The following fields support Airflow Jinja2 templating:
- `raw_data_path`
- `raw_data_inventory_summary_path`
- `rdc_path`
- `client`
- `dataset`


## Catalog Structure

The FileValidator relies on YAML-based catalogs that define table specifications and validation rules. The catalog structure follows a hierarchical format supporting multiple datasets per file.

### Root Structure
```yaml
datasets:
  - name: "dataset0-client0"
    dataset_description:
      tag: "client0"
      short_description: "Dataset Description"
      long_description: "Detailed description"
      coding_systems: "common"
      family: "dataset_family"

    # Table group configuration
    table_groups: ["default", "optional", "supplementary"]
    skip_loading_table_groups: ["temp", "archive"]
    skip_loading_tables: ["DEPRECATED_TABLE", "TEST_TABLE"]

    # Default file format and processing options
    default_format: csv
    partitioner: table
    default_partitioner_key: patid
    default_partitioner_num: 1

    # Default options inherited by all tables
    default_options:
      header: true
      delimiter: ","
      allowMissingOrEmpty: false

    # Ignored file patterns
    ignored_files:
      - "*.tmp"
      - "backup/*"
      - "_*"

    # Table definitions
    tables:
      - name: PATIENTS
        glob: "patients*.csv"
        group: "default"
        options:
          header: true
          delimiter: ","

      - name: CLAIMS
        glob: "claims_*.csv"
        group: "default"
        options:
          allowMissingOrEmpty: true
```

### Table Definition Structure

Each table definition supports the following properties:

#### Required Fields
- **`name`**: Unique table identifier (used in error messages and inventory)
- **`glob`**: Glob pattern for matching files to this table
- **`group`**: Table group for organizational and filtering purposes

#### Optional Fields
- **`options`**: Table-specific validation and processing options
- **`description`**: Human-readable table description

### Glob Pattern Support

The FileValidator supports advanced glob patterns using the `wcmatch` library:

#### Basic Patterns
- `*`: Matches any characters except path separators
- `?`: Matches any single character
- `[abc]`: Matches any character in the set
- `[a-z]`: Matches any character in the range

#### Advanced Patterns
- `**`: Globstar - matches any number of directories
- `{pattern1,pattern2}`: Brace expansion - matches any of the patterns
- `!(pattern)`: Negation - matches anything except the pattern

#### Pattern Examples
```yaml
tables:
  # Simple wildcard
  - {name: PATIENTS, glob: "patients*.csv"}

  # Date-based patterns
  - {name: DAILY_DATA, glob: "data_????-??-??.csv"}

  # Brace expansion
  - {name: CLAIMS, glob: "claims_{inpatient,outpatient,pharmacy}.csv"}

  # Globstar for nested directories
  - {name: NESTED_FILES, glob: "**/data/*.csv"}

  # Complex patterns
  - {name: BENEFICIARY, glob: "DE1_0_????_Beneficiary_Summary_File_Sample_*.csv"}
```

### Option Inheritance

Table options follow an inheritance hierarchy:

1. **Global Defaults**: System-wide defaults (hardcoded)
2. **Catalog `default_options`**: Dataset-level defaults
3. **Table `options`**: Table-specific overrides

```yaml
# Catalog level defaults
default_options:
  header: true
  delimiter: ","
  allowMissingOrEmpty: false

tables:
  # Inherits all default_options
  - {name: TABLE1, glob: "table1*.csv"}

  # Overrides specific options
  - name: TABLE2
    glob: "table2*.csv"
    options:
      delimiter: "|"  # Overrides default
      # header: true (inherited)
      # allowMissingOrEmpty: false (inherited)
```

### Special Options

#### `allowMissingOrEmpty`
- **Type**: Boolean
- **Default**: `false`
- **Purpose**: Skip validation if no files match the glob pattern
- **Use Case**: Optional tables that may not be present in all data deliveries

#### `extra` Field Processing
The catalog loader supports an `extra` field for complex option parsing:

```yaml
default_options:
  header: true
  delimiter: ","
  extra: "allowMissingOrEmpty=true;customOption=false"
```

The `extra` field is parsed into individual options with automatic type conversion.

### Filtering Configuration

#### Table Groups
```yaml
table_groups: ["default", "supplementary"]
```
- **Purpose**: Include only tables from specified groups
- **Behavior**: If empty or omitted, all groups are included
- **Use Case**: Environment-specific table subsets

#### Skip Loading Groups
```yaml
skip_loading_table_groups: ["archive", "temp"]
```
- **Purpose**: Exclude entire table groups from validation
- **Priority**: Takes precedence over `table_groups`
- **Use Case**: Temporarily disable table groups

#### Skip Loading Tables
```yaml
skip_loading_tables: ["DEPRECATED_TABLE", "TEST_TABLE"]
```
- **Purpose**: Exclude specific tables by name
- **Priority**: Takes precedence over group-based inclusion
- **Use Case**: Temporarily disable individual tables

### Ignored Files
```yaml
ignored_files:
  - "*.tmp"
  - "*.log"
  - "_*"
  - "backup/**"
```
- **Purpose**: Define patterns for files that should be ignored during validation
- **Pattern Support**: Full glob pattern syntax including globstar
- **Use Case**: Exclude temporary files, logs, and system-generated content

## Validation Rules

The FileValidator enforces several critical validation rules to ensure data quality and consistency.

### File-to-Table Mapping

#### Pattern Matching Process
1. **File Discovery**: Scan S3 path recursively for all files
2. **Ignore Filtering**: Remove files matching `ignored_files` patterns
3. **Pattern Matching**: Map remaining files to table glob patterns
4. **Validation**: Apply business rules to the mappings

#### Mapping Rules
- Each file must match exactly one table pattern (no ambiguity)
- Each expected table pattern must have at least one matching file (unless `allowMissingOrEmpty=true`)
- Files not matching any pattern and not ignored are considered errors

### Validation Scenarios

#### 1. Missing Files
```
Error: "The following tables had 0 files matching in s3://bucket/path/: ['patients*.csv'].
Please check the raw data catalog"
```
- **Trigger**: Expected table pattern has no matching files
- **Exception**: `allowMissingOrEmpty=true` in table options
- **Resolution**: Add missing files or update catalog configuration

#### 2. Unexpected Files
```
Error: "Found files in s3://bucket/path/ that do not have an associated table: ['unknown_file.csv'].
Please add them to be ignored in the raw data catalog"
```
- **Trigger**: Files exist that don't match any table pattern or ignore rule
- **Resolution**: Add table definition or add to `ignored_files`

#### 3. Ambiguous Mappings
```
Error: "The following files were found to match multiple tables in s3://bucket/path/:
{'data.csv': ['table1*.csv', 'table2*.csv']}. Please correct the ambiguity."
```
- **Trigger**: File matches multiple table patterns
- **Resolution**: Make glob patterns more specific or restructure file organization

## Error Handling

The FileValidator provides comprehensive error handling with actionable error messages.

### Common Error Scenarios

#### 1. Catalog Loading Errors
```python
# Missing catalog file
FileNotFoundError: "No such file or directory: 's3://bucket/catalog.yml'"

# Invalid YAML syntax
yaml.YAMLError: "Invalid YAML syntax in catalog file"

# Missing dataset configuration
AirflowFailException: "could not find dataset dataset0-client0 in RDC"
```

#### 2. S3 Access Errors
```python
# Permission denied
botocore.exceptions.NoCredentialsError: "Unable to locate credentials"

# Bucket not found
botocore.exceptions.ClientError: "The specified bucket does not exist"

# Network connectivity issues
botocore.exceptions.EndpointConnectionError: "Could not connect to the endpoint URL"
```

#### 3. Validation Errors
```python
# Missing expected files
AirflowException: "The following tables had 0 files matching in s3://bucket/path/: ['patients*.csv']"

# Unexpected files
AirflowException: "Found files in s3://bucket/path/ that do not have an associated table: ['unknown.csv']"

# Ambiguous file mappings
AirflowException: "The following files were found to match multiple tables: {'data.csv': ['pattern1', 'pattern2']}"
```

### Error Resolution Strategies

#### Missing Files
1. **Check file delivery**: Verify files were uploaded to correct S3 path
2. **Review glob patterns**: Ensure patterns match actual file names
3. **Use allowMissingOrEmpty**: Add option if files are legitimately optional
4. **Update catalog**: Modify table definitions if file naming changed

#### Unexpected Files
1. **Add table definition**: Create new table entry for legitimate files
2. **Add to ignored_files**: Exclude files that shouldn't be processed
3. **Move files**: Relocate files to appropriate directories
4. **Clean up**: Remove temporary or obsolete files

#### Ambiguous Mappings
1. **Refine patterns**: Make glob patterns more specific
2. **Restructure files**: Organize files to avoid pattern overlap
3. **Review catalog**: Check for duplicate or overly broad patterns
4. **Use subdirectories**: Separate files into distinct paths

### Debugging Tips

#### Review Inventory Summary
The generated CSV file provides detailed mapping information:
```csv
key,size,last_modified,table,glob,ignored
data/patients.csv,1024,2024-01-15,PATIENTS,patients*.csv,False
data/unknown.csv,512,2024-01-15,"","",False
temp/backup.txt,256,2024-01-14,"","",True
```

## Usage Examples

### Testing and Development

```python
# Test DAG for development
test_dag = DAG(
    'test_file_validation',
    default_args={
        'owner': 'developer',
        'start_date': datetime(2024, 1, 1),
    },
    description='Test file validation with sample data',
    schedule_interval=None,  # Manual trigger only
    catchup=False,
    tags=['test', 'development'],
)

# Test with specific client/dataset
test_validation = FileValidator(
    task_id='test_validate_files',
    raw_data_path='test-bucket/sample-client/sample-dataset/raw/',
    raw_data_inventory_summary_path='test-bucket/sample-client/sample-dataset/test-inventory.csv',
    rdc_path='s3://test-bucket/catalogs/sample-client-sample-dataset-catalog.yml',
    client='sample-client',
    dataset='sample-dataset',
    aws_conn_id='aws_test',
    dag=test_dag
)
```
