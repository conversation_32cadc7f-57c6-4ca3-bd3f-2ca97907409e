# CopyEnumsAndDictionaries Airflow Operator - Developer Documentation

## Overview & Purpose

The **CopyEnumsAndDictionaries** operator is a critical component of Aetion's healthcare data processing pipeline that handles the transfer and intelligent merging of enumeration (enum) and dictionary files between different S3 locations. It ensures data consistency and completeness by preserving existing enum mappings while incorporating new values from source datasets.

### Primary Goals
- **Data Consistency**: Maintains consistent enum mappings across different data processing stages
- **Incremental Updates**: Merges new enum values without losing existing mappings
- **Performance**: Uses parallel processing for efficient large-scale data transfers
- **Data Integrity**: Preserves order and handles edge cases like missing files

### Use Cases
- **Healthcare Data Ingestion**: First step in processing patient data with standardized code mappings
- **Data Transformation Workflows**: Between patient filtering and full shard creation operations
- **Multi-source Data Consolidation**: Combining enum mappings from different healthcare data sources
- **Incremental Data Processing**: Adding new enum values while preserving existing mappings

## Core Functionality

The CopyEnumsAndDictionaries operator performs three main operations in sequence:

1. **Dictionary Copy**: Copies all dictionary files from source to destination using parallel processing
2. **Enum Resolution**: Intelligently merges enum files from two sources:
   - Previous enums (from `transform_path`): Existing enum mappings to preserve
   - New enums (from `source_enum_and_dict_path`): New enum values to incorporate
3. **Enum Upload**: Writes the merged enum data to the destination in CSV format

### Data Flow Architecture

```
transform_path/
└── enums.csv                    # Previous enum mappings (to preserve)

source_enum_and_dict_path/
├── dict/                        # Dictionary files (copied as-is)
│   ├── DIAGNOSIS_CODE.dict.csv
│   ├── PROCEDURE_CODE.dict.csv
│   └── ...
└── enums/
    └── enums.csv               # New enum mappings (to incorporate)

                    ↓ CopyEnumsAndDictionaries ↓

dest_enum_and_dict_path/
├── dict/                       # Copied dictionary files
│   ├── DIAGNOSIS_CODE.dict.csv
│   ├── PROCEDURE_CODE.dict.csv
│   └── ...
└── enums/
    └── enums.csv              # Merged enum file
```

### Enum File Format

Enum CSV files follow this standardized structure:
```csv
enum,code,label
BENE_SEX_IDENT_CD,M,Male
BENE_SEX_IDENT_CD,F,Female
BENE_RACE_CD,B,Black
BENE_RACE_CD,H,Hispanic
BENE_RACE_CD,W,White
```

Where:
- **enum**: The enum category/field name (e.g., gender, race, state codes)
- **code**: The coded value (often numeric or short string)
- **label**: Human-readable description of the code

## Key Components

### S3 Filesystem Integration

#### `fs` Property
```python
@property
def fs(self) -> AetionS3FileSystem
```
- **Purpose**: Lazy-initialized S3 filesystem interface
- **Implementation**: Creates `AetionS3FileSystem` instance on first access
- **Configuration**: Uses `aws_conn_id` for AWS connection management
- **Caching**: Reuses the same instance across multiple method calls

### Dictionary Operations

#### `copy_dictionaries()` Method
```python
def copy_dictionaries(self, source_dictionary_path: str, dest_dictionary_path: str) -> None
```
- **Purpose**: Copies all dictionary files from source to destination using parallel processing
- **Performance**: Uses configurable `copy_parallelism` for concurrent operations
- **Pattern Matching**: Copies all files (pattern `.*`) in the source directory
- **Use Case**: Bulk transfer of lookup tables for healthcare coding systems

### Enum Resolution Logic

#### `resolve_enums()` Static Method
```python
@staticmethod
def resolve_enums(fs: AetionS3FileSystem, previous_enums_path: str, new_enums_path: str) -> dict[str, OrderedDict[str, str]]
```
- **Purpose**: Intelligently merges enum files from two different sources
- **Merge Logic**:
  1. **Preserve Existing**: All existing enum mappings are preserved
  2. **Add New Values**: New codes within existing enums are added
  3. **Add New Enums**: Entirely new enum categories are added
  4. **Order Preservation**: Uses OrderedDict to maintain insertion order
  5. **No Overwrites**: Existing code-label mappings are never modified
- **Error Handling**: Gracefully handles missing enum files (returns None)

#### `upload_enums()` Method
```python
def upload_enums(self, final_enums: dict[str, OrderedDict[str, str]], dest_path: str) -> None
```
- **Purpose**: Writes merged enum data to S3 in standard CSV format
- **Format**: Three-column CSV with headers: enum, code, label
- **Performance**: Sequential write operation optimized for large enum datasets

## Configuration Options

### Constructor Parameters

#### Required Parameters
```python
def __init__(self,
             transform_path: str,                    # S3 path to transformation directory with previous enums
             source_enum_and_dict_path: str,         # S3 path to source directory with new data
             dest_enum_and_dict_path: str,           # S3 path to destination directory
             copy_parallelism: int,                  # Number of parallel workers for dictionary copying
             **kwargs)
```

#### Optional Parameters
```python
git_meta_repo: str | None = None        # Git repository metadata (unused, for compatibility)
aws_conn_id: str = "aws_default"        # Airflow connection ID for AWS/S3 access
```

### Template Fields

The following fields support Airflow Jinja2 templating:
- `transform_path`
- `source_enum_and_dict_path`
- `dest_enum_and_dict_path`

### Performance Configuration

#### Copy Parallelism Guidelines
- **Small datasets (< 1GB)**: 2-5 workers
- **Medium datasets (1-10GB)**: 5-10 workers
- **Large datasets (> 10GB)**: 8-15 workers
- **Very large datasets**: 10-20 workers (diminishing returns beyond this)

#### Performance Impact
- `parallelism=1`: ~100MB/min transfer rate
- `parallelism=5`: ~400MB/min transfer rate
- `parallelism=10`: ~700MB/min transfer rate
- `parallelism=20`: ~1GB/min transfer rate

## Merge Logic Details

The enum merging follows these specific rules to ensure data consistency:

### Scenario 1: Existing Enum Category with New Codes
```python
# Previous enums
BENE_SEX_IDENT_CD: {"M": "Male", "F": "Female"}

# New enums
BENE_SEX_IDENT_CD: {"U": "Unknown"}

# Result
BENE_SEX_IDENT_CD: {"M": "Male", "F": "Female", "U": "Unknown"}
```

### Scenario 2: Entirely New Enum Category
```python
# Previous enums
BENE_SEX_IDENT_CD: {"M": "Male", "F": "Female"}

# New enums
SP_STATE_CODE: {"MD": "MARYLAND", "CA": "CALIFORNIA"}

# Result
BENE_SEX_IDENT_CD: {"M": "Male", "F": "Female"}
SP_STATE_CODE: {"MD": "MARYLAND", "CA": "CALIFORNIA"}
```

### Scenario 3: Conflicting Code-Label Mappings
```python
# Previous enums (preserved)
BENE_RACE_CD: {"W": "White"}

# New enums (ignored for existing codes)
BENE_RACE_CD: {"W": "Caucasian"}

# Result (existing mapping preserved)
BENE_RACE_CD: {"W": "White"}
```

## Error Handling

The operator provides comprehensive error handling for common failure scenarios:

### S3 Access Errors
```python
# Permission denied
botocore.exceptions.NoCredentialsError: "Unable to locate credentials"

# Bucket not found
botocore.exceptions.ClientError: "The specified bucket does not exist"

# Path not found
FileNotFoundError: "No such file or directory: 's3://bucket/path/'"
```

### File Operation Errors
```python
# Missing source files
FileNotFoundError: "Source dictionary or enum paths do not exist"

# Write permission issues
PermissionError: "Access to S3 destination location is denied"

# S3 connection failures
Exception: "S3 connection errors or file operation failures"
```

### Resolution Strategies

#### Missing Files
1. **Verify S3 paths**: Ensure all paths exist and are accessible
2. **Check permissions**: Verify read access to source and write access to destination
3. **Review file structure**: Confirm expected directory structure (dict/, enums/)
4. **Test connectivity**: Validate AWS connection configuration

#### Performance Issues
1. **Adjust parallelism**: Tune `copy_parallelism` based on dataset size
2. **Monitor resources**: Check cluster memory and network bandwidth
3. **Review S3 limits**: Consider S3 rate limiting for high parallelism
4. **Optimize connection**: Use high-bandwidth AWS connection for large datasets

## Usage Examples

### Basic Healthcare Data Processing
```python
from airflow import DAG
from datetime import datetime
from operators.copy_enums_and_dictionaries import CopyEnumsAndDictionaries

# Standard SYNPUF dataset processing
copy_enums_and_dicts = CopyEnumsAndDictionaries(
    task_id='copy_enums_and_dictionaries',
    transform_path='s3://adipalbert.app.dev.aetion.com/etl/synpuf_subset_filtered/20221004/transformation/',
    source_enum_and_dict_path='s3://adipalbert.app.dev.aetion.com/etl/synpuf_subset/20211018/full-shard/',
    dest_enum_and_dict_path='s3://adipalbert.app.dev.aetion.com/etl/synpuf_subset_filtered/20221004/full-shard/',
    copy_parallelism=5,
    aws_conn_id='aws_default'
)
```

### High-Performance Configuration
```python
# Large dataset with optimized settings
copy_large_dataset = CopyEnumsAndDictionaries(
    task_id='copy_large_dataset_enums',
    transform_path='s3://adipalbert.app.dev.aetion.com/etl/claims_large/20230101/transformation/',
    source_enum_and_dict_path='s3://adipalbert.app.dev.aetion.com/etl/claims_source/20230101/full-shard/',
    dest_enum_and_dict_path='s3://adipalbert.app.dev.aetion.com/etl/claims_processed/20230101/full-shard/',
    copy_parallelism=12,  # Higher parallelism for large datasets
    aws_conn_id='high_bandwidth_s3_conn'  # Optimized connection
)
```

### Multi-Source Data Consolidation
```python
# Consolidating patient data from multiple sources
consolidate_patient_data = CopyEnumsAndDictionaries(
    task_id='consolidate_patient_data',
    transform_path='s3://adipalbert.app.dev.aetion.com/etl/master_enums/current/transformation/',
    source_enum_and_dict_path='s3://adipalbert.app.dev.aetion.com/etl/hospital_a/20230101/full-shard/',
    dest_enum_and_dict_path='s3://adipalbert.app.dev.aetion.com/etl/consolidated/20230101/full-shard/',
    copy_parallelism=8
)
```

## Integration Points

The CopyEnumsAndDictionaries operator is typically used in healthcare data processing pipelines between:

- **Patient filtering operations**: After filtering patient cohorts
- **Full shard creation**: Before creating complete data shards
- **Data validation steps**: As part of data quality assurance
- **Package deployment preparation**: Before final data packaging

## Best Practices

### Performance Optimization
1. **Right-size parallelism**: Start with 5-8 workers and adjust based on performance
2. **Monitor S3 costs**: High parallelism can increase S3 request costs
3. **Use appropriate connections**: Configure AWS connections with sufficient bandwidth
4. **Consider data locality**: Place source and destination in the same AWS region

### Data Integrity
1. **Validate inputs**: Ensure source paths contain expected file structures
2. **Monitor enum growth**: Track enum file sizes to detect unexpected data growth
3. **Backup critical enums**: Preserve important enum mappings before major updates
4. **Test merge logic**: Validate enum merging with representative test data

### Operational Monitoring
1. **Log analysis**: Monitor operator logs for performance and error patterns
2. **Resource usage**: Track memory and CPU usage during large transfers
3. **Data lineage**: Document enum sources and transformation history
4. **Alert configuration**: Set up alerts for operator failures or performance degradation

## See Also

- **AetionS3FileSystem**: Underlying S3 operations interface
- **AutomaticallyUpdateEnums**: Related operator for enum synchronization
- **Patient filtering pipelines**: Common upstream integration point
- **Full shard creation**: Common downstream integration point
