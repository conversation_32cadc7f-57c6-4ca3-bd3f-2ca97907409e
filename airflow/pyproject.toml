[build-system]
requires = ["setuptools == 67.6.1"]

[project]
name = "adip-airflow"
version = "25.0801.1"
description = "ADIP Airflow"
readme = "README.md"
requires-python = ">=3.10"


[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --cov=dags --cov-branch --cov-report xml --cov-report html --cov-report term-missing"
testpaths = [
    "tests"
]
pythonpath = [
    "../spark-validator",
    "dags",
    "plugins",
    "config",
    "../adip_path_config"
]

[[tool.uv.index]]
url = "https://nexus.eng.aetion.com/repository/pypi/simple"
default = true
