# Release v25.0801.1 (from v25.0730.1)

2025-08-01: [PE-6263] removed compare_enums and tests for it (#4733) (53dc9fa)
2025-08-01: created new airflow tasks for automating lookups (#4646) (ca59d0c)

# Release v25.0730.1 (from v25.0725.1)

2025-07-30: patient_prep bug fix (#4725) (0e3d7b2)
2025-07-29: PE-6199: airflow: artifact publisher operator uses S3hook (#4722) (cca90ee)
2025-07-29: fix(airflow): s3_support| get method (#4713) (b3e4607)

# Release v25.0725.1 (from v25.0723.1)

2025-07-25: Order steps and spark group config (#4706) (f7a5563)
2025-07-25: fix(gha): add git_fetch_depth and fetch tags in some gha (#4703) (759f8d3)
2025-07-25: PE-5626 - feat(airflow): Use S3Hook as base for our interactions with AWS S3 (#4649) (13f1382)
2025-07-24: fix(gha): adip-build-image| git_version an version.txt generation (#4688) (4c5bb70)

# Release v25.0723.1 (from v25.0721.2)

2025-07-23: feat(gha): airflow| checkout repo with full history (#4683) (11dbaa2)
2025-07-23: fix(gha): airflow| add explicitly a  checkout step (#4680) (f7084e1)
2025-07-23: feat(gha): airflow| changes in entrypoint.sh may trigger a release (#4679) (456b9e7)
2025-07-23: feat(airflow): recreate the 'aws_default' connection in the entrypoint (#4677) (6e567c2)
2025-07-22: Feat/pe 5532 validation fails for adm empty tables (#4490) (f065aa6)

# Release v25.0721.2 (from v25.0721.1)

2025-07-21: Hotfix/check only active datasets var (#4671) (4750768)
2025-07-21: Only check the datasets var of the enabled config version (#4668) (48b0d68)

# Release v25.0721.1 (from v25.0717.1)

2025-07-21: Feat/pe 6089 tiny fix (#4664) (fc80e57)
2025-07-18: PE-5935 Slack notifications improvement (#4647) (9d4d882)

# Release v25.0717.1 (from v25.0711.2)

2025-07-17: build(airflow): dbc-utilities==0.0.45 (#4656) (9635f78)
2025-07-17: config(airflow): dbc-utilities==0.0.44 (#4655) (1f52083)
2025-07-14: fixed paths in environment_defaults to match default_adip_config (#4645) (684f5fd)

# Release v25.0711.2 (from v25.0711.1)

2025-07-11: Added connector repo to path (#4616) (e3a0f08)

# Release v25.0711.1 (from v25.0704.1)

2025-07-11: feat(airflow): add default spark/prophecy images for isolated_python_deps feature (#4625) (8806692)
2025-07-10: Fix s3a partition full (#4605) (5cb7743)
2025-07-10: PE-5631: Isolate python dependencies in adip spark/prophecy images (#4609) (b0c9de7)
2025-07-10: Added better debug config when --debug also raise error when memory negative (#4613) (0c43039)
2025-07-09: PE-6058: fixed bug with slack error  notifications not being sent for pyspark … (#4614) (a7ecbce)
2025-07-08: added config for using ZSTD compression as default during full shard (#4602) (7e32df3)
2025-07-08: PE-3516 Validate RDC against metadata before partitioning (#4510) (f5084e5)
2025-07-04: refactor(airflow): update the default adip-spark (#4592) (638a553)

# Release v25.0704.1 (from v25.0701.1)

2025-07-04: refactor(airflow): add a trailing slash to the url path of the driver metrics (#4589) (f35e9d5)
2025-07-03: Added default tiny config and apply that in the tiny pipelines (#4588) (67e99c3)
2025-07-03: Feat/pe 3620 send relevant files in some of the slack notifications (#4566) (7d546bd)

# Release v25.0701.1 (from v25.0630.1)

2025-07-01: fix(airflow): PE-6002: tpa_start and tpa_end passed as empty string when None (#4572) (e4f13e6)

# Release v25.0630.1 (from v25.0627.2)

2025-06-30: refactor(airflow): move test env creation into justfile (#4567) (ed2d59d)
2025-06-30: Switched to static data in tags added stage for billing monitoring atomic (#4564) (01e8046)
2025-06-30: Added preprocess steps to config (#4563) (e932e09)
2025-06-30: PE-5906: implemented suggested GDR in date range report (#4519) (62d2da6)
2025-06-30: pe-5631: fix python_path_featurer at pipeline level (#4562) (81677b2)
2025-06-30: If parsed group is job name it default (#4559) (adef3b9)

# Release v25.0627.2 (from v25.0627.1)

2025-06-27: fix(airflow): PE-5631: resolve pythonpath in preflight unarchiver (#4551) (09c582d)
2025-06-27: refactor(airflow): PE-5631: add python_path feature in preflight + fix load airflow variables (#4542) (adc8462)
2025-06-27: refactor: PE-5631: add justfile + cicd python script + gha (#4546) (577ba72)
2025-06-27: fix(adip-common): downgrade gitpython to 3.1.41 (#4544) (9718b0f)
2025-06-27: Revert "fix(airflow): PE-5631: python_path_feature in prefligtht + load config from airflow vars" (a7794d8)
2025-06-27: fix(airflow): PE-5631: python_path_feature in prefligtht + load config from airflow vars (0e34dc5)

# v25.0627.1  (since v25.0625.1)

2025-06-27 Feat/re instaurate adip config check (#4530) (8a695083f)
2025-06-27 Feat/deep mergew spark configs (#4536) (adae612c6)
2025-06-26 PE-5889 Add preprocess support to patient and validation prep (#4442) (a4b3f0e80)
2025-06-26 PE-5631: unarchiver build for python 3.10 (#4531) (034110b64)
2025-06-25 Feat/re instaurate adip config check (#4509) (a265e7b45)


# v25.0625.1  (since v25.0621.1)

2025-06-25 Get branch or default for creadentials (#4493) (d860e3657)
2025-06-25 Use empty value instead of null for factors default (#4500) (997961c56)
2025-06-23 feat(airflow): PE-5631: python_path_feature| Add PYTHONPATH to pyspark apps (#4499) (f102eb3d7)
2025-06-23 pe-5931: downgrade yamllint to meet airflow constraints (#4498) (893e20ad3)
2025-06-23 Merge error made cluster calculations not available (#4497) (0da849b3a)
2025-06-23 fix(airflow): only register kryo serializer of spark-connector when required (#4492) (5e0d50f42)
2025-06-23 Migrate memory_on disk (#4495) (d3aaabc67)


# v25.0621.1  (since v25.0618.1)

2025-06-19 PE:5905: added tpa variables (#4487) (35ccd8f48)
2025-06-19 Skip if nothing to commit (#4485) (ab5b81735)
2025-06-18 PE-5882 airflow upgrade (#4483) (407663628)
2025-06-18 Remove pipeline.enum_automation_override from steps inputs (#4477) (fa0cfee2a)
2025-06-18 PE-3082: delete bucketed/chunked tables during delete_full_flat_tables (#4476) (cb942a7cd)


# v25.0618.1  (since v25.0605.1)

2025-06-18 PE-5141 transform path override for deploy package build (#4433) (e9f4bdf9f)
2025-06-17 chase spark-validator upgrade (#4470) (e528413e1)
2025-06-17 Added slack message and alert_scientist parameter (#4457) (1565d5d11)
2025-06-16 Feat/pe 4546 enums branch check (#4435) (328dc31a5)
2025-06-16 fix(airflow): PE-5287 Register Kryo Serializers in Apache Spark (IntMapping) (#4454) (819795dcf)
2025-06-10 PE-2778: create metadata for ADM pipelines (#4399) (34d74bf09)
2025-06-05 refactor(airflow): replace conda by uv as  virtual environment manager and package manager (#4426) (9efd09882)


# v25.0605.1  (since v25.0604.2)

2025-06-05 PE-3114 dbc_utilities for validation_prep and full_validation_prep (#4395) (c00c9ca39)
2025-06-05 fix(airflow): SpecCopyOperator| use_copy_spec params is a boolean (#4421) (664ded326)


# v25.0604.2  (since v25.0604.1)

2025-06-04 fix(airflow): some variables might be None (#4419) (81d02094e)


# v25.0604.1  (since v25.0603.1)

2025-06-04 Feat/switch yaml formatter (#4412) (88729aa29)
2025-06-04 ci(airflow): disable triggering airflow workflows on spark-validator changes (#4415) (7f012b501)
2025-06-04 Fixed pre-commits (#4400) (c5c322b7c)
2025-06-04 refactor(airflow): set spark.executor.metrics.pollingInterval to 10s (#4414) (e01c238b0)


# v25.0603.1  (since v25.0602.1)

2025-06-03 ci(airflow): trigger workflows on adip changes + requirements_spark-validator.txt (#4410) (02b9a4bb4)
2025-06-03 deps(airflow): set spark-validator==1.0.4 (#4408) (b9b1f30e2)
2025-06-03 PE-5757: use virtualenv for spark-validator functionality in airflow (duplicate) (#4401) (26253493f)
2025-06-02 Improve yaml variable documentation (#4381) (402132ae0)


# v25.0602.1  (since v25.0530.1)

2025-05-30 fix(airflow): include 'spark_monitoring_feature' the template params of celeborn task in preflight template (#4388) (76f45b192)


# v25.0530.1  (since v25.0527.1)

2025-05-30 feat(airflow): PE-5747 enable spark monitoring by default (#4385) (6da24d71d)
2025-05-30 fix(airflow): PE-5606| prevent driver pod not found (#4362) (83f00a954)
2025-05-29 fixed bug recently introduced, added logs (#4382) (f9bc5f44f)
2025-05-29 Added celery patching to spark configs (#4380) (64fa5b0c6)
2025-05-29 PE-4412 dbc-utilities-tests for patient_prep (#3933) (8b21c9f11)
2025-05-27 Updating variables and setting data_ingestion only if enable_v2 is set (#4369) (597aa47d3)


# v25.0527.1  (since v25.0521.1)

2025-05-27 Fix case when aetionpkg.shard.json changed directory (#4347) (3c1469522)
2025-05-27 feat(airflow): PE-5643 expose spark metrics in DIP pipelines (#4338) (2cc665ff3)
2025-05-26 Feat/pe 5553 create concord triggering adip variables manager (#4350) (a60eb74e4)
2025-05-23 Fixed bootstap variable change (#4345) (1fda8dabb)
2025-05-23 fix(ci): set correct Git user identity and token for GitHub Actions automation (#4343) (49c1cff02)
2025-05-22 chore(airflow): expose spark metrics in preflight pipeline + refactor preflight sparkapp template (#4335) (43d52e54d)
2025-05-22 Pe 4948/adip configuration creation (#4247) (c701f77be)


# v25.0521.1  (since v25.0516.1)

2025-05-21 feat(airflow): upgrade python to 3.10 (#4196) (68541c50b)


# v25.0516.1  (since v25.0509.2)

2025-05-16 refactor(airflow): use urllib.parse for compatibility with python v3.10+ (#4311) (cb29c6f18)
2025-05-13 refactor(airflow): move preflight dag-level params to task levels (#4287) (08be9e069)


# v25.0509.2  (since v25.0509.1)

2025-05-09 fix(celeborn): make available celeborn stresser pet in all environments (#4273) (fb7583d60)


# v25.0509.1  (since v25.0508.1)

2025-05-09 fix(airflow): egg artifacts not supported in databricks 14.3 and above (#4270) (adb76aa7d)


# v25.0508.1  (since v25.0506.1)

2025-05-08 fix(airflow): mark spark-targeted task green when driver pod succeeds (#4261) (4f7011324)
2025-05-07 Modifyied list coding (#4260) (0abc3d5b0)


# v25.0506.1  (since v25.0425.1)

2025-05-06 feat(airflow): use databricks runtime 14.3 LTS (#4091) (8e66c2d63)
2025-04-25 fix(airflow): excluded_tasks when two celeborn features are combined (#4199) (521ce8fe8)


# v25.0425.1  (since v25.0424.3)

2025-04-25 feat(airflow): (PE-5061) Upgrade airflow to python 3.9 (#4092) (474466f63)


# v25.0424.3  (since v25.0424.2)

2025-04-24 ci(airflow): use the main branch of the docker aetion action (#4191) (7b76a7527)


# v25.0424.2  (since v25.0424.1)

2025-04-24 fix(airflow): healthverity-filter-patient uses default spark docker image (#4186) (85f98d61a)
2025-04-24 ci(airflow): use version from commit message (#4187) (dcbf13794)


# v25.0424.1  (since v25.0423.2)

2025-04-24 ci(airflow): revert git user on release pr workflow (#4180) (381833bbd)
2025-04-24 ci(airflow): use personal email as committer for release pr (#4178) (1b6bd22c2)
2025-04-24 ci(airflow): add a empty commit to release pr to workaround stuck jobs (#4176) (370e00b22)
2025-04-24 ci(airflow): change committer of the release pr (#4174) (6ded4e871)
2025-04-23 ci(airflow): add label  'release' to release PR (#4171) (bd95df477)
2025-04-23 ci(airflow): fix computing next release part in the same day (#4167) (8fc284c25)
2025-04-23 ci(airflow): extend trackable assets to some github actions workflow files (#4165) (0f798244c)


# v25.0423.1  (since v25.0320.1)

2025-04-23 PE-5322: ADiP Airflow Release Versioning/Tagging (#4105) (63cf60cb6)
2025-04-17 Add workaround for Excel bug (#4117) (fce8087d1)
2025-04-15 pe-5370: Allow excluding sparkapps from using celeborn (#4113) (3aefbfb32)
2025-04-07 feat(airflow): celeborn task in preflight pipeline (#4081) (2ecc0f4ac)
2025-04-07 Pe 2764 validate shard package values (#4057) (ecdde2f08)
2025-04-04 legacy_mismatched_datasets.json fix (#4089) (efba16a28)
2025-04-03 dbc_utilities airflow upgrade (#4085) (03a59d57a)
2025-04-01 refactor(airflow): use XML format in log4j2 configuration (#4075) (e1e4c2361)
2025-03-31 PE-5258: fix(airflow): celeborn spark conf default resolution (#4073) (8d86444af)
2025-03-31 bufix(airflow): use k8s_spark_image from pipeline config is defined (#4074) (dbc5bdcfa)
2025-03-29 fix(airflow): typo in celeborn default spark conf settings (#4070) (0fa8c75d8)
2025-03-28 fix(airflow): reference to the celeborn client jvm class (#4068) (31650f44f)
2025-03-28 pe-5181: fix(airflow) global celeborn (#4067) (0486ab7de)
2025-03-28 PE-5196: add aws sdk v2 library in non-prophecy databricks jobs (#4066) (cba5d415b)
2025-03-27 dbc-utilities==0.0.28 (#4062) (e28e23732)
2025-03-26 fixed rde conf prep (#4058) (a96bd087b)
2025-03-25 PE-4381 rdc preprocessed tables (#4020) (260af7d03)
2025-03-24 fix(airflow): missing 'labels' param in preflight (#4052) (f92d68c61)
2025-03-24 PE-5183: use Celeborn External Shuffle Service in k8s spark apps (#4051) (988ce0d68)
2025-03-21 fix(airflow): missing client/Dataset/Revision annotations in sparkapp manifest (#4043) (db4b7b52a)
2025-03-20 dbc_utilities upgrade for Airflow (#4047) (63506df7b)