#!/usr/bin/env python3
"""
Local script to build the configuration for data_ingestion_adip0_synpuf_tiny_subset
"""

import os
import sys
import json
import yaml
from pathlib import Path

def build_config_locally():
    """Build the configuration locally to see the final merged result"""
    
    # Configuration details
    env_name = 'adip0'
    target_pipeline = 'data_ingestion_adip0_synpuf_tiny_subset'
    
    print(f"=== BUILDING CONFIGURATION FOR {target_pipeline} ===")
    print(f"Environment: {env_name}")
    print()
    
    # 1. Load the main pipeline configuration
    pipeline_config_path = Path(f"config/environments/{env_name}/pipeline/{target_pipeline}/{target_pipeline}.yaml")
    print(f"1. Loading main pipeline config: {pipeline_config_path}")
    
    if not pipeline_config_path.exists():
        print("   ✗ Main pipeline config file does not exist!")
        return
    
    with open(pipeline_config_path, 'r') as f:
        pipeline_config = yaml.safe_load(f)
    
    print("   ✓ Main pipeline config loaded")
    print(f"   - Dataset: {pipeline_config.get('dataset')}")
    print(f"   - Client: {pipeline_config.get('client')}")
    print(f"   - Revision: {pipeline_config.get('revision')}")
    print()
    
    # 2. Load the tiny databricks step group config
    # First look in adip_common package (this is where it actually exists)
    tiny_config_path = Path("../adip/adip_common/dags_configuration/default_adip_config/step_group_cluster_config/tiny_databricks_step_group_config.yaml")
    print(f"2. Loading tiny databricks config: {tiny_config_path}")
    
    if not tiny_config_path.exists():
        print("   ✗ Tiny databricks config file does not exist in adip_common package!")
        return
    
    with open(tiny_config_path, 'r') as f:
        tiny_config = yaml.safe_load(f)
    
    print("   ✓ Tiny databricks config loaded from adip_common package")
    print(f"   - Default workers: {tiny_config.get('default', {}).get('num_workers')}")
    print(f"   - Default parallelism: {tiny_config.get('default', {}).get('spark_conf', {}).get('spark.default.parallelism')}")
    print()
    
    # 3. Simulate the Hydra search path behavior
    print("3. Simulating Hydra search path resolution...")
    
    # Hydra search paths (in order):
    # 1. pkg://adip_common.dags_configuration.default_adip_config
    # 2. file://{config_path}/environment_defaults/{env_name}/
    
    print("   Hydra search paths:")
    print("   1. pkg://adip_common.dags_configuration.default_adip_config")
    print("   2. file://config/environment_defaults/adip0/")
    print()
    
    # When tiny config references /step_group_cluster_config/databricks_step_group_config@_here_
    # Hydra looks for this file in both search paths:
    
    # Search path 1: adip_common package
    base_databricks_config_path = Path("../adip/adip_common/dags_configuration/default_adip_config/step_group_cluster_config/databricks_step_group_config.yaml")
    print(f"   Looking in search path 1: {base_databricks_config_path}")
    
    if base_databricks_config_path.exists():
        with open(base_databricks_config_path, 'r') as f:
            base_databricks_config = yaml.safe_load(f)
        print("   ✓ Found base databricks config in adip_common package")
        print(f"   - Default spark_version: {base_databricks_config.get('default', {}).get('spark_version')}")
    else:
        print("   ✗ Base databricks config not found in adip_common package!")
        return
    
    # Search path 2: environment-specific override
    # BUG: Hydra looks for step_group_cluster_config/databricks_step_group_config.yaml
    # But the file is actually at databricks_step_group_config.yaml
    env_databricks_config_path = Path("config/environment_defaults/adip0/step_group_cluster_config/databricks_step_group_config.yaml")
    print(f"   Looking in search path 2: {env_databricks_config_path}")
    
    if env_databricks_config_path.exists():
        with open(env_databricks_config_path, 'r') as f:
            env_databricks_config = yaml.safe_load(f)
        print("   ✓ Found environment databricks config override")
        print(f"   - Override spark_version: {env_databricks_config.get('default', {}).get('spark_version')}")
    else:
        print("   ✗ Environment databricks config override NOT FOUND!")
        print("   This is the bug! The file structure doesn't match the search path.")
        print("   Expected: config/environment_defaults/adip0/step_group_cluster_config/databricks_step_group_config.yaml")
        print("   Actual:   config/environment_defaults/adip0/databricks_step_group_config.yaml")
        
        # Check what actually exists
        actual_env_config_path = Path("config/environment_defaults/adip0/databricks_step_group_config.yaml")
        if actual_env_config_path.exists():
            with open(actual_env_config_path, 'r') as f:
                actual_env_config = yaml.safe_load(f)
            print(f"   ✓ Found actual environment config: {actual_env_config_path}")
            print(f"   - Actual spark_version: {actual_env_config.get('default', {}).get('spark_version')}")
            print("   But Hydra can't find it due to the path mismatch!")
        else:
            print("   ✗ No environment config found at all!")
        
        # Use the base config since the override wasn't found
        env_databricks_config = base_databricks_config.copy()
    
    # Start with the base databricks config
    merged_config = base_databricks_config.copy()
    
    # If environment override was found, merge it in
    if env_databricks_config != base_databricks_config:
        print("   Merging environment override into base config...")
        if 'default' in env_databricks_config:
            if 'default' not in merged_config:
                merged_config['default'] = {}
            merged_config['default'].update(env_databricks_config['default'])
    
    # Merge in the tiny config overrides
    if 'default' in tiny_config:
        if 'default' not in merged_config:
            merged_config['default'] = {}
        print("   Merging tiny config overrides...")
        merged_config['default'].update(tiny_config['default'])
    
    # Merge in the pipeline-specific config
    for key, value in pipeline_config.items():
        if key == 'defaults':
            continue  # Skip the defaults section
        merged_config[key] = value
    
    print("   ✓ Configuration merged")
    print()
    
    # 4. Display the final configuration
    print("4. FINAL CONFIGURATION:")
    print("=" * 50)
    
    # Pretty print the configuration
    print(yaml.dump(merged_config, default_flow_style=False, indent=2))
    
    # 5. Show key configuration details
    print("5. KEY CONFIGURATION DETAILS:")
    print("=" * 50)
    
    # Pipeline details
    print(f"Pipeline Details:")
    print(f"  - Dataset: {merged_config.get('dataset')}")
    print(f"  - Client: {merged_config.get('client')}")
    print(f"  - Revision: {merged_config.get('revision')}")
    print(f"  - Is K8s: {merged_config.get('is_k8s')}")
    print(f"  - Alert User: {merged_config.get('alert_user')}")
    print(f"  - Domain: {merged_config.get('domain')}")
    print()
    
    # Spark configuration
    if 'steps_spark_config' in merged_config:
        print(f"Spark Configuration:")
        spark_config = merged_config['steps_spark_config']
        for job_type, config in spark_config.items():
            print(f"  {job_type}:")
            print(f"    - Workers: {config.get('num_workers', 'N/A')}")
            print(f"    - Node Type: {config.get('node_type_id', 'N/A')}")
            print(f"    - Parallelism: {config.get('spark_conf', {}).get('spark.default.parallelism', 'N/A')}")
            print(f"    - Shuffle Partitions: {config.get('spark_conf', {}).get('spark.sql.shuffle.partitions', 'N/A')}")
    
    # Default cluster config
    if 'default' in merged_config:
        print(f"Default Cluster Config:")
        default_config = merged_config['default']
        print(f"  - Workers: {default_config.get('num_workers', 'N/A')}")
        print(f"  - Node Type: {default_config.get('node_type_id', 'N/A')}")
        print(f"  - Driver Node Type: {default_config.get('driver_node_type_id', 'N/A')}")
    
    print()
    print("=== CONFIGURATION BUILD COMPLETE ===")

if __name__ == "__main__":
    build_config_locally() 