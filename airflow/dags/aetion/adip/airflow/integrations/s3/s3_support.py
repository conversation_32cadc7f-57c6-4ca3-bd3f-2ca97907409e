from collections.abc import Generator
import datetime
import glob
import logging
import os
import re
from contextlib import contextmanager
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from io import Bytes<PERSON>, String<PERSON>, TextIOWrapper
from typing import Any, NamedTuple, Optional, Tuple, Union
from urllib.parse import urlparse

from airflow.providers.amazon.aws.hooks.s3 import S3Hook

logger = logging.getLogger(__name__)


"""
Aetion S3 File System Integration for Airflow
============================================

This module provides a comprehensive S3 file system abstraction built on top of Airflow's S3Hook,
designed to simplify S3 operations within Airflow DAGs while maintaining compatibility with
Airflow's connection management and configuration systems.

Key Components
--------------
- **AetionS3FileSystem**: Main class providing file system-like interface for S3 operations
- **S3Location**: Named tuple for structured S3 path representation
- **parse_s3_url**: Utility function for parsing S3 URLs

Design Philosophy
-----------------
This module follows a file system abstraction pattern, providing familiar operations like
`cat()`, `put()`, `ls()`, `rm()` that work seamlessly with S3. It leverages Airflow's S3Hook
for connection management, ensuring proper credential handling and connection pooling.

Key Features
------------
- Thread-safe parallel operations for bulk transfers
- Seamless integration with Airflow's connection management
- Support for both file and directory operations
- Comprehensive error handling and logging
- Memory-efficient streaming for large files

Usage Examples
--------------
Basic file operations::

    # Initialize filesystem
    fs = AetionS3FileSystem(aws_conn_id="my_s3_conn")

    # Read file content
    content = fs.cat("s3://my-bucket/data.txt")

    # Write data to S3
    fs.put(b"Hello World", "s3://my-bucket/output.txt")

    # List directory contents
    files = fs.ls("s3://my-bucket/folder/", detail=True)

Parallel operations for performance::

    # Upload directory with parallel workers
    fs.upload_dir_parallel(
        local_dir="/tmp/data",
        s3_prefix="s3://my-bucket/uploads/",
        max_workers=8
    )

    # Copy files with pattern matching
    fs.copy_with_pattern_parallel(
        source_path="s3://source-bucket/data/",
        target_path="s3://dest-bucket/backup/",
        pattern=r"\.parquet$",
        copy_parallelism=4
    )

Context manager for file operations::

    # Read file using context manager
    with fs.open("s3://my-bucket/data.csv", "r") as f:
        data = f.read()

    # Write file using context manager
    with fs.open("s3://my-bucket/output.json", "w") as f:
        json.dump(data, f)

Integration with Airflow
------------------------
This module is designed to work seamlessly within Airflow DAGs:

- Uses Airflow's connection management for AWS credentials
- Supports templated connection IDs for dynamic environments
- Provides comprehensive logging compatible with Airflow's logging system
- Thread-safe for use in parallel Airflow tasks

Thread Safety
-------------
The AetionS3FileSystem class is thread-safe for concurrent operations. Each instance
maintains its own S3Hook connection, and the underlying boto3 client handles
connection pooling automatically.

Performance Considerations
--------------------------
- Use parallel methods for bulk operations (upload_dir_parallel, copy_files_parallel)
- Consider memory usage when working with large files
- Leverage streaming operations for memory efficiency
- Use appropriate parallelism levels based on your infrastructure
"""


class AetionS3FileSystem:
    """
    Aetion S3 file system class providing a comprehensive file system interface for S3 operations.

    This class serves as the primary interface for all S3 operations within Aetion's Airflow
    environment. It wraps Airflow's S3Hook to provide a more intuitive, file system-like API
    while maintaining full compatibility with Airflow's connection management.

    Connection Management
    ---------------------
    The class uses lazy initialization for the S3Hook connection, creating it only when needed.
    This approach ensures efficient resource usage and proper connection lifecycle management
    within Airflow's execution context.

    Thread Safety
    -------------
    This class is thread-safe for concurrent operations. Each instance maintains its own
    S3Hook connection, and parallel operations use ThreadPoolExecutor for safe concurrency.

    Usage Patterns
    --------------
    Basic file operations::

        fs = AetionS3FileSystem()

        # Read operations
        content = fs.cat("s3://bucket/file.txt")
        metadata = fs.info("s3://bucket/file.txt")
        exists = fs.exists("s3://bucket/file.txt")

        # Write operations
        fs.put(b"data", "s3://bucket/output.txt")
        fs.upload("/local/file.txt", "s3://bucket/remote.txt")

        # Directory operations
        files = fs.ls("s3://bucket/folder/")
        fs.rm("s3://bucket/folder/", recursive=True)

    Parallel operations for performance::

        # Bulk upload with parallelism
        fs.upload_dir_parallel(
            local_dir="/data",
            s3_prefix="s3://bucket/uploads/",
            max_workers=8
        )

        # Parallel copy with pattern matching
        fs.copy_with_pattern_parallel(
            source_path="s3://src/data/",
            target_path="s3://dst/backup/",
            pattern=r"\.csv$",
            copy_parallelism=4
        )

    Context manager usage::

        with fs.open("s3://bucket/data.json", "r") as f:
            data = json.load(f)

    Attributes
    ----------
    aws_conn_id : str
        Airflow connection ID for AWS credentials
    _hook : Optional[S3Hook]
        Cached S3Hook instance (lazy-initialized)

    Parameters
    ----------
    aws_conn_id : str, default="aws_default"
        Airflow connection ID for AWS credentials. This should correspond to a
        connection configured in Airflow's connection management system.

    Examples
    --------
    Initialize with default connection::

        fs = AetionS3FileSystem()

    Initialize with custom connection::

        fs = AetionS3FileSystem(aws_conn_id="my_custom_s3_conn")

    Notes
    -----
    - The S3Hook is created lazily on first access to optimize resource usage
    - All S3 paths can be provided in s3:// or s3a:// format
    - The class handles both file and directory operations transparently
    - Parallel operations are optimized for bulk transfers and should be used
      for operations involving multiple files
    """

    def __init__(self, aws_conn_id: str = "aws_default") -> None:
        """
        Initialize AetionS3FileSystem with S3Hook.

        Args:
            aws_conn_id: Airflow connection ID for AWS credentials
        """
        self.aws_conn_id = aws_conn_id
        self._hook: Optional[S3Hook] = None

    @property
    def hook(self) -> S3Hook:
        """Get or create S3Hook instance."""
        if self._hook is None:
            self._hook = S3Hook(aws_conn_id=self.aws_conn_id)
        return self._hook

    @property
    def client(self):
        """Get the underlying boto3 S3 client from the hook."""
        return self.hook.get_conn()

    def _parse_s3_path(self, path: str) -> Tuple[str, str]:
        """
        Extract bucket and key components from an S3 path.

        This private method handles various S3 path formats and normalizes them
        into bucket and key components for use with boto3/S3Hook operations.

        Parameters
        ----------
        path : str
            S3 path in one of the following formats:
            - s3://bucket/key/path
            - s3a://bucket/key/path
            - bucket/key/path (direct format)

        Returns
        -------
        Tuple[str, str]
            A tuple containing (bucket_name, key_path)

        Raises
        ------
        ValueError
            If the path format is invalid or cannot be parsed

        Examples
        --------
        >>> fs = AetionS3FileSystem()
        >>> bucket, key = fs._parse_s3_path("s3://my-bucket/data/file.txt")
        >>> print(f"Bucket: {bucket}, Key: {key}")
        Bucket: my-bucket, Key: data/file.txt

        Notes
        -----
        - Leading slashes in keys are automatically stripped
        - Both s3:// and s3a:// schemes are supported for Spark compatibility
        - The method includes debug logging for troubleshooting path parsing issues
        """
        try:
            if path.startswith("s3://") or path.startswith("s3a://"):
                parsed = urlparse(path)
                bucket = parsed.netloc
                key = parsed.path.lstrip("/")
            else:
                parts = path.split("/", 1)
                if len(parts) != 2:
                    raise ValueError(f"Invalid S3 path format: {path}")
                bucket, key = parts

            logger.debug(f"Parsed S3 path: bucket={bucket}, key={key}")
            return bucket, key
        except Exception as e:
            logger.error(f"Invalid S3 path: {path}")
            raise e

    @contextmanager
    def open(self, path: str, mode: str = "r", encoding: str = "utf-8"):
        """
        Open an S3 file and return a file-like object.

        This method provides a context manager interface for reading from and writing
        to S3 objects, similar to Python's built-in open() function.

        Parameters
        ----------
        path : str
            S3 path to the file (e.g., "s3://bucket/key")
        mode : str, default="r"
            File access mode. Supported modes:
            - "r": Read text mode
            - "rb": Read binary mode
            - "w": Write text mode
            - "wb": Write binary mode
        encoding : str, default="utf-8"
            Character encoding for text modes (ignored for binary modes)

        Returns
        -------
        file-like object
            A file-like object that can be used in a context manager

        Raises
        ------
        ValueError
            If an unsupported mode is specified
        FileNotFoundError
            If the file does not exist (for read modes)
        PermissionError
            If access to the S3 object is denied

        Examples
        --------
        Read text file::

            fs = AetionS3FileSystem()
            with fs.open("s3://my-bucket/data.txt", "r") as f:
                content = f.read()
                print(content)

        Read binary file::

            with fs.open("s3://my-bucket/image.jpg", "rb") as f:
                image_data = f.read()

        Write text file::

            with fs.open("s3://my-bucket/output.txt", "w") as f:
                f.write("Hello World")

        Write binary file::

            with fs.open("s3://my-bucket/data.bin", "wb") as f:
                f.write(b"\\x00\\x01\\x02\\x03")

        Process large file line by line::

            with fs.open("s3://my-bucket/large.txt", "r") as f:
                for line in f:
                    process_line(line)

        Notes
        -----
        - Always use as a context manager to ensure proper resource cleanup
        - For read operations, the entire file is loaded into memory
        - For write operations, data is buffered until the context exits
        - Binary modes work with bytes, text modes work with strings
        """
        bucket, key = self._parse_s3_path(path)

        if mode in {"rb", "r"}:
            # Use S3Hook to read the object
            content = self.hook.read_key(key=key, bucket_name=bucket)
            byte_stream = BytesIO(
                content.encode("utf-8") if isinstance(content, str) else content
            )
            if mode == "rb":
                yield byte_stream
            else:
                yield TextIOWrapper(byte_stream, encoding=encoding)
        elif mode in {"wb", "w"}:
            buffer = BytesIO() if mode == "wb" else StringIO()
            try:
                yield buffer
            finally:
                buffer.seek(0)
                if mode == "w":
                    # encode string data
                    string_data = buffer.getvalue()
                    data: bytes | str = (
                        string_data.encode(encoding)
                        if isinstance(string_data, str)
                        else string_data
                    )
                else:
                    data = buffer.getvalue()
                self.put(data, path)
        else:
            raise NotImplementedError(f"Unsupported mode: {mode}")

    def cat(
        self, path: str, as_bytes: bool = False, encoding: str = "utf-8"
    ) -> str | bytes:
        """
        Read and return the complete content of an S3 file.

        This method provides a simple interface for reading file content from S3,
        with options for returning either raw bytes or decoded string content.

        Parameters
        ----------
        path : str
            S3 path to the file (e.g., "s3://bucket/key" or "s3a://bucket/key")
        as_bytes : bool, default=False
            If True, return content as bytes. If False, decode to string using
            the specified encoding.
        encoding : str, default="utf-8"
            Character encoding to use when decoding bytes to string (only used
            when as_bytes=False)

        Returns
        -------
        Union[bytes, str]
            File content as bytes (if as_bytes=True) or as decoded string
            (if as_bytes=False)

        Raises
        ------
        FileNotFoundError
            If the specified S3 object does not exist
        PermissionError
            If access to the S3 object is denied
        UnicodeDecodeError
            If decoding fails when as_bytes=False

        Examples
        --------
        Read file as bytes::

            fs = AetionS3FileSystem()
            data = fs.cat("s3://my-bucket/data.bin", as_bytes=True)

        Read text file as string::

            content = fs.cat("s3://my-bucket/data.txt", as_bytes=False)
            print(content)

        Read with specific encoding::

            content = fs.cat("s3://my-bucket/data.txt", as_bytes=False, encoding="latin-1")

        Notes
        -----
        - For large files, consider using the `open()` method with streaming
        - The entire file content is loaded into memory
        - S3Hook.read_key() returns string by default, which is handled appropriately
        """
        bucket, key, _ = self.split_path(path)

        content = self.hook.get_key(key=key, bucket_name=bucket).get()["Body"].read()
        if as_bytes:
            return content
        elif encoding:
            return content.decode(encoding)
        else:
            raise ValueError("encoding must be specified when as_bytes=False")

    def put(self, data: Union[bytes, str, BytesIO], path: str) -> None:
        """
        Write data to an S3 object.

        This method provides a flexible interface for uploading various types of data
        to S3, including raw bytes, strings, file-like objects, and local file paths.

        Parameters
        ----------
        data : Union[bytes, str, BytesIO, str]
            Data to upload. Can be:
            - bytes: Raw binary data
            - str: String data (will be UTF-8 encoded) or local file path
            - BytesIO: File-like object containing binary data
        path : str
            S3 destination path (e.g., "s3://bucket/key")

        Raises
        ------
        FileNotFoundError
            If data is a file path and the local file does not exist
        PermissionError
            If access to write to the S3 location is denied
        ValueError
            If the S3 path format is invalid

        Examples
        --------
        Upload bytes::

            fs = AetionS3FileSystem()
            fs.put(b"Hello World", "s3://my-bucket/greeting.txt")

        Upload string::

            fs.put("Hello World", "s3://my-bucket/greeting.txt")

        Upload from BytesIO::

            from io import BytesIO
            buffer = BytesIO(b"Binary data")
            fs.put(buffer, "s3://my-bucket/data.bin")

        Upload local file::

            fs.put("/path/to/local/file.txt", "s3://my-bucket/remote.txt")

        Notes
        -----
        - String data is automatically encoded as UTF-8
        - File paths are detected and handled automatically
        - BytesIO objects are read completely into memory
        - The operation will overwrite existing S3 objects
        """
        bucket, key = self._parse_s3_path(path)

        if isinstance(data, str) and os.path.isfile(data):
            # It's a local file path - use S3Hook's load_file method
            self.hook.load_file(
                filename=data, key=key, bucket_name=bucket, replace=True
            )
        elif isinstance(data, bytes):
            # Use S3Hook's load_bytes method
            self.hook.load_bytes(
                bytes_data=data, key=key, bucket_name=bucket, replace=True
            )
        elif isinstance(data, BytesIO):
            # Use S3Hook's load_file_obj method
            data.seek(0)
            self.hook.load_file_obj(
                file_obj=data, key=key, bucket_name=bucket, replace=True
            )
        else:
            raise TypeError("`data` must be a file path (str), bytes, or BytesIO")

    def rm(self, path: str, recursive: bool = False) -> None:
        """
        Delete objects at the specified S3 path.

        Args:
            path: S3 path to delete (e.g., s3://bucket/prefix/ or s3://bucket/file.txt)
            recursive: If True, delete all objects under the path (folder and nested contents).
                      If False, delete only the exact object at the path.
        """
        bucket, prefix = self._parse_s3_path(path)

        if recursive:
            # Delete all objects under the prefix (folder and nested contents)
            keys_to_delete = self.hook.list_keys(bucket_name=bucket, prefix=prefix)

            if not keys_to_delete:
                logger.info(f"No objects found to delete under {path}")
                return

            # Use S3Hook's delete_objects method for batch deletion
            try:
                self.hook.delete_objects(bucket=bucket, keys=keys_to_delete)
                logger.info(
                    f"Successfully deleted {len(keys_to_delete)} objects recursively under {path}"
                )
            except Exception as e:
                logger.error(f"Failed to delete objects recursively under {path}: {e}")
                raise
        else:
            # Delete only the exact object at the path
            try:
                # Check if the object exists first
                if not self.hook.check_for_key(key=prefix, bucket_name=bucket):
                    logger.info(f"Object {path} does not exist, nothing to delete")
                    return

                # Delete the single object
                self.hook.delete_objects(bucket=bucket, keys=[prefix])
                logger.info(f"Successfully deleted object {path}")
            except Exception as e:
                logger.error(f"Failed to delete object {path}: {e}")
                raise

    def info(self, path: str) -> dict[str, Any]:
        """Get object metadata information."""
        bucket, key = self._parse_s3_path(path)
        response = self.hook.head_object(key=key, bucket_name=bucket)

        if response is None:
            raise FileNotFoundError(f"Object not found: {path}")

        return {
            "name": path,
            "size": response["ContentLength"],
            "type": "file",
            "last_modified": response["LastModified"],
        }

    def upload_dir_parallel(
        self, local_dir: str, s3_prefix: str, max_workers: int = 16
    ) -> None:
        """
        Upload all files in a local directory to S3 using parallel workers.

        This method provides high-performance directory uploads by utilizing multiple
        threads to upload files concurrently. It's optimized for scenarios where
        you need to upload many files efficiently.

        Parameters
        ----------
        local_dir : str
            Path to the local directory containing files to upload
        s3_prefix : str
            S3 destination prefix (e.g., "s3://bucket/prefix/")
        max_workers : int, default=16
            Maximum number of concurrent upload threads. Adjust based on your
            network bandwidth and S3 rate limits.

        Raises
        ------
        FileNotFoundError
            If the local directory does not exist
        PermissionError
            If access to read local files or write to S3 is denied
        ValueError
            If the S3 prefix format is invalid

        Examples
        --------
        Upload directory with default parallelism::

            fs = AetionS3FileSystem()
            fs.upload_dir_parallel(
                local_dir="/tmp/data",
                s3_prefix="s3://my-bucket/uploads/"
            )

        Upload with custom parallelism::

            fs.upload_dir_parallel(
                local_dir="/large/dataset",
                s3_prefix="s3://my-bucket/data/",
                max_workers=8  # Reduce for limited bandwidth
            )

        Notes
        -----
        - Only files with extensions (containing '.') are uploaded
        - Directory structure is flattened - all files go to the same S3 prefix
        - Failed uploads are logged but don't stop other uploads
        - Consider your network bandwidth when setting max_workers
        - S3 rate limits may affect performance with high parallelism

        Performance Tips
        ----------------
        - Use 8-16 workers for typical scenarios
        - Reduce workers if you encounter rate limiting
        - Monitor CloudWatch metrics for S3 request rates
        """
        bucket, prefix = self._parse_s3_path(s3_prefix)
        local_files = glob.glob(os.path.join(local_dir, "*.*"))

        def upload(file_path: str):
            s3_key = os.path.join(prefix, os.path.basename(file_path))
            # Use S3Hook's load_file method directly
            self.hook.load_file(
                filename=file_path, key=s3_key, bucket_name=bucket, replace=True
            )

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(upload, f): f for f in local_files}
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"Failed to upload {futures[future]}: {e}")

    def upload(self, local_path: str, s3_path: str) -> None:
        """
        Upload a local file or directory to S3.

        Args:
            local_path: Path to the local file or directory to upload
            s3_path: S3 destination path (e.g., s3://bucket/key)
        """
        bucket, key = self._parse_s3_path(s3_path)

        if os.path.isfile(local_path):
            # Upload single file using S3Hook's load_file method
            self.hook.load_file(
                filename=local_path, key=key, bucket_name=bucket, replace=True
            )
            logger.info(f"Successfully uploaded file {local_path} to {s3_path}")
        elif os.path.isdir(local_path):
            # Upload directory by uploading all files in it
            for root, dirs, files in os.walk(local_path):
                for file in files:
                    local_file_path = os.path.join(root, file)
                    # Calculate relative path from local_path to maintain directory structure
                    relative_path = os.path.relpath(local_file_path, local_path)
                    s3_key = os.path.join(key, relative_path).replace(os.sep, "/")

                    self.hook.load_file(
                        filename=local_file_path,
                        key=s3_key,
                        bucket_name=bucket,
                        replace=True,
                    )

            logger.info(f"Successfully uploaded directory {local_path} to {s3_path}")
        else:
            raise FileNotFoundError(
                f"Local path {local_path} does not exist or is not a file/directory"
            )

    def exists(self, path: str) -> bool:
        """Check if an S3 object exists."""
        bucket, key = self._parse_s3_path(path)
        return self.hook.check_for_key(key=key, bucket_name=bucket)

    def exists_folder(self, path: str) -> bool:
        """Check if an S3 folder exists."""
        bucket, key = self._parse_s3_path(path)
        prefix_exists = self.hook.check_for_prefix(
            prefix=key, bucket_name=bucket, delimiter="/"
        )
        if not prefix_exists:
            return False
        # Check the prefix is not a file
        is_file = self.hook.check_for_key(key=key, bucket_name=bucket)
        return not is_file

    def split_path(self, url: str) -> Tuple[str, str, Optional[str]]:
        """
        Splits an S3 URL into (bucket, key, version_id) tuple.

        Args:
            url: An S3 URI like 's3://my-bucket/path/to/file.csv'

        Returns:
            tuple: (bucket, key, version_id)

        Notes:
            - The version_id is optional and will be None if not specified in the URL
            - The key does not include the leading slash
            - Both 's3://' and 's3a://' schemes are supported
        """

        is_s3_url = any(url.startswith(f"{scheme}://") for scheme in ["s3", "s3a"])
        if not is_s3_url:
            raise ValueError(f"Expected S3 URL starting with 's3:// or s3a://', got: {url}")

        parsed = urlparse(url)
        bucket = parsed.netloc
        key = parsed.path.lstrip("/")

        # Optionally extract versionId if present (s3://bucket/key?versionId=...)
        query = parsed.query
        version_id = None
        if query:
            match = re.search(r"versionId=([^&]+)", query)
            if match:
                version_id = match.group(1)

        return bucket, key, version_id

    def walk(self, path: str, maxdepth: int | None = None, **kwargs):
        """
        Walk S3 directory tree, yielding tuples of (dirpath, dirnames, filenames).

        This method provides the same interface and behavior as s3fs.S3FileSystem.walk(),
        yielding directory information in a filesystem-like manner for each level of
        the S3 directory structure.

        Parameters
        ----------
        path : str
            S3 path to start walking from (e.g., "s3://bucket/prefix/")
        maxdepth : int, optional
            Maximum depth to traverse. None means unlimited depth.
            Depth 0 means only the specified directory, depth 1 includes
            immediate subdirectories, etc.
        **kwargs
            Additional keyword arguments (for compatibility with s3fs interface)

        Yields
        ------
        tuple[str, list[str], list[str]]
            For each directory level, yields (dirpath, dirnames, filenames) where:
            - dirpath: Current directory path
            - dirnames: List of subdirectory names in current directory
            - filenames: List of file names in current directory

        Raises
        ------
        FileNotFoundError
            If the specified path does not exist
        ValueError
            If path format is invalid

        Examples
        --------
        Walk entire directory tree::

            fs = AetionS3FileSystem()
            for dirpath, dirnames, filenames in fs.walk("s3://bucket/data/"):
                print(f"Directory: {dirpath}")
                print(f"Subdirs: {dirnames}")
                print(f"Files: {filenames}")

        Walk with depth limit::

            # Only traverse 2 levels deep
            for dirpath, dirnames, filenames in fs.walk("s3://bucket/", maxdepth=2):
                process_directory(dirpath, dirnames, filenames)

        Notes
        -----
        - Follows the same traversal pattern as os.walk() and s3fs.walk()
        - Directory names are returned without trailing slashes
        - File and directory names are relative to their parent directory
        - Empty directories are included in the traversal
        - Respects maxdepth parameter for controlling traversal depth
        """
        if not path:
            raise ValueError("Path cannot be empty")

        # Normalize path and ensure it ends with /
        path = path.rstrip("/")
        if not path.endswith("/") and not self._is_s3_root(path):
            path += "/"

        bucket, prefix = self._parse_s3_path(path)

        # Check if the starting path exists
        if prefix and not (self.exists(path) or self.exists_folder(path)):
            raise FileNotFoundError(f"Path does not exist: {path}")

        def _walk_recursive(current_path: str, current_depth: int):
            """Recursively walk directory structure."""
            if maxdepth is not None and current_depth > maxdepth:
                return

            try:
                # Get current directory contents
                items = self.ls(current_path, detail=True, recursive=False)

                dirnames = []
                filenames = []

                # Separate directories and files
                for item in items:
                    item_name = item["name"]
                    # Get relative name (basename)
                    if item_name.endswith("/"):
                        # Directory - remove trailing slash for name
                        dirname = os.path.basename(item_name.rstrip("/"))
                        if dirname:  # Skip empty names
                            dirnames.append(dirname)
                    else:
                        # File
                        filename = os.path.basename(item_name)
                        if filename:  # Skip empty names
                            filenames.append(filename)

                # Yield current directory info
                yield (current_path.rstrip("/"), dirnames, filenames)

                # Recursively walk subdirectories if within depth limit
                if maxdepth is None or current_depth < maxdepth:
                    for dirname in dirnames:
                        subdir_path = f"{current_path.rstrip('/')}/{dirname}/"
                        yield from _walk_recursive(subdir_path, current_depth + 1)

            except Exception as e:
                logger.warning(f"Error walking directory {current_path}: {e}")
                # Continue walking other directories
                return

        # Start recursive walk from the specified path
        yield from _walk_recursive(path, 0)

    def _is_s3_root(self, path: str) -> bool:
        """Check if path is an S3 root (bucket only)."""
        try:
            bucket, key = self._parse_s3_path(path)
            return not key or key == ""
        except ValueError:
            return False

    def walk_all(self, path: str) -> Generator[tuple[str, int, datetime.datetime]]:
        """
        Walk all objects under the given S3 path.

        This method recursively traverses all S3 objects under the specified path
        and yields metadata for each object found. Unlike the newer :meth:`walk` method
        which provides directory-like traversal, this method returns a flat list of
        all objects with their metadata.

        Parameters
        ----------
        path : str
            S3 path to start walking from. Supports the following formats:
            - s3://bucket/prefix/
            - s3a://bucket/prefix/
            - bucket/prefix/ (direct format)

        Yields
        ------
        Generator[tuple[str, int, datetime.datetime]]
            For each S3 object found, yields a tuple containing:
            - str: Object key (relative path from bucket root)
            - int: Object size in bytes
            - datetime.datetime: Last modified timestamp

        Raises
        ------
        ValueError
            If the path format is invalid or cannot be parsed
        PermissionError
            If access to the S3 bucket/prefix is denied
        ClientError
            If there are issues communicating with S3

        Examples
        --------
        Basic usage (deprecated)::

            fs = AetionS3FileSystem()
            for key, size, last_modified in fs.walk_all("s3://my-bucket/data/"):
                print(f"File: {key}, Size: {size}, Modified: {last_modified}")

        Notes
        -----
        - Returns all objects under the path in a flat structure
        - Does not provide directory hierarchy information
        - May be memory-intensive for paths with many objects
        - The returned keys are relative to the bucket root, not the specified prefix

        Performance Considerations
        --------------------------
        - Processes all objects under the path, which can be slow for large datasets
        - No built-in pagination or batching
        - Consider using :meth:`ls` with recursive=True for similar functionality
          with better control over the listing process

        See Also
        --------
        walk : Preferred method for directory-like traversal
        ls : List objects with optional recursion and detail
        """

        bucket, prefix = self._parse_s3_path(path)

        # Use S3Hook's get_file_metadata to get object information
        metadata_list = self.hook.get_file_metadata(prefix=prefix, bucket_name=bucket)

        for metadata in metadata_list:
            yield metadata["Key"], metadata["Size"], metadata["LastModified"]

    def listdir(
        self, path: str, detail: bool = False
    ) -> list[Union[str, dict[str, Any]]]:
        """List directory contents in S3."""
        bucket, prefix = self._parse_s3_path(path)
        if prefix and not prefix.endswith("/"):
            prefix += "/"

        results: list[Union[str, dict[str, Any]]] = []
        # Use S3Hook to list keys with delimiter to get directory-like behavior
        keys = self.hook.list_keys(bucket_name=bucket, prefix=prefix, delimiter="/")

        for key in keys:
            if key == prefix:
                continue
            if detail:
                try:
                    metadata = self.hook.head_object(key=key, bucket_name=bucket)
                    if metadata:
                        results.append(
                            {
                                "Key": key,
                                "Size": metadata["ContentLength"],
                                "LastModified": metadata["LastModified"],
                            }
                        )
                except Exception:
                    continue
            else:
                results.append(f"s3://{bucket}/{key}")

        return results

    def ls(self, path: str, detail: bool = True, recursive: bool = False):
        """List objects in S3 bucket under the given path."""
        bucket, prefix = self._parse_s3_path(path)
        if prefix and not prefix.endswith("/"):
            prefix += "/"

        items = []

        if recursive:
            # For recursive listing, use list_keys
            keys = self.hook.list_keys(bucket_name=bucket, prefix=prefix)
            for key in keys:
                if key == prefix:
                    continue
                # Get metadata for each key
                try:
                    metadata = self.hook.head_object(key=key, bucket_name=bucket)
                    if metadata:
                        item = {
                            "name": f"s3://{bucket}/{key}",
                            "size": metadata["ContentLength"],
                            "type": "file",
                            "last_modified": metadata["LastModified"],
                        }
                        items.append(item)
                except Exception:
                    # Skip objects we can't access
                    continue
        else:
            # For non-recursive, use list_prefixes to get directories
            prefixes = self.hook.list_prefixes(
                bucket_name=bucket, prefix=prefix, delimiter="/"
            )
            for pref in prefixes:
                items.append(
                    {
                        "name": f"s3://{bucket}/{pref}",
                        "size": 0,
                        "type": "directory",
                        "last_modified": None,
                    }
                )

            # Also get files in current directory
            keys = self.hook.list_keys(bucket_name=bucket, prefix=prefix, delimiter="/")
            for key in keys:
                if key == prefix or key.endswith("/"):
                    continue
                try:
                    metadata = self.hook.head_object(key=key, bucket_name=bucket)
                    if metadata:
                        item = {
                            "name": f"s3://{bucket}/{key}",
                            "size": metadata["ContentLength"],
                            "type": "file",
                            "last_modified": metadata["LastModified"],
                        }
                        items.append(item)
                except Exception:
                    continue

        return items if detail else [i["name"] for i in items]

    def get(self, rpath: str, lpath: str, recursive: bool = False) -> None:
        """
        Download files from S3 to local filesystem.

        This method provides a simple interface for downloading S3 objects to the local
        filesystem, with support for both single file downloads and recursive directory
        downloads. It handles path resolution and directory creation automatically.

        Parameters
        ----------
        rpath : str
            S3 source path to download from. Supports the following formats:
            - s3://bucket/key/path (single file or directory prefix)
            - s3a://bucket/key/path (alternative S3 format)
            - bucket/key/path (direct format)
        lpath : str
            Local filesystem destination path. Can be:
            - File path for single file downloads
            - Directory path (files will be placed inside with original names)
            - Target file path with custom name
        recursive : bool, default=False
            If True, download all objects under the S3 prefix recursively,
            maintaining directory structure. If False, download only the
            specified object.

        Raises
        ------
        FileNotFoundError
            If the specified S3 object or prefix does not exist
        PermissionError
            If access to S3 or local filesystem is denied
        OSError
            If local directory creation fails or filesystem errors occur
        ValueError
            If the S3 path format is invalid or cannot be parsed

        Examples
        --------
        Download single file to specific path::

            fs = AetionS3FileSystem()
            fs.get("s3://my-bucket/data.csv", "/local/downloads/data.csv")

        Download single file to directory (preserves original name)::

            fs.get("s3://my-bucket/reports/summary.txt", "/local/downloads/")
            # Creates: /local/downloads/summary.txt

        Download entire directory recursively::

            fs.get(
                "s3://my-bucket/dataset/",
                "/local/data/",
                recursive=True
            )
            # Downloads all files under dataset/ maintaining structure

        Download with custom local name::

            fs.get("s3://my-bucket/old-name.csv", "/local/new-name.csv")

        Notes
        -----
        - Uses Airflow's S3Hook.download_file() method with preserve_file_name=True
        - For single file downloads to directories, the original S3 filename is preserved
        - For recursive downloads, the complete directory structure is maintained locally
        - Local directories are created automatically as needed
        - If the downloaded file name differs from the target path, it's automatically renamed
        - The method handles both file and directory targets intelligently
        - Progress is logged for each file download operation

        Performance Considerations
        --------------------------
        - Recursive downloads process files sequentially (not parallelized)
        - For high-performance bulk downloads, consider using download_files_parallel_sync()
        - Large files may take significant time depending on network bandwidth
        - Ensure sufficient local disk space before starting downloads

        See Also
        --------
        download_files_parallel_sync : Parallel download of multiple specific files
        cat : Read file content directly into memory
        upload : Upload files from local filesystem to S3
        """
        bucket, prefix = self._parse_s3_path(rpath)

        self.hook.log.info(f"Downloading files from S3 URL ({rpath}) to local path ({lpath}) (recursive={recursive})")

        def _download(s3_key: str, local_file: str):
            os.makedirs(os.path.dirname(local_file), exist_ok=True)

            # Use S3Hook's download_file method
            downloaded_file = self.hook.download_file(
                key=s3_key,
                bucket_name=bucket,
                local_path=os.path.dirname(local_file),
                preserve_file_name=True,
                use_autogenerated_subdir=False,
            )
            self.hook.log.info(f"Downloaded {s3_key} (bucket={bucket}) to {downloaded_file}")

            # Move to the exact target path if needed
            if downloaded_file != local_file:
                os.rename(downloaded_file, local_file)

        if not recursive:
            if os.path.isdir(lpath):
                lpath = os.path.join(lpath, os.path.basename(prefix))
            _download(prefix, lpath)
        else:
            # Get all keys under the prefix
            keys = self.hook.list_keys(bucket_name=bucket, prefix=prefix)
            for key in keys:
                rel_path = os.path.relpath(key, prefix)
                local_file = os.path.join(lpath, rel_path)
                _download(key, local_file)

    def copy_files_parallel_sync(
        self,
        source_path: str,
        target_path: str,
        files: list[str],
        copy_parallelism: int = 8,
    ) -> None:
        """
        Copy specific files from source to target S3 location using parallel workers.

        This method performs efficient parallel copying of specified files between S3
        locations. It's designed for scenarios where you need to copy a subset of files
        from a larger dataset with high performance.

        Parameters
        ----------
        source_path : str
            Source S3 prefix (e.g., "s3://source-bucket/data/")
        target_path : str
            Target S3 prefix (e.g., "s3://target-bucket/backup/")
        files : list[str]
            List of relative file paths to copy (relative to source_path)
        copy_parallelism : int, default=8
            Maximum number of concurrent copy operations

        Raises
        ------
        FileNotFoundError
            If source files do not exist
        PermissionError
            If access to source or target locations is denied
        ValueError
            If S3 path formats are invalid

        Examples
        --------
        Copy specific files::

            fs = AetionS3FileSystem()
            files_to_copy = ["data1.csv", "data2.csv", "reports/summary.txt"]
            fs.copy_files_parallel_sync(
                source_path="s3://source-bucket/dataset/",
                target_path="s3://backup-bucket/archive/",
                files=files_to_copy,
                copy_parallelism=4
            )

        Copy with high parallelism::

            fs.copy_files_parallel_sync(
                source_path="s3://src/large-dataset/",
                target_path="s3://dst/backup/",
                files=file_list,
                copy_parallelism=16
            )

        Notes
        -----
        - Files are copied using read/write operations (not S3 copy API)
        - Only files that exist in the source and match the files list are copied
        - Failed copies will raise exceptions and stop the operation
        - Progress is logged for each file copy operation
        - Consider S3 transfer acceleration for cross-region copies

        Performance Considerations
        --------------------------
        - Optimal parallelism depends on file sizes and network bandwidth
        - Large files may benefit from lower parallelism to avoid timeouts
        - Monitor S3 request rates to avoid throttling
        - Consider using S3 batch operations for very large file sets
        """
        logger.info(
            f"Starting parallel copy of {len(files)} files from {source_path} to {target_path} "
            f"with {copy_parallelism} threads"
        )

        src_bucket, src_prefix, _ = self.split_path(source_path)
        dst_bucket, dst_prefix, _ = self.split_path(target_path)

        def _copy_file(key: str):
            src_key = os.path.join(src_prefix, key)
            dst_key = os.path.join(dst_prefix, key)
            source_s3_path = f"s3://{src_bucket}/{src_key}"
            dest_s3_path = f"s3://{dst_bucket}/{dst_key}"

            try:
                logger.info(f"Copying: {src_key} → {dst_key}")
                # Read from source and write to destination using S3Hook
                content = self.cat(source_s3_path, as_bytes=True)
                self.put(content, dest_s3_path)
                logger.info(f"Copied: {src_key} → {dst_key}")
            except Exception as e:
                logger.error(f"Failed to copy {src_key} → {dst_key}: {e}")
                raise

        with ThreadPoolExecutor(max_workers=copy_parallelism) as executor:
            futures = {
                executor.submit(_copy_file, key): key
                for key, _, _ in self.walk_all(source_path)
                if key in files
            }

            for future in as_completed(futures):
                key = futures[future]
                try:
                    future.result()
                except Exception as e:
                    logger.exception(f"Error copying {key}: {e}")
                    raise  # Let it fail fast for now. Retry logic could be added.

        logger.info("All files copied successfully.")

    def download_files_parallel_sync(
        self,
        source_path: str,
        target_path: str,
        files: list[str],
        copy_parallelism: int,
    ) -> None:
        """
        Download multiple files from S3 to a local directory using parallel workers.

        This method provides high-performance downloads by utilizing multiple threads
        to download files concurrently from S3 to the local filesystem.

        Parameters
        ----------
        source_path : str
            S3 source prefix (e.g., "s3://bucket/data/")
        target_path : str
            Local directory path where files will be downloaded
        files : list[str]
            List of relative file paths to download (relative to source_path)
        copy_parallelism : int
            Maximum number of concurrent download threads

        Raises
        ------
        FileNotFoundError
            If source files do not exist in S3
        PermissionError
            If access to S3 or local filesystem is denied
        OSError
            If local directory creation fails

        Examples
        --------
        Download specific files::

            fs = AetionS3FileSystem()
            files_to_download = ["data1.csv", "data2.csv", "reports/summary.txt"]
            fs.download_files_parallel_sync(
                source_path="s3://my-bucket/dataset/",
                target_path="/local/downloads/",
                files=files_to_download,
                copy_parallelism=8
            )

        Download with custom parallelism::

            fs.download_files_parallel_sync(
                source_path="s3://bucket/large-files/",
                target_path="/tmp/data/",
                files=large_file_list,
                copy_parallelism=4  # Lower for large files
            )

        Notes
        -----
        - Local directories are created automatically if they don't exist
        - Files maintain their relative path structure in the target directory
        - Only files that exist in S3 and match the files list are downloaded
        - Failed downloads will raise exceptions and stop the operation
        - Progress is logged for each file download

        Performance Tips
        ----------------
        - Use 4-8 workers for large files, 8-16 for small files
        - Ensure sufficient local disk space before starting
        - Monitor network bandwidth utilization
        - Consider download location (same region as S3 bucket for best performance)
        """
        logger.info(f"Downloading files {files} from {source_path} to {target_path}")

        src_bucket, source_prefix, _ = self.split_path(source_path)

        def download_file(rel_key: str):
            try:
                source_key = os.path.join(source_prefix, rel_key)
                source_s3_path = f"s3://{src_bucket}/{source_key}"
                dest_file = os.path.join(target_path, rel_key)

                logger.info(f"Downloading {source_s3_path} → {dest_file}")

                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(dest_file), exist_ok=True)

                # Download using S3Hook
                content = self.cat(source_s3_path, as_bytes=True)
                with open(dest_file, "wb") as f:
                    if isinstance(content, str):
                        f.write(content.encode("utf-8"))
                    else:
                        f.write(content)
            except Exception as e:
                logger.error(f"Download failed: {e}")
                raise

        futures = []
        with ThreadPoolExecutor(max_workers=copy_parallelism) as executor:
            for key, _, _ in self.walk_all(source_path):
                rel_key = resolve_relative_key(key, source_prefix)
                if rel_key in files:
                    futures.append(executor.submit(download_file, rel_key))
                else:
                    logger.info(f"Skipping {key}")

        for future in futures:
            future.result()

    def copy_with_pattern_parallel(
        self, source_path: str, target_path: str, pattern: str, copy_parallelism: int
    ) -> None:
        """
        Copy files matching pattern in parallel using S3Hook.

        This method recursively traverses the source path, identifies files matching
        the specified pattern, and copies them to the target path while preserving
        the relative directory structure.

        Parameters
        ----------
        source_path : str
            Source S3 path (e.g., "s3://source-bucket/prefix/")
        target_path : str
            Target S3 path (e.g., "s3://target-bucket/prefix/")
        pattern : str
            Regular expression pattern to match against relative file paths
            (e.g., "\.csv$" to match only CSV files)
        copy_parallelism : int
            Maximum number of concurrent copy operations

        Notes
        -----
        - Preserves directory structure relative to source_path
        - Correctly handles nested paths and maintains hierarchy
        - Uses relative path computation to avoid path duplication issues

        Examples
        --------
        Copy all files::

            fs.copy_with_pattern_parallel(
                "s3://adipalbert.app.dev.aetion.com/etl/synpuf_subset/20211018/full-shard/dict/",
                "s3://adipalbert.app.dev.aetion.com/etl/synpuf_subset_filtered/20221004/full-shard/dict/",
                ".*",
                5
            )

        Copy only parquet files::

            fs.copy_with_pattern_parallel(
                "s3://adipalbert.app.dev.aetion.com/etl/synpuf_subset/20211018/full-shard/",
                "s3://adipalbert.app.dev.aetion.com/etl/synpuf_subset_filtered/20221004/full-shard/",
                "\.parquet$",
                10
            )
        """
        logger.info(f"copying {source_path} to {target_path} with pattern {pattern}")

        src_bucket, source_prefix, _ = self.split_path(source_path)
        dest_bucket, dest_prefix, _ = self.split_path(target_path)

        def copy_file(full_key: str):
            # walk_all returns full S3 keys from bucket root, so we need to compute relative key
            rel_key = resolve_relative_key(full_key, source_prefix)

            # Construct destination key by joining dest_prefix with relative key
            dest_key = os.path.join(dest_prefix, rel_key) if rel_key else dest_prefix

            source_s3_path = f"s3://{src_bucket}/{full_key}"
            dest_s3_path = f"s3://{dest_bucket}/{dest_key}"

            logger.info(f"copy {source_s3_path} to {dest_s3_path}")

            # Read from source and write to destination
            content = self.cat(source_s3_path, as_bytes=True)
            self.put(content, dest_s3_path)

        futures = []
        with ThreadPoolExecutor(max_workers=copy_parallelism) as executor:
            for full_key, _, _ in self.walk_all(source_path):
                # Apply pattern matching to the relative key for consistency
                rel_key = resolve_relative_key(full_key, source_prefix)

                if not pattern or re.search(pattern, rel_key):
                    futures.append(executor.submit(copy_file, full_key))
                else:
                    logger.info(f"skipping {source_path}/{rel_key}")

        # propagate any exception
        for future in futures:
            future.result()

    def copy_files_parallel(
        self,
        source_path: str,
        target_path: str,
        files: list[str],
        copy_parallelism: int,
    ) -> None:
        """Copy specific files in parallel using S3Hook."""
        logger.info(f"copying files {files} from {source_path} to {target_path}")

        src_bucket, source_prefix, _ = self.split_path(source_path)
        dest_bucket, dest_prefix, _ = self.split_path(target_path)

        def copy_file(full_key: str):
            # walk_all returns full S3 keys from bucket root, so we need to compute relative key
            rel_key = resolve_relative_key(full_key, source_prefix)

            # Construct destination key by joining dest_prefix with relative key
            dest_key = os.path.join(dest_prefix, rel_key) if rel_key else dest_prefix

            source_s3_path = f"s3://{src_bucket}/{full_key}"
            dest_s3_path = f"s3://{dest_bucket}/{dest_key}"

            logger.info(f"copy {source_s3_path} to {dest_s3_path}")

            # Read from source and write to destination
            content = self.cat(source_s3_path, as_bytes=True)
            self.put(content, dest_s3_path)

        futures = []
        with ThreadPoolExecutor(max_workers=copy_parallelism) as executor:
            for full_key, _, _ in self.walk_all(source_path):
                # Compute relative key for file matching
                rel_key = resolve_relative_key(full_key, source_prefix)

                if rel_key in files:
                    futures.append(executor.submit(copy_file, full_key))
                else:
                    logger.info(f"skipping {source_path}/{rel_key}")

        # propagate any exception
        for future in futures:
            future.result()

    def download_files_parallel(
        self,
        source_path: str,
        target_path: str,
        files: list[str],
        copy_parallelism: int,
    ) -> None:
        """
        Download specific files from S3 to local filesystem in parallel using ThreadPoolExecutor.

        This method efficiently downloads a subset of files from an S3 location to a local
        directory using parallel processing. It only downloads files that are explicitly
        specified in the files parameter, making it ideal for selective file retrieval
        from large S3 directories. The method automatically creates local directory
        structures as needed and preserves the relative path structure from S3.

        Args:
            source_path: S3 URI in the format 's3://bucket-name/prefix/path'. The prefix
                can be empty (s3://bucket-name/) or contain multiple path segments
                (s3://bucket-name/folder/subfolder/). All files in the files parameter
                are resolved relative to this path.
            target_path: Local filesystem path where files will be downloaded. Can be
                an absolute path (/home/<USER>/data) or relative path (./downloads).
                Directory structure will be created automatically if it doesn't exist.
            files: List of relative file paths to download from the source_path. Each
                path should be relative to the source_path prefix (e.g., ['file1.txt',
                'subfolder/file2.csv', 'data/results.json']). Only files in this list
                will be downloaded; all other files in the S3 location are ignored.
            copy_parallelism: Maximum number of concurrent download threads to use.
                Higher values increase download speed but consume more system resources.
                Typical values range from 4-16 depending on network bandwidth and
                system capabilities.

        Raises:
            Exception: Propagates any exception that occurs during file download,
                including S3 access errors, network timeouts, or local filesystem
                write errors. If any download fails, the exception is raised after
                all other downloads complete.

        Examples:
            Basic file download:
                >>> s3_support = S3Support()
                >>> s3_support.download_files_parallel(
                ...     source_path="s3://my-bucket/data/exports",
                ...     target_path="/local/downloads",
                ...     files=["report.csv", "summary.json"],
                ...     copy_parallelism=8
                ... )
                # Downloads:
                # s3://my-bucket/data/exports/report.csv -> /local/downloads/report.csv
                # s3://my-bucket/data/exports/summary.json -> /local/downloads/summary.json

            Downloading files with nested directory structure:
                >>> s3_support.download_files_parallel(
                ...     source_path="s3://analytics-bucket/processed",
                ...     target_path="./local_data",
                ...     files=[
                ...         "2023/01/sales.csv",
                ...         "2023/01/inventory.csv",
                ...         "2023/02/sales.csv"
                ...     ],
                ...     copy_parallelism=4
                ... )
                # Creates local directory structure and downloads:
                # s3://analytics-bucket/processed/2023/01/sales.csv -> ./local_data/2023/01/sales.csv
                # s3://analytics-bucket/processed/2023/01/inventory.csv -> ./local_data/2023/01/inventory.csv
                # s3://analytics-bucket/processed/2023/02/sales.csv -> ./local_data/2023/02/sales.csv

            Parallel processing demonstration:
                >>> # Downloads 100 files using 10 concurrent threads
                >>> file_list = [f"batch_{i:03d}/data.parquet" for i in range(100)]
                >>> s3_support.download_files_parallel(
                ...     source_path="s3://large-dataset/partitioned",
                ...     target_path="/fast-storage/dataset",
                ...     files=file_list,
                ...     copy_parallelism=10
                ... )

        Note:
            - The method uses ThreadPoolExecutor for parallel downloads, with the number
              of worker threads controlled by copy_parallelism
            - Local directories are created automatically using os.makedirs(exist_ok=True)
            - Files not listed in the files parameter are skipped and logged
            - The method walks through all files in the source S3 location but only
              downloads those specified in the files list
            - Binary and text files are handled automatically based on content type
            - All download exceptions are propagated after parallel execution completes
        """
        logger.info(f"downloading files {files} from {source_path} to {target_path}")

        src_bucket, source_prefix, _ = self.split_path(source_path)

        def download_file(rel_key: str):
            try:
                source_s3_path = f"s3://{src_bucket}/{rel_key}"
                target_rel_file_path = os.path.relpath(rel_key, source_prefix)
                dest_file = os.path.join(target_path, target_rel_file_path)

                logger.info(f"download {source_s3_path} to {dest_file}")

                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(dest_file), exist_ok=True)

                # Download using S3Hook
                self.get(source_s3_path, dest_file)
                # content = self.cat(source_s3_path, as_bytes=True)
                # with open(dest_file, "wb") as f:
                #     if isinstance(content, str):
                #         f.write(content.encode("utf-8"))
                #     else:
                #         f.write(content)
            except Exception as e:
                logger.error(f"Failed to download: {str(e)}")
                raise

        futures = []
        with ThreadPoolExecutor(max_workers=copy_parallelism) as executor:
            file_key_prefixes = [ os.path.join(source_prefix, file) for file in files ]
            for rel_key, _, _ in self.walk_all(source_path):
                if rel_key in file_key_prefixes:
                    futures.append(executor.submit(download_file, rel_key))
                else:
                    logger.info(f"skipping {source_path}/{rel_key}")

        # propagate any exception
        for future in futures:
            future.result()

    def copy_basic_directory(self, source_path: str, target_path: str) -> None:
        """Copy basic directory from source to target using S3Hook."""

        logger.info(f"source_path: {source_path}")
        logger.info(f"target_path: {target_path}")

        if source_path == target_path:
            logger.info(f"source_path and target_path are the same: {source_path}")
            return

        if not self.exists_folder(source_path):
            logger.info(f"source_path does not exist: {source_path}")
            raise FileNotFoundError(f"source_path does not exist: {source_path}")

        # List objects in source path
        objects = self.ls(source_path, detail=False, recursive=True)
        for source_key in objects:
            if source_key.endswith("/"):
                logger.info(f"skipping directory: {source_key}")
                continue

            logger.info(f"source_key: {source_key}")
            target_key = os.path.join(target_path, os.path.basename(source_key))
            logger.info(f"copying {source_key} to {target_key}")
            self.copy_basic(source_key, target_key)

    def copy_basic(self, src: str, dst: str) -> None:
        """Copy basic file from source to target using S3Hook.

        Args:
            src (str): s3 path to source file
            dst (str): s3 path to target file
        """

        logger.info(f"copying {src} to {dst}")

        # Read from source and write to target
        src_bucket, src_key = self._parse_s3_path(src)
        obj = self.hook.get_key(src_key, src_bucket)
        self.put(obj.get()["Body"].read(), dst)

    def read_file_content(self, file_path: str) -> str:
        """
        Read file content from local file or S3 using S3Hook.

        Args:
            file_path: File path - if starts with 'file:', uses local filesystem,
                      otherwise uses S3Hook

        Returns:
            File content as string
        """
        from urllib.parse import urlsplit

        logger.info(f"reading {file_path}")

        scheme, _, local_file_path, _, _ = urlsplit(file_path)
        if scheme == "file":  # workaround to enable testing without S3
            with open(local_file_path, "r") as f:
                return f.read()
        else:
            content = self.cat(file_path, as_bytes=False)
            return str(content)

    def check_files_ready(
        self,
        source_path: str,
        tables_count: int,
        default_partitions_count: int,
        partitions_counts: dict,
    ) -> bool:
        """Check if files are ready using S3Hook."""
        logger.info(
            f"checking {source_path} ({tables_count} tables, {default_partitions_count} partitions by default"
            f" {partitions_counts} are expected)"
        )

        if not self.exists(source_path):
            logger.info(f"{source_path} does not exist")
            return False

        # List objects and filter for directories
        objects = self.ls(source_path, detail=True)
        tables = [obj["name"] for obj in objects if obj["type"] == "directory"]

        logger.info(f"{len(tables)} tables found ({tables_count} expected):\n{tables}")
        if len(tables) < tables_count:
            return False

        for table_path in tables:
            logger.info(f"checking {table_path}")
            count = len(self.ls(table_path))
            table_name = os.path.basename(table_path)
            expected_count = partitions_counts.get(table_name, default_partitions_count)
            logger.info(
                f"{count} objects found at {table_name} ({expected_count} expected)"
            )
            if count <= expected_count:
                return False

        return True

    def find_next_revision(self, source_path: str, prev_revision: str) -> str | None:
        """Find next revision using S3Hook."""
        source_path = os.path.normpath(source_path)
        logger.info(
            f"looking for the next revision after {prev_revision} at {source_path}"
        )

        if not self.exists(source_path):
            logger.warning(
                f"{source_path} does not exist (might be permissions related)"
            )
            return None

        # List objects and filter for directories
        objects = self.ls(source_path, detail=True)
        sub_dirs = [
            os.path.relpath(obj["name"], source_path)
            for obj in objects
            if obj["type"] == "directory"
        ]

        logger.info(f"sub directories found at {source_path}\n{sub_dirs}")

        next_revisions = [r for r in sub_dirs if r > prev_revision and r.isnumeric()]
        logger.info(
            f"the revision greater than {prev_revision} found at {source_path}\n{next_revisions}"
        )
        most_recent_revision = max(next_revisions) if next_revisions else None
        logger.info(f"most recent revision is {most_recent_revision}")
        return most_recent_revision


class S3Location(NamedTuple):
    """Named tuple for S3 bucket and key."""

    bucket: str
    key: str


def parse_s3_url(url: str) -> S3Location:
    """
    Parse an S3 or S3A URL into structured bucket and key components.

    This utility function provides a standardized way to parse S3 URLs into
    their constituent parts, supporting both standard S3 and Spark-compatible
    S3A URL schemes.

    Parameters
    ----------
    url : str
        The S3 URL to parse. Must start with 's3://' or 's3a://' and include
        both bucket and key components.

    Returns
    -------
    S3Location
        A named tuple with `bucket` and `key` attributes containing the
        parsed components.

    Raises
    ------
    ValueError
        If the URL does not start with a supported scheme ('s3://' or 's3a://')
        or if the URL does not include both bucket and key components.

    Examples
    --------
    Parse standard S3 URL::

        from aetion.adip.airflow.integrations.s3.s3_support import parse_s3_url

        location = parse_s3_url("s3://my-bucket/path/to/file.csv")
        print(f"Bucket: {location.bucket}")  # Output: my-bucket
        print(f"Key: {location.key}")        # Output: path/to/file.csv

    Parse S3A URL (Spark-compatible)::

        location = parse_s3_url("s3a://data-bucket/datasets/2023/data.parquet")
        print(f"Bucket: {location.bucket}")  # Output: data-bucket
        print(f"Key: {location.key}")        # Output: datasets/2023/data.parquet

    Use in path operations::

        location = parse_s3_url("s3://my-bucket/folder/file.txt")
        # Reconstruct URL
        new_url = f"s3://{location.bucket}/{location.key}"

    Notes
    -----
    - Both 's3://' and 's3a://' schemes are supported for compatibility
    - The function validates that both bucket and key are present
    - Keys preserve their full path structure including subdirectories
    - This function is used internally by AetionS3FileSystem methods
    """
    if url.startswith("s3://"):
        path = url[5:]
    elif url.startswith("s3a://"):
        path = url[6:]
    else:
        raise ValueError(
            f"Invalid S3 URL: {url!r}. Must start with 's3://' or 's3a://'."
        )

    if "/" not in path:
        raise ValueError(f"S3 URL must include both bucket and key: {url!r}")

    bucket, key = path.split("/", 1)
    return S3Location(bucket=bucket, key=key)


def resolve_relative_key(full_key: str, base_prefix: str) -> str:
    """
    Resolve relative key from full key and base prefix.

    Parameters
    ----------
    full_key: str
        Full key including the base prefix
    base_prefix: str
        Base prefix to remove from the full key

    Returns
    -------
    str
        Relative key

    Examples
    --------
    Resolve relative key::

        full_key = "a/b/c/d"
        base_prefix = "a/b"
        relative_key = resolve_relative_key(full_key, base_prefix)
        assert relative_key == "c/d"
    """
    return full_key[len(base_prefix):].lstrip("/") if full_key.startswith(base_prefix) else full_key
