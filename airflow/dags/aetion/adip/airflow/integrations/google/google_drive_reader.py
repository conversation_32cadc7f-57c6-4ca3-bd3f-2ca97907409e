from functools import cached_property
from io import BytesIO
import logging
import os
from pathlib import Path
from typing import List, Optional
from oauth2client.service_account import ServiceAccountCredentials
from google.auth.credentials import AnonymousCredentials
from googleapiclient.errors import HttpError
from googleapiclient.discovery import build

logger = logging.getLogger(__name__)


class GDriveReader(object):

    default_mimetype = (
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
    default_scopes = [
        "https://www.googleapis.com/auth/drive.metadata.readonly",
        "https://www.googleapis.com/auth/drive.readonly",
    ]
    default_gdrive_envvar = "GOOGLE_APPLICATION_CREDENTIALS"

    def __init__(
        self,
        filename: str,
        query: bool = False,
        json_credentials: Optional[str] = None,
        mimetype: str = default_mimetype,
        scopes: List[str] = default_scopes,
    ):
        """
        Read from google drive

        A class that wraps access to files in google drive in the python
        abstraction of an io.BufferedReader, BufferedReaders are file-like
        objects.
        :param file_name: The name of the file in google drive to find
        :type file_name: str
        :param query: Is the filename a query instead of a filename? See
            https://developers.google.com/drive/api/guides/search-files
            for query DSL.
        :type query: bool
        :param json_credentials the credentials to use to authenticate with
            google drive. Details are described at
            TODO: I can't actually find a page that documents the format, I've
            copied what I'm using locally here for now with details redacted.
            This was created by copying the output of
            ServiceAccountCredentials.from_json_keyfile_name(...).to_json()
                {
                    "client_id": "103726823025215315097",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "revoke_uri": "https://oauth2.googleapis.com/revoke",
                    "_service_account_email": "... .iam.gserviceaccount.com",
                    "_scopes": "https://www.googleapis.com/auth/drive.metadata.readonly https://www.googleapis.com/auth/drive.readonly",
                    "_private_key_id": "...",
                    "_private_key_pkcs8_pem": "-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\\n",
                    "_class": "ServiceAccountCredentials",
                    "_module": "oauth2client.service_account"
                }
        :type json_credentials: str
        :param scopes: The scopes the json credentials have, we need at least
            drive.metadata.readonly (to read the names of files) and
            drive.readonly (to read the data of the file)
        :type scopes: List[str]
        :param mimetype: The format we should ask google for the document.
            Changing this changes what type of document we download. See
            https://developers.google.com/drive/api/guides/ref-export-formats
            for valid mime types.
        :type mimetype: str
        """
        self.query = query
        self.filename = filename
        self.mimetype = mimetype
        self._setup_credentials(json_credentials)

    def __enter__(self):

        filename = self.filename
        query = self.query
        mimetype = self.mimetype
        search = filename

        if not query:
            # See https://developers.google.com/drive/api/guides/search-files
            search = f"name contains '{filename}'"

        mapped = (
            (file_item["id"], file_item["name"])
            for file_item in iter_gdrive(self.service, q=search)
        )
        filtered = (
            id for (id, name) in mapped if Path(name).with_suffix("").name == filename
        )
        file_id = next(filtered, None)
        if file_id is None:
            raise FileNotFoundError(
                f"Failed to find file {filename} from the query {search}. "
                f"Enable debug log for a verbose printout of the files we did "
                f"find."
            )
        try:
            data = (
                self.service.files().export(fileId=file_id, mimeType=mimetype).execute()
            )
        except HttpError as e:
            if e.error_details[0]["reason"] == "fileNotExportable":
                data = self.service.files().get_media(fileId=file_id).execute()
            else:
                raise e

        return BytesIO(data)

    def __exit__(self, exc_type, exc_value, traceback):
        pass

    @cached_property
    def service(self):
        return build("drive", "v3", credentials=self.credentials)

    def _setup_credentials(self, json_credentials: str | None):
        self.credentials = None
        if json_credentials is not None:
            self.credentials = ServiceAccountCredentials.from_json(
                json_data=json_credentials,
            )
        elif self.default_gdrive_envvar in os.environ:
            cred_file = os.getenv(self.default_gdrive_envvar)
            self.credentials = ServiceAccountCredentials.from_json_keyfile_name(
                filename=cred_file
            )
        else:
            logging.warn(
                f"{self.__class__} was not passed credentials to log into "
                f"gdrive, and no {self.default_gdrive_envvar} environment variable "
                f"was specified. Using anonymous credentials."
            )
            self.credentials = AnonymousCredentials()


def iter_gdrive(service, q):
    "Generator for the google drive api to make it look more pythonic"
    files_api = service.files()
    list_req = files_api.list(pageSize=10, fields="nextPageToken, files(id, name)", q=q)
    while list_req is not None:
        file_list = list_req.execute()
        for file in file_list["files"]:
            yield file
        list_req = files_api.list_next(list_req, file_list)
