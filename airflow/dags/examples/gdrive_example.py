import json
import logging
import tempfile

from airflow.decorators import dag, task
from airflow.utils.dates import days_ago

from aetion.adip.airflow.integrations.google.google_drive_reader import G<PERSON><PERSON>Reader

# To generate a set of credentials see docs/google_service_account
# TODO: Find a better place to put this
json_credentials = """
{
    "access_token": null,
    "client_id": "103726823025215315097",
    "client_secret": null,
    "refresh_token": null,
    "token_expiry": null,
    "token_uri": "https://oauth2.googleapis.com/token",
    "user_agent": null,
    "revoke_uri": "https://oauth2.googleapis.com/revoke",
    "id_token": null,
    "id_token_jwt": null,
    "token_response": null,
    "scopes": [],
    "token_info_uri": null,
    "invalid": false,
    "assertion_type": null,
    "_service_account_email": "... .iam.gserviceaccount.com",
    "_scopes": "https://www.googleapis.com/auth/drive.metadata.readonly https://www.googleapis.com/auth/drive.readonly",
    "_private_key_id": "3c27d2169033fcc5d6d8133216d9dc08948c616a",
    "_user_agent": null,
    "_kwargs": {},
    "_private_key_pkcs8_pem": "-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\\n",
    "_class": "ServiceAccountCredentials",
    "_module": "oauth2client.service_account"
}
"""
json.loads(json_credentials)


@dag(
    dag_id="gdrive_example",
    start_date=days_ago(1),
    schedule=None,
    catchup=False,
    tags=["aetion"],
)
def gdrive_example_dag():
    pass

    @task
    def start():
        logging.info("Starting GDrive example DAG")

    @task
    def end():
        logging.info("Ending GDrive example DAG")

    @task(task_id="file_copy")
    def download():
        reader = GDriveReader(
            json_credentials=json_credentials,
            filename="Copy of DBC Specification - Sanofi Flatiron Multiple Myeloma - ********",
        )

        with tempfile.NamedTemporaryFile(mode="wb", suffix=".xlsx") as temp_file:
            with reader as gdrive_file:
                temp_file.write(gdrive_file.read())
                temp_file.flush()
                logging.info(f"File copied to S3: {temp_file.name}")

    start() >> download() >> end()  # type: ignore


gdrive_example_dag()
