# STD
from datetime import datetime
import json

# Airflow
from airflow import DAG
from airflow.operators.dummy_operator import DummyOperator
from operators.file_copy_operator import FileCopyOperator, <PERSON><PERSON><PERSON><PERSON>eader

# To generate a set of credentials see docs/google_service_account
# TODO: Find a better place to put this
json_credentials = """
{
    "access_token": null,
    "client_id": "103726823025215315097",
    "client_secret": null,
    "refresh_token": null,
    "token_expiry": null,
    "token_uri": "https://oauth2.googleapis.com/token",
    "user_agent": null,
    "revoke_uri": "https://oauth2.googleapis.com/revoke",
    "id_token": null,
    "id_token_jwt": null,
    "token_response": null,
    "scopes": [],
    "token_info_uri": null,
    "invalid": false,
    "assertion_type": null,
    "_service_account_email": "... .iam.gserviceaccount.com",
    "_scopes": "https://www.googleapis.com/auth/drive.metadata.readonly https://www.googleapis.com/auth/drive.readonly",
    "_private_key_id": "3c27d2169033fcc5d6d8133216d9dc08948c616a",
    "_user_agent": null,
    "_kwargs": {},
    "_private_key_pkcs8_pem": "-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\\n",
    "_class": "ServiceAccountCredentials",
    "_module": "oauth2client.service_account"
}
"""
json.loads(json_credentials)

dag_default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "schedule": None,
    "catchup_by_default": False,
    "retries": 0,
    "start_date": datetime(2022, 6, 1),
}
with DAG(
    dag_id="GDrive-test",
    default_args=dag_default_args,
    catchup=False,
    schedule=None,
) as dag:
    start = DummyOperator(task_id="start", dag=dag)
    end = DummyOperator(task_id="end", dag=dag)
    download = FileCopyOperator(
        task_id="file_copy",
        dag=dag,
        output_stream=open("test.xlsx", "ab"),  # If this was 'wb', it would be
        # overwritten every time the
        # dag was read by the scheduler
        # In a real scenario, it might be helpful to get a random file name
        # to write to, see the `tempfile` module.
        input_stream=GDriveReader(
            json_credentials,
            (
                "Copy of DBC Specification - "
                "Sanofi Flatiron Multiple Myeloma - 20220504"
            ),
        ),
    )
    start >> download >> end
