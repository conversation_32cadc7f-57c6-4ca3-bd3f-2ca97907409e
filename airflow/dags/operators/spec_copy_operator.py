import logging
import os

from airflow.models import BaseOperator

from aetion.adip.airflow.integrations.s3.s3_support import AetionS3FileSystem
from aetion.adip.airflow.integrations.google.google_drive_reader import G<PERSON>riveReader
from operators.utils import DATA_SPEC
from tools.config import is_truthy

logger = logging.getLogger(__name__)

GDRIVE_SPEC_FILENAME = (
    "data specification - {self.client} - {self.dataset} - {self.revision}"
)
S3_SPEC_FILENAME = "{self.dataset_artifacts_path}{DATA_SPEC}"


class SpecCopyOperator(BaseOperator):
    template_fields = (
        "dataset_artifacts_path",
        "client",
        "dataset",
        "revision",
        "use_copy_spec",
        "gdrive_credentials",
    )

    template_ext = ()

    def __init__(
        self,
        global_artifacts_path,
        dataset_artifacts_path,
        client,
        dataset,
        revision,
        use_copy_spec=False,
        gdrive_credentials=None,
        aws_conn_id="aws_default",
        repo=None,
        git_meta_repo=None,
        private_key=None,
        git_default_branch=None,
        branch=None,
        *args,
        **kwargs,
    ):
        super(SpecCopyOperator, self).__init__(*args, **kwargs)
        self.global_artifacts_path = global_artifacts_path
        self.dataset_artifacts_path = dataset_artifacts_path
        self.client = client
        self.dataset = dataset
        self.revision = revision
        self.use_copy_spec = is_truthy(use_copy_spec)
        self.gdrive_credentials = gdrive_credentials
        self.aws_conn_id = aws_conn_id
        self._fs: AetionS3FileSystem | None = None

    @property
    def fs(self):
        """
        Lazy-initialized S3 filesystem interface.

        Returns
        -------
        AetionS3FileSystem
            Configured S3 filesystem instance for file operations.
        """
        if self._fs is None:
            self._fs = AetionS3FileSystem(aws_conn_id=self.aws_conn_id)
        return self._fs

    def execute(self, context):
        logger.info(f"In spec copy operator, use_copy_spec is {self.use_copy_spec}")

        if self.use_copy_spec:
            logger.info(
                f"Copying spec to artifacts path: {self.dataset_artifacts_path}"
            )
            self.upload_spec(context)
        else:
            logger.info("Skipping spec copy")

    def upload_spec(self, context):
        gdrive_filename = GDRIVE_SPEC_FILENAME.format(self=self)
        s3_filename = os.path.join(self.dataset_artifacts_path, DATA_SPEC)
        s3_global_filename = os.path.join(
            self.global_artifacts_path,
            "specs",
            "{GDRIVE_SPEC_FILENAME}.xlsx".format(GDRIVE_SPEC_FILENAME=gdrive_filename),
        )

        # read from s3
        if self.fs.exists(s3_global_filename):
            logging.info(
                f"Found Spec in S3! Copying {s3_global_filename} ->  {s3_filename}"
            )
            self.fs.copy_basic(s3_global_filename, s3_filename)
        # read from gdrive
        else:
            logging.info(f"Reading from {gdrive_filename}")
            logging.info(f"Writing to: {s3_filename}")
            reader = GDriveReader(
                filename=gdrive_filename,
                json_credentials=self.gdrive_credentials,
            )
            with reader as input_stream:
                self.fs.put(input_stream, s3_filename)
