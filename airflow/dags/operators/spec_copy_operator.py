import logging
import os

from airflow.models import <PERSON><PERSON>perator

from operators.file_copy_operator import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, S3Writer, GDriveReader
from operators.utils import DATA_SPEC
from operators.aws import fs_factory_default
from tools.config import is_truthy

logger = logging.getLogger(__name__)

GDRIVE_SPEC_FILENAME = "data specification - {self.client} - {self.dataset} - {self.revision}"
S3_SPEC_FILENAME = "{self.dataset_artifacts_path}{DATA_SPEC}"


class SpecCopyOperator(BaseOperator):
    template_fields = ('dataset_artifacts_path', 'client', 'dataset', 'revision', 'use_copy_spec', 'gdrive_credentials')

    template_ext = ()

    def __init__(self,
                 global_artifacts_path,
                 dataset_artifacts_path,
                 client,
                 dataset,
                 revision,
                 use_copy_spec=False,
                 gdrive_credentials=None,
                 fs_factory=fs_factory_default,
                 repo=None,
                 git_meta_repo=None,
                 private_key=None,
                 git_default_branch=None,
                 branch=None,
                 *args,
                 **kwargs
    ):
        super(SpecCopyOperator, self).__init__(*args, **kwargs)
        self.global_artifacts_path = global_artifacts_path
        self.dataset_artifacts_path = dataset_artifacts_path
        self.client = client
        self.dataset = dataset
        self.revision = revision
        self.use_copy_spec = is_truthy(use_copy_spec)
        self.gdrive_credentials = gdrive_credentials
        self.fs = fs_factory()

    def execute(self, context):
        logger.info(f"In spec copy operator, use_copy_spec is {self.use_copy_spec}")

        if self.use_copy_spec:
            logger.info(f"Copying spec to artifacts path: {self.dataset_artifacts_path}")
            self.upload_spec(context)
        else:
            logger.info(f"Skipping spec copy")

    def upload_spec(self, context):
        gdrive_filename = GDRIVE_SPEC_FILENAME.format(self=self)
        s3_filename = os.path.join(self.dataset_artifacts_path, DATA_SPEC)
        s3_global_filename = os.path.join(self.global_artifacts_path, "specs", "{GDRIVE_SPEC_FILENAME}.xlsx".format(GDRIVE_SPEC_FILENAME=gdrive_filename))

        # read from s3
        if self.fs.exists(s3_global_filename):
            logging.info(f"Found Spec in S3! Copying {s3_global_filename} ->  {s3_filename}")
            self.fs.copy_basic(s3_global_filename, s3_filename)
        # read from gdrive
        else:
            logging.info(f"Reading from {gdrive_filename}")
            logging.info(f"Writing to: {s3_filename}")
            copy_op = FileCopyOperator(
                task_id="copy_spec",
                input_stream=GDriveReader(
                    filename=gdrive_filename,
                    json_credentials=self.gdrive_credentials,
                ),
                output_stream=S3Writer(
                    filename=s3_filename,
                )
            )
            copy_op.execute(context=context)
