# STD
from io import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>rite<PERSON>, Bytes<PERSON>
from typing import Callable, List, Optional
from pathlib import Path
import logging
import os

# Airflow
from airflow.models import BaseOperator


# Custom
from operators.utils import lazy

from oauth2client.service_account import ServiceAccountCredentials
from google.auth.credentials import AnonymousCredentials
from googleapiclient.errors import HttpError
from apiclient.discovery import build


class FileCopyOperator(BaseOperator):
    def __init__(
        self,
        input_stream: BufferedReader,
        output_stream: BufferedWriter,
        transformer: Callable[[bytes], bytes] = lambda x: x,
        *args,
        **kwargs,
    ):
        """
        Copy a file from one place to another.

        An operator to copy small files that can be held in memory. If you need
        to copy large files, don't try to hack this operator, just write a new
        one.
        :param input_stream: The stream to copy data from
        :type input_stream: io.BufferedReader
        :param output_stream: The stream to write data to
        :type output_stream: io.BufferedWriter
        :param transformer: A function that can modify the file as it's copied.
            This function is called with a :class:bytes object. The transformer
            must return a :class:bytes object to be written to the output
            stream. The returned bytes object may have a length of 0.
        :type transformer: Callable[[bytes],bytes]
        """
        self.input_stream = input_stream
        self.output_stream = output_stream
        self.transformer = transformer
        super().__init__(*args, **kwargs)

    def execute(self, context):
        try:
            data = self.input_stream.read()
            logging.info(f"Read {len(data)} bytes")
        except EOFError:
            logging.warn(
                "Failed to read any data from input stream, " "hit EOF immediately."
            )
            data = None
        try:
            transformed = self.transformer(data)
            logging.info(f"Transformed into {len(data)} bytes")
        except Exception as e:
            raise Exception(
                f"Failed while calling transformer {self.transformer} "
                f"with data {data}"
            ) from e
        try:
            written = self.output_stream.write(transformed)
            self.output_stream.flush()
            logging.info(f"Wrote {written} bytes")
        except Exception as e:
            raise Exception(
                f"Transformation succeeded with output {transformed}, but "
                f"writing to the output stream {self.output_stream} failed"
            ) from e


@lazy()
class GDriveReader(BufferedReader):

    default_mimetype = (
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
    default_scopes = [
        "https://www.googleapis.com/auth/drive.metadata.readonly",
        "https://www.googleapis.com/auth/drive.readonly",
    ]
    default_gdrive_envvar = "GOOGLE_APPLICATION_CREDENTIALS"

    @staticmethod
    def iter_gdrive(service, q):
        "Generator for the google drive api to make it look more pythonic"
        files_api = service.files()
        list_req = files_api.list(
            pageSize=10, fields="nextPageToken, files(id, name)", q=q
        )
        while list_req is not None:
            file_list = list_req.execute()
            for file in file_list["files"]:
                yield file
            list_req = files_api.list_next(list_req, file_list)

    def __init__(
        self,
        filename: str,
        query: bool = False,
        json_credentials: Optional[str] = None,
        mimetype: str = default_mimetype,
        scopes: List[str] = default_scopes,
    ):
        """
        Read from google drive

        A class that wraps access to files in google drive in the python
        abstraction of an io.BufferedReader, BufferedReaders are file-like
        objects.
        :param file_name: The name of the file in google drive to find
        :type file_name: str
        :param query: Is the filename a query instead of a filename? See
            https://developers.google.com/drive/api/guides/search-files
            for query DSL.
        :type query: bool
        :param json_credentials the credentials to use to authenticate with
            google drive. Details are described at
            TODO: I can't actually find a page that documents the format, I've
            copied what I'm using locally here for now with details redacted.
            This was created by copying the output of
            ServiceAccountCredentials.from_json_keyfile_name(...).to_json()
                {
                    "client_id": "103726823025215315097",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "revoke_uri": "https://oauth2.googleapis.com/revoke",
                    "_service_account_email": "... .iam.gserviceaccount.com",
                    "_scopes": "https://www.googleapis.com/auth/drive.metadata.readonly https://www.googleapis.com/auth/drive.readonly",
                    "_private_key_id": "...",
                    "_private_key_pkcs8_pem": "-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\\n",
                    "_class": "ServiceAccountCredentials",
                    "_module": "oauth2client.service_account"
                }
        :type json_credentials: str
        :param scopes: The scopes the json credentials have, we need at least
            drive.metadata.readonly (to read the names of files) and
            drive.readonly (to read the data of the file)
        :type scopes: List[str]
        :param mimetype: The format we should ask google for the document.
            Changing this changes what type of document we download. See
            https://developers.google.com/drive/api/guides/ref-export-formats
            for valid mime types.
        :type mimetype: str
        """
        self.credentials = None
        if json_credentials is not None:
            self.credentials = ServiceAccountCredentials.from_json(
                json_data=json_credentials,
            )
        elif self.default_gdrive_envvar in os.environ:
            cred_file = os.getenv(self.default_gdrive_envvar)
            self.credentials = ServiceAccountCredentials.from_json_keyfile_name(
                filename=cred_file
            )
        else:
            logging.warn(
                f"{self.__class__} was not passed credentials to log into "
                f"gdrive, and no {self.default_gdrive_envvar} environment variable "
                f"was specified. Using anonymous credentials."
            )
            self.credentials = AnonymousCredentials()
        service = build("drive", "v3", credentials=self.credentials)
        file_id = None
        search = filename
        if not query:
            # See https://developers.google.com/drive/api/guides/search-files
            search = f"name contains '{filename}'"
        for file_item in GDriveReader.iter_gdrive(service, q=search):
            logging.info(f"File item: {file_item}")
            _id = file_item["id"]
            _name = Path(file_item["name"]).with_suffix("").name
            if _name == filename:
                file_id = _id
                break
        if file_id is None:
            raise FileNotFoundError(
                f"Failed to find file {filename} from the query {search}. "
                f"Enable debug log for a verbose printout of the files we did "
                f"find."
            )
        try:
            data = service.files().export(fileId=file_id, mimeType=mimetype).execute()
        except HttpError as e:
            if e.error_details[0]["reason"] == "fileNotExportable":
                data = service.files().get_media(fileId=file_id).execute()
            else:
                raise e
        super().__init__(BytesIO(data))
