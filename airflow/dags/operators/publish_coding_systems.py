import gzip
import json
import logging
import os
import shutil
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from urllib.parse import urljoin, urlparse

from airflow.exceptions import AirflowException, AirflowFailException
from airflow.models import BaseOperator
from airflow.operators.python import PythonOperator

from aetion.adip.airflow.integrations.s3.s3_support import AetionS3FileSystem
from dbc_utilities.dbc_validator.rdc import RDCEntry, CodingSystem
from hooks.git_hook import GitHook
from operators.catalog_loader import get_catalog_from_s3
from tools.git_credentials import GitCredentials
from tools.adip_git_repo_manager import ADIPGitRepoManager
from tools.aws_util import build_s3_path

logger = logging.getLogger(__name__)

# Constants for CSV column handling
DEFAULT_CODE_COLUMN = "code"
DEFAULT_DESCRIPTION_COLUMN = "description"

# Constants for folder structure
BASE_FOLDER_NAME = "base"
META_DATA_STORE_PATH = "metaDataStore"
VERSIONED_PATH = "versioned"
CODING_PATH = "coding"
CODING_SYSTEMS_FILE = "coding_systems.csv"


@dataclass
class CodingSystemAttribute:
    """Represents an attribute in a coding system base resource file."""
    dataType: str
    name: str
    description: str
    refCodingSystemAttribute: Optional[str] = None
    isArray: Optional[bool] = None


@dataclass
class CodingSystemBaseResource:
    """Represents a coding system base resource file."""
    name: str
    description: str
    longDescription: str
    caseSensitiveCode: bool = False
    attributes: List[CodingSystemAttribute] = field(default_factory=list)


@dataclass
class CodingSystemInstanceParams:
    """Represents the params section of a coding system instance resource file."""
    filename: str
    codeCsvCol: str
    descCsvCol: str
    attrCsvCols: Optional[Dict[str, str]] = None


@dataclass
class CodingSystemInstanceResource:
    """Represents a coding system instance resource file."""
    name: str
    codingSystem: str
    date: str
    params: CodingSystemInstanceParams


class PublishCodingSystems(PythonOperator):
    """
    Publishes coding systems to the metadatastore repository.
    
    This operator:
    1. Reads the raw data catalog from S3
    2. Clones the metadatastore repository with sparse checkout for coding systems
    3. Iterates over coding_systems in the RDC
    4. For each coding system, either updates existing or creates new
    5. Commits and pushes changes to a new branch
    """
    
    template_fields = (
        'client', 'dataset', 'revision', 'rdc_url_s3', 'etl_path', 'git_meta_repo', 
        'private_key', 'git_default_branch', 'aws_conn_id'
    )
    template_ext = ()

    def __init__(self,
                 client: str,
                 dataset: str,
                 revision: str,
                 rdc_url_s3: str,
                 etl_path: str,
                 repo: str,
                 git_meta_repo: str,
                 private_key: str,
                 git_default_branch: str,
                 branch: str,
                 aws_conn_id: str = "aws_default",
                 *args,
                 **kwargs):
        super(PublishCodingSystems, self).__init__(python_callable=self.publish_coding_systems, *args, **kwargs)
        self.client = client
        self.dataset = dataset
        self.revision = revision
        self.rdc_url_s3 = rdc_url_s3
        self.etl_path = etl_path
        self.repo = repo
        self.git_meta_repo = git_meta_repo
        self.private_key = private_key
        self.git_default_branch = git_default_branch
        self.branch = branch
        self.aws_conn_id = aws_conn_id
        self._fs: AetionS3FileSystem | None = None

    @property
    def fs(self) -> AetionS3FileSystem:
        """Lazy initialization of S3 filesystem."""
        if self._fs is None:
            self._fs = AetionS3FileSystem(aws_conn_id=self.aws_conn_id)
        return self._fs

    def publish_coding_systems(self):
        """
        Main method to publish coding systems to metadatastore.
        """
        logger.info(f"Starting coding systems publication for {self.dataset}-{self.client}")
        
        # Load raw data catalog
        rdc_entry = self._load_raw_data_catalog()
        
        # Check if coding_systems exist
        if not rdc_entry.coding_systems:
            logger.info(f"No coding_systems defined for {self.dataset}-{self.client}")
            return
        
        coding_systems = rdc_entry.coding_systems
        logger.info(f"Found {len(coding_systems)} coding systems to process")
        
        # Clone metadatastore repository
        with tempfile.TemporaryDirectory() as tmp_meta_folder:
            with GitHook(self.git_meta_repo, self.private_key) as meta_git:
                
                logger.info(f"Cloning metadatastore {self.git_meta_repo} to {tmp_meta_folder}")
                # Sparse checkout for specific dataset/client coding systems only
                dataset_client = f"{self.dataset}_{self.client}"
                sparse_folders = [
                    f'metaDataStore/versioned/coding/{dataset_client}/',
                    'metaDataStore/versioned/coding/coding_systems.csv'
                ]
                logger.info(f"Attempting sparse checkout with folders: {sparse_folders}")
                
                meta_git.clone(tmp_meta_folder, self.git_default_branch, sparse_folders=sparse_folders)
                
                # Create new branch
                branch_name = f"coding_systems_{self.client}_{self.dataset}_{self.revision}"
                meta_git.checkout_new(branch_name)
                logger.info(f"Created branch: {branch_name}")
                
                # Process each coding system and track errors
                failed_coding_systems = []
                successful_count = 0
                
                logger.info(f"Starting to process {len(coding_systems)} coding systems...")
                
                for i, coding_system in enumerate(coding_systems, 1):
                    logger.info(f"Processing coding system {i}/{len(coding_systems)}: {coding_system.name}")
                    try:
                        self._process_coding_system(coding_system, tmp_meta_folder)
                        successful_count += 1
                        logger.info(f"Successfully processed coding system: {coding_system.name}")
                    except Exception as e:
                        error_msg = f"Failed to process coding system '{coding_system.name}': {str(e)}"
                        logger.error(error_msg)
                        failed_coding_systems.append({
                            'name': coding_system.name,
                            'error': str(e)
                        })
                
                logger.info(f"Processing complete: {successful_count} successful, {len(failed_coding_systems)} failed")
                
                # Update coding systems registry with only successful coding systems
                successful_coding_systems = [cs for cs in coding_systems if not any(f['name'] == cs.name for f in failed_coding_systems)]
                if successful_coding_systems:
                    self._update_coding_systems_registry(tmp_meta_folder, successful_coding_systems)
                else:
                    logger.warning("No coding systems were successfully processed, skipping registry update")
                
                # Commit and push changes
                if meta_git.any_changes():
                    commit_message = f"Update coding systems for {self.dataset}-{self.client} revision {self.revision}"
                    meta_git.commit(commit_message)
                    meta_git.push(branch_name, branch_name, '-f')
                    logger.info(f"Successfully pushed branch: {branch_name}")
                    
                    # Create GitHub PR
                    self._create_github_pr(branch_name, commit_message)
                    
                    # Copy coding_systems.csv to S3
                    self._copy_coding_systems_to_s3(tmp_meta_folder)
                else:
                    logger.info("No changes to commit")
                
                # Report any failures at the end
                if failed_coding_systems:
                    error_details = []
                    for failure in failed_coding_systems:
                        error_details.append(f"  - {failure['name']}: {failure['error']}")
                    
                    error_summary = f"Task completed with {len(failed_coding_systems)} coding system(s) failed:\n" + "\n".join(error_details)
                    logger.error(error_summary)
                    
                    # If all coding systems failed, fail the task
                    if len(failed_coding_systems) == len(coding_systems):
                        raise AirflowException(f"All coding systems failed to process:\n" + "\n".join(error_details))
                    else:
                        # Partial success - still fail the task but provide summary
                        successful_count = len(coding_systems) - len(failed_coding_systems)
                        raise AirflowException(
                            f"Task completed with partial success: {successful_count} coding system(s) processed successfully, "
                            f"{len(failed_coding_systems)} failed:\n" + "\n".join(error_details)
                        )
    
    def _load_raw_data_catalog(self) -> RDCEntry:
        """
        Load raw data catalog from S3.
        
        Returns:
            RDCEntry containing the RDC entry for the dataset
        """
        logger.info(f"Loading raw data catalog from {self.rdc_url_s3}")
        try:
            rdc_entry_dict = get_catalog_from_s3(
                client_name=self.client,
                dataset_name=self.dataset,
                rdc_path=self.rdc_url_s3,
                fs=self.fs
            )
            # Convert the dictionary to RDCEntry
            rdc_entry = RDCEntry.parse_rdc_entry_dict(**rdc_entry_dict)
            
            # Convert coding_systems dictionaries to CodingSystem objects
            if hasattr(rdc_entry, 'coding_systems') and rdc_entry.coding_systems:
                coding_system_objects = []
                for cs_dict in rdc_entry.coding_systems:
                    if isinstance(cs_dict, dict):
                        coding_system_obj = CodingSystem(**cs_dict)
                        coding_system_objects.append(coding_system_obj)
                    else:
                        # If it's already an object, keep it as is
                        coding_system_objects.append(cs_dict)
                rdc_entry.coding_systems = coding_system_objects
            
            logger.info(f"Successfully loaded RDC entry for {self.dataset}-{self.client}")
            return rdc_entry
        except Exception as e:
            raise AirflowFailException(f"Failed to load raw data catalog: {e}")
    
    def _compress_file(self, file_path: Path) -> Path:
        """
        Compress a file using gzip.
        
        Args:
            file_path: Path to the file to compress
            
        Returns:
            Path to the compressed file
        """
        compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
        logger.info(f"Compressing {file_path} to {compressed_path}")
        
        with open(file_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        # Remove the original uncompressed file
        file_path.unlink()
        logger.info(f"Removed original file: {file_path}")
        
        return compressed_path
    
    def _update_instance_resource_file(self, json_file_path: Path, old_revision: str):
        """
        Update the instance resource file with new revision information.
        
        Args:
            json_file_path: Path to the JSON file to update
            old_revision: The old revision that needs to be replaced
        """
        logger.info(f"Updating instance resource file: {json_file_path}")
        
        try:
            # Read the existing JSON file
            with open(json_file_path, 'r') as f:
                instance_data = json.load(f)
            
            # Parse into our dataclass
            params_data = instance_data.get('params', {})
            params = CodingSystemInstanceParams(
                filename=params_data.get('filename', ''),
                codeCsvCol=params_data.get('codeCsvCol', ''),
                descCsvCol=params_data.get('descCsvCol', ''),
                attrCsvCols=params_data.get('attrCsvCols') if params_data.get('attrCsvCols') is not None else {}
            )
            
            # Get current date in yyyy-MM-dd format
            current_date = datetime.now().strftime("%Y-%m-%d")
            
            instance_resource = CodingSystemInstanceResource(
                name=instance_data.get('name', ''),
                codingSystem=instance_data.get('codingSystem', ''),
                date=current_date,  # Update to current date
                params=params
            )
            
            # Update the name to include the new revision
            if old_revision in instance_resource.name:
                instance_resource.name = instance_resource.name.replace(old_revision, self.revision)
                logger.info(f"Updated name from {instance_data.get('name', '')} to {instance_resource.name}")
            
            # Update the filename if it contains the old revision
            if old_revision in instance_resource.params.filename:
                instance_resource.params.filename = instance_resource.params.filename.replace(old_revision, self.revision)
                logger.info(f"Updated filename from {params_data.get('filename', '')} to {instance_resource.params.filename}")
            
            # Convert back to dictionary and always include attrCsvCols
            params_dict = {
                'filename': instance_resource.params.filename,
                'codeCsvCol': instance_resource.params.codeCsvCol,
                'descCsvCol': instance_resource.params.descCsvCol,
                'attrCsvCols': instance_resource.params.attrCsvCols if instance_resource.params.attrCsvCols is not None else {}
            }
            
            updated_data = {
                'name': instance_resource.name,
                'codingSystem': instance_resource.codingSystem,
                'date': instance_resource.date,
                'params': params_dict
            }
            
            # Write the updated JSON back to file
            with open(json_file_path, 'w') as f:
                json.dump(updated_data, f, indent=4)
            
            logger.info(f"Successfully updated instance resource file: {json_file_path}")
            
        except Exception as e:
            raise AirflowException(f"Failed to update instance resource file {json_file_path}: {e}")
    
    def _update_instance_resource_file_with_columns(self, json_file_path: Path, revision: str, csv_columns: List[str], coding_system: CodingSystem) -> None:
        """
        Update the instance resource file with new revision information and CSV column data.
        
        Args:
            json_file_path: Path to the JSON file to update
            revision: The revision to use
            csv_columns: List of CSV column names
            coding_system: The coding system object
        """
        logger.info(f"Updating instance resource file with columns: {json_file_path}")
        
        try:
            # Read the existing JSON file
            with open(json_file_path, 'r') as f:
                file_content = f.read()
                
                # Check if file is empty
                if not file_content.strip():
                    logger.warning(f"JSON file is empty: {json_file_path}")
                    # Create a default structure if file is empty
                    instance_data = {
                        'name': f"{coding_system.name}_{revision}",
                        'codingSystem': coding_system.name,
                        'date': datetime.now().strftime("%Y-%m-%d"),
                        'params': {
                            'filename': f"{coding_system.name}_{revision}.csv.gz",
                            'codeCsvCol': csv_columns[0] if len(csv_columns) > 0 else "",
                            'descCsvCol': csv_columns[1] if len(csv_columns) > 1 else "",
                            'attrCsvCols': {}
                        }
                    }
                else:
                    # Parse the JSON content
                    instance_data = json.loads(file_content)
            
            # Get current date in yyyy-MM-dd format
            current_date = datetime.now().strftime("%Y-%m-%d")
            
            # Get code and description column names from RDC or defaults
            code_csv_col = coding_system.code_column or DEFAULT_CODE_COLUMN
            desc_csv_col = coding_system.description_column or DEFAULT_DESCRIPTION_COLUMN
            
            # Get the base name for the coding system
            name = instance_data.get('name', '')
            base_name = name.split('_')[0] if name and '_' in name else name
            
            # Create params with CSV column information
            params_dict = {
                'filename': f"{base_name}_{revision}.csv.gz",
                'codeCsvCol': code_csv_col,
                'descCsvCol': desc_csv_col,
                'attrCsvCols': {}  # No additional attributes for now
            }
            
            updated_data = {
                'name': f"{base_name}_{revision}",
                'codingSystem': instance_data.get('codingSystem', ''),
                'date': current_date,
                'params': params_dict
            }
            
            # Write the updated JSON back to file
            with open(json_file_path, 'w') as f:
                json.dump(updated_data, f, indent=4)
            
            logger.info(f"Successfully updated instance resource file with columns: {json_file_path}")
            
        except Exception as e:
            raise AirflowException(f"Failed to update instance resource file {json_file_path}: {e}")

    def _validate_csv_columns_exist(self, csv_columns: List[str], coding_system: CodingSystem) -> None:
        """
        Validate that required columns exist in the CSV.
        
        Args:
            csv_columns: List of column names from CSV
            coding_system: CodingSystem object from RDC
            
        Raises:
            AirflowException: If required columns are missing
        """
        code_column = coding_system.code_column or DEFAULT_CODE_COLUMN
        desc_column = coding_system.description_column or DEFAULT_DESCRIPTION_COLUMN
        
        missing_columns = []
        if code_column not in csv_columns:
            missing_columns.append(code_column)
        if desc_column not in csv_columns:
            missing_columns.append(desc_column)
        
        if missing_columns:
            raise AirflowException(
                f"Required columns not found in CSV: {missing_columns}. "
                f"Available columns: {csv_columns}"
            )
    
    def _read_csv_headers(self, csv_file_path: Path) -> List[str]:
        """
        Read the headers from a CSV file.
        
        Args:
            csv_file_path: Path to the CSV file
            
        Returns:
            List of column names
        """
        import csv
        
        try:
            with open(csv_file_path, 'r', newline='') as csvfile:
                reader = csv.reader(csvfile)
                headers = next(reader)  # Read the first row (headers)
                return [header.strip() for header in headers]
        except Exception as e:
            raise AirflowException(f"Failed to read CSV headers from {csv_file_path}: {e}")
    
    def _create_base_resource_file(self, coding_system: CodingSystem, coding_base_path: Path, csv_columns: List[str]) -> None:
        """
        Create the base resource file for a new coding system.
        
        Args:
            coding_system: CodingSystem object from RDC
            coding_base_path: Base path for the coding system in metadatastore
            csv_columns: List of column names from the CSV file
        """
        coding_system_name = coding_system.name
        coding_system_folder = coding_system.metadata_folder or coding_system.name.lower()
        
        # Create base folder
        base_folder = coding_base_path / BASE_FOLDER_NAME
        base_folder.mkdir(parents=True, exist_ok=True)
        
        # Create base resource file path
        base_resource_file = base_folder / f"{coding_system_folder}.json"
        
        # Get description and long_description from RDC
        description = coding_system.description or coding_system_name
        long_description = coding_system.long_description or description
        case_sensitive = coding_system.case_sensitive or False
        
        # Get code column name
        code_csv_col = coding_system.code_column or DEFAULT_CODE_COLUMN
        
        # Create attributes based on CSV columns
        attributes = []
        if len(csv_columns) == 2:
            # Only 2 columns: code and description, only declare CODE
            attributes.append(CodingSystemAttribute(
                dataType="CODE",
                name="CODE",
                description="CODE"
            ))
        else:
            # More than 2 columns: declare all columns as attributes
            for column in csv_columns:
                attributes.append(CodingSystemAttribute(
                    dataType="CODE",
                    name=column.upper(),
                    description=column
                ))
        
        # Create base resource object
        base_resource = CodingSystemBaseResource(
            name=coding_system_name,  # Use actual coding system name, not folder name
            description=description,
            longDescription=long_description,
            caseSensitiveCode=case_sensitive,
            attributes=attributes
        )
        
        # Write base resource file
        try:
            # Convert attributes to dictionaries and filter out null values
            attribute_dicts = []
            for attr in base_resource.attributes:
                attr_dict = attr.__dict__.copy()
                # Remove null values
                attr_dict = {k: v for k, v in attr_dict.items() if v is not None}
                attribute_dicts.append(attr_dict)
            
            with open(base_resource_file, 'w') as f:
                json.dump({
                    'name': base_resource.name,
                    'description': base_resource.description,
                    'longDescription': base_resource.longDescription,
                    'caseSensitiveCode': base_resource.caseSensitiveCode,
                    'attributes': attribute_dicts
                }, f, indent=4)
            
            logger.info(f"Created base resource file: {base_resource_file}")
            logger.info(f"Added {len(attributes)} attributes to base resource file")
            
        except Exception as e:
            raise AirflowException(f"Failed to create base resource file {base_resource_file}: {e}")
    
    def _create_instance_resource_file(self, coding_system: CodingSystem, revision_folder: Path, csv_columns: List[str]) -> None:
        """
        Create the instance resource file for a new coding system.
        
        Args:
            coding_system: CodingSystem object from RDC
            revision_folder: Path to the revision folder
            csv_columns: List of column names from the CSV file
        """
        coding_system_name = coding_system.name
        coding_system_folder = coding_system.metadata_folder or coding_system.name.lower()
        
        # Create instance resource file path
        instance_resource_file = revision_folder / f"{coding_system_folder}_{self.revision}.json"
        
        # Get current date in yyyy-MM-dd format
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        # Get code and description column names from RDC or defaults
        code_csv_col = coding_system.code_column or DEFAULT_CODE_COLUMN
        desc_csv_col = coding_system.description_column or DEFAULT_DESCRIPTION_COLUMN
        
        # Create params
        params = {
            'filename': f"{coding_system_folder}_{self.revision}.csv.gz",
            'codeCsvCol': code_csv_col,
            'descCsvCol': desc_csv_col
        }
        
        # Add attrCsvCols if more than 2 columns
        if len(csv_columns) > 2:
            attr_csv_cols = {}
            for column in csv_columns:
                # Exclude the column that corresponds to the CODE attribute
                if column.lower() != code_csv_col.lower():
                    attr_csv_cols[column.upper()] = column
            params['attrCsvCols'] = attr_csv_cols
        else:
            params['attrCsvCols'] = {}  # Always present, even if empty
        
        # Create instance resource object
        # For the name property, always use coding system name (not folder name) + revision
        instance_resource = CodingSystemInstanceResource(
            name=f"{coding_system_name}_{self.revision}",
            codingSystem=coding_system_name,  # Use actual coding system name, not folder name
            date=current_date,
            params=CodingSystemInstanceParams(
                filename=params['filename'],
                codeCsvCol=params['codeCsvCol'],
                descCsvCol=params['descCsvCol'],
                attrCsvCols=params.get('attrCsvCols')
            )
        )
        
        # Write instance resource file
        try:
            # Build params dictionary and always include attrCsvCols
            params_dict = {
                'filename': instance_resource.params.filename,
                'codeCsvCol': instance_resource.params.codeCsvCol,
                'descCsvCol': instance_resource.params.descCsvCol,
                'attrCsvCols': instance_resource.params.attrCsvCols if instance_resource.params.attrCsvCols is not None else {}
            }
            
            with open(instance_resource_file, 'w') as f:
                json.dump({
                    'name': instance_resource.name,
                    'codingSystem': instance_resource.codingSystem,
                    'date': instance_resource.date,
                    'params': params_dict
                }, f, indent=4)
            
            logger.info(f"Created instance resource file: {instance_resource_file}")
            
        except Exception as e:
            raise AirflowException(f"Failed to create instance resource file {instance_resource_file}: {e}")
    
    def _validate_csv_columns(self, csv_file_path: Path, instance_resource_file: Path):
        """
        Validate that CSV columns match what's defined in the instance resource file.
        
        Args:
            csv_file_path: Path to the CSV file
            instance_resource_file: Path to the instance resource JSON file
        """
        logger.info(f"Validating CSV columns against instance resource file")
        
        try:
            # Read CSV headers
            csv_columns = self._read_csv_headers(csv_file_path)
            
            # Read instance resource file
            with open(instance_resource_file, 'r') as f:
                instance_data = json.load(f)
            
            params = instance_data.get('params', {})
            code_csv_col = params.get('codeCsvCol', '')
            desc_csv_col = params.get('descCsvCol', '')
            attr_csv_cols = params.get('attrCsvCols', {})
            
            # Build expected columns list
            expected_columns = set()
            expected_columns.add(code_csv_col)
            expected_columns.add(desc_csv_col)
            expected_columns.update(attr_csv_cols.values())
            
            # Remove empty strings
            expected_columns.discard('')
            
            # Convert to sets for comparison
            csv_columns_set = set(csv_columns)
            
            # Check if CSV columns match expected columns
            if csv_columns_set != expected_columns:
                missing_columns = expected_columns - csv_columns_set
                extra_columns = csv_columns_set - expected_columns
                
                error_msg = f"CSV columns do not match instance resource definition:\n"
                if missing_columns:
                    error_msg += f"Missing columns: {missing_columns}\n"
                if extra_columns:
                    error_msg += f"Extra columns: {extra_columns}\n"
                error_msg += f"Expected: {sorted(expected_columns)}\n"
                error_msg += f"Found: {sorted(csv_columns_set)}"
                
                raise AirflowException(error_msg)
            
            logger.info("CSV columns validation passed")
            
        except Exception as e:
            if isinstance(e, AirflowException):
                raise e
            raise AirflowException(f"Failed to validate CSV columns: {e}")
    
    def _update_coding_systems_registry(self, tmp_meta_folder: str, coding_systems: List[CodingSystem]) -> None:
        """
        Update the coding systems registry CSV file.
        
        Args:
            tmp_meta_folder: Path to the cloned metadatastore repository
            coding_systems: List of CodingSystem objects from RDC
        """
        import csv
        import shutil
        
        registry_file = Path(tmp_meta_folder) / META_DATA_STORE_PATH / VERSIONED_PATH / CODING_PATH / CODING_SYSTEMS_FILE
        logger.info(f"Updating coding systems registry: {registry_file}")
        
        try:

            # Read all existing rows and preserve order
            existing_rows = []
            key_to_index = {}  # (name, type) -> index in existing_rows
            original_count = 0
            
            if registry_file.exists():
                logger.info(f"Reading coding_systems.csv file: {registry_file}")
                with open(registry_file, 'r', newline='') as csvfile:
                    reader = csv.DictReader(csvfile)
                    for idx, row in enumerate(reader):
                        key = (row['name'], row['type'])
                        existing_rows.append(row.copy())
                        # Only keep the first occurrence for updating; preserve all for writing
                        if key not in key_to_index:
                            key_to_index[key] = idx
                        original_count += 1
            else:
                error_msg = f"coding_systems.csv file not found at {registry_file}. This indicates a problem with the sparse checkout. The file should always exist."
                logger.error(error_msg)
                raise AirflowException(error_msg)
            
            # Track which entries are being updated or added
            updated_keys = set()
            new_rows = []
            
            # Update or add entries for each coding system
            for coding_system in coding_systems:
                coding_system_name = coding_system.name
                coding_system_folder = coding_system.metadata_folder or coding_system.name.lower()
                dataset_client_type = f"{self.dataset}_{self.client}"
                key = (coding_system_name, dataset_client_type)
                
                # Build relative paths using pathlib for consistency
                base_resource_path = Path(VERSIONED_PATH) / CODING_PATH / dataset_client_type / coding_system_folder / BASE_FOLDER_NAME / f"{coding_system_folder}.json"
                instance_resource_path = Path(VERSIONED_PATH) / CODING_PATH / dataset_client_type / coding_system_folder / self.revision / f"{coding_system_folder}_{self.revision}.json"
                csv_data_path = Path(VERSIONED_PATH) / CODING_PATH / dataset_client_type / coding_system_folder / self.revision / f"{coding_system_folder}_{self.revision}.csv.gz"
                
                # Convert to string for CSV storage
                base_resource_path_str = str(base_resource_path).replace('\\', '/')  # Ensure forward slashes
                instance_resource_path_str = str(instance_resource_path).replace('\\', '/')
                csv_data_path_str = str(csv_data_path).replace('\\', '/')
                
                # Prepare updated/new row
                updated_row = {
                    'name': coding_system_name,
                    'type': dataset_client_type,
                    'csResource': base_resource_path_str,
                    'csInstanceResource': instance_resource_path_str,
                    'csInstanceDataResource': csv_data_path_str,
                    'caseSensitive': str(coding_system.case_sensitive or False).lower(),
                    'codeProcessor': ''
                }
                
                if key in key_to_index:
                    # Update the first occurrence in-place
                    idx = key_to_index[key]
                    existing_rows[idx].update(updated_row)
                    updated_keys.add(key)
                    logger.info(f"Updated existing coding system: {key}")
                else:
                    # Add new row at the end
                    new_rows.append(updated_row)
                    logger.info(f"Added new coding system: {key}")
            
            # Ensure all rows have all required fields
            fieldnames = ['name', 'type', 'csResource', 'csInstanceResource', 'csInstanceDataResource', 'caseSensitive', 'codeProcessor']
            for row in existing_rows + new_rows:
                for field in fieldnames:
                    if field not in row:
                        if field == 'caseSensitive':
                            row[field] = 'false'
                        elif field == 'codeProcessor':
                            row[field] = ''
                        else:
                            row[field] = ''
            
            # Write updated coding_systems.csv preserving original order
            with open(registry_file, 'w', newline='') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for row in existing_rows:
                    writer.writerow(row)
                for row in new_rows:
                    writer.writerow(row)
            
            final_count = len(existing_rows) + len(new_rows)
            logger.info(f"Successfully updated coding systems registry")
            logger.info(f"Original entries: {original_count}, Final entries: {final_count}")
            if updated_keys:
                logger.info(f"Updated {len(updated_keys)} existing entries: {sorted(updated_keys)}")
            if new_rows:
                logger.info(f"Added {len(new_rows)} new entries: {[ (r['name'], r['type']) for r in new_rows ]}")
            
        except Exception as e:
            logger.error(f"Error updating coding systems registry: {e}")
            raise AirflowException(f"Failed to update coding systems registry: {e}")
    
    def _create_github_pr(self, branch_name: str, commit_message: str):
        """
        Create a GitHub pull request for the coding systems changes.
        
        Args:
            branch_name: Name of the branch to create PR from
            commit_message: Commit message to use as PR title
        """
        if not self.private_key:
            logger.info("GitHub token not provided, skipping PR creation")
            return
            
        logger.info(f"Creating GitHub PR for branch: {branch_name}")
        
        try:
            pr = ADIPGitRepoManager.create_pull_request_from_git_repo(
                title=commit_message,
                head=branch_name,
                base=self.git_default_branch,
                body=f"Automated coding systems update for {self.dataset}-{self.client} revision {self.revision}",
                git_repo=self.git_meta_repo,
                git_token=self.private_key,
            )
            logger.info(f"Successfully created PR: {pr.html_url}")
            
        except Exception as e:
            logger.warning(f"Failed to create GitHub PR: {e}")
    
    def _copy_coding_systems_to_s3(self, tmp_meta_folder: str):
        """
        Copy the coding_systems.csv file to S3 artifacts location.
        
        Args:
            tmp_meta_folder: Path to the cloned metadatastore repository
        """
        logger.info("Copying coding_systems.csv to S3 artifacts")
        
        try:
            # Source file path
            registry_file = Path(tmp_meta_folder) / META_DATA_STORE_PATH / VERSIONED_PATH / CODING_PATH / CODING_SYSTEMS_FILE
            
            if not registry_file.exists():
                logger.warning("coding_systems.csv not found, skipping S3 copy")
                return
            
            # Destination S3 path using proper path joining
            s3_dest_path = build_s3_path(self.etl_path, "artifacts", "coding_systems.csv")
            
            # Copy file to S3
            self.fs.put(str(registry_file), s3_dest_path)
            logger.info(f"Successfully copied coding_systems.csv to S3: {s3_dest_path}")
            
        except Exception as e:
            logger.warning(f"Failed to copy coding_systems.csv to S3: {e}")
    
    def _process_coding_system(self, coding_system: CodingSystem, tmp_meta_folder: str) -> None:
        """
        Process a single coding system.
        
        Args:
            coding_system: CodingSystem object from RDC
            tmp_meta_folder: Path to the cloned metadatastore repository
        """
        coding_system_name = coding_system.name
        logger.info(f"Processing coding system: {coding_system_name}")
        
        # Check if this is a new or existing coding system
        dataset_client_folder = f"{self.dataset}_{self.client}"
        coding_system_folder = coding_system.metadata_folder or coding_system.name.lower()
        coding_base_path = Path(tmp_meta_folder) / META_DATA_STORE_PATH / VERSIONED_PATH / CODING_PATH / dataset_client_folder / coding_system_folder
        
        if coding_base_path.exists():
            # Existing coding system - update
            self._update_existing_coding_system(coding_system, coding_base_path)
        else:
            # New coding system - create
            self._create_new_coding_system(coding_system, coding_base_path)
    
    def _update_existing_coding_system(self, coding_system: CodingSystem, coding_base_path: Path) -> None:
        """
        Update an existing coding system.
        
        Args:
            coding_system: CodingSystem object from RDC
            coding_base_path: Base path for the coding system in metadatastore
        """
        coding_system_name = coding_system.name
        logger.info(f"Updating existing coding system: {coding_system_name}")
        
        # Find the last revision (folder with greatest date)
        revision_folders = [d for d in coding_base_path.iterdir() if d.is_dir() and d.name != BASE_FOLDER_NAME]
        if not revision_folders:
            raise AirflowException(f"No revision folders found for {coding_system_name}")
        
        # Sort by date (folder names are dates)
        revision_folders.sort()
        last_revision_folder = revision_folders[-1]
        
        # Check if we're overwriting the same revision
        if last_revision_folder.name == self.revision:
            logger.info(f"Overwriting existing revision {self.revision} for {coding_system_name}")
            revision_folder = last_revision_folder
            # Find existing JSON file in the revision folder
            coding_system_folder = coding_system.metadata_folder or coding_system.name.lower()
            expected_json_name = f"{coding_system_folder}_{self.revision}.json"
            json_file = revision_folder / expected_json_name
            
            if not json_file.exists():
                # Fallback: look for any JSON file in the revision folder
                json_files = list(revision_folder.glob("*.json"))
                if not json_files:
                    raise AirflowException(f"No JSON file found in revision: {revision_folder}")
                json_file = json_files[0]
                logger.warning(f"Could not find expected JSON file {expected_json_name}, using: {json_file.name}")
            
            # Copy CSV file from S3 first so we can read its headers
            csv_s3_path = build_s3_path(self.etl_path, "coding_systems", f"{coding_system_name}.csv")
            csv_local_path = revision_folder / f"{coding_system_folder}_{self.revision}.csv"
            
            try:
                self.fs.get(csv_s3_path, str(csv_local_path))
                logger.info(f"Successfully downloaded {csv_s3_path} to {csv_local_path}")
            except Exception as e:
                raise AirflowException(f"Failed to copy CSV file from S3: {e}")
            
            # Read CSV headers and validate required columns exist
            csv_columns = self._read_csv_headers(csv_local_path)
            
            # Validate that required columns exist in CSV
            self._validate_csv_columns_exist(csv_columns, coding_system)
            
            # Update the instance resource file with correct column information
            self._update_instance_resource_file_with_columns(json_file, self.revision, csv_columns, coding_system)
            
            # Validate CSV columns against instance resource file
            self._validate_csv_columns(csv_local_path, json_file)
            
            # Compress the CSV file
            compressed_csv_path = self._compress_file(csv_local_path)
            logger.info(f"Compressed CSV file: {compressed_csv_path}")
        else:
            # Create new revision folder
            revision_folder = coding_base_path / self.revision
            revision_folder.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created new revision folder: {revision_folder}")
            
            # Look for the correct instance resource file in the last revision folder
            # The instance resource file should have the revision in its name
            coding_system_folder = coding_system.metadata_folder or coding_system.name.lower()
            expected_json_name = f"{coding_system_folder}_{last_revision_folder.name}.json"
            json_file = last_revision_folder / expected_json_name
            
            if not json_file.exists():
                # Fallback: look for any JSON file in the last revision folder
                json_files = list(last_revision_folder.glob("*.json"))
                if not json_files:
                    raise AirflowException(f"No JSON file found in last revision: {last_revision_folder}")
                json_file = json_files[0]
                logger.warning(f"Could not find expected JSON file {expected_json_name}, using: {json_file.name}")
            
            # Create new JSON file with current revision
            new_json_name = f"{coding_system_folder}_{self.revision}.json"
            new_json_file = revision_folder / new_json_name
            
            shutil.copy2(json_file, new_json_file)
            logger.info(f"Copied JSON file: {json_file} -> {new_json_file}")
            json_file = new_json_file
            
            # Update the instance resource file with new revision information
            self._update_instance_resource_file(json_file, last_revision_folder.name)
            
            csv_s3_path = build_s3_path(self.etl_path, "coding_systems", f"{coding_system_name}.csv")
            # Use revision in the filename: [coding_system_folder]_[revision].csv
            csv_local_path = revision_folder / f"{coding_system_folder}_{self.revision}.csv"
            
            try:
                self.fs.get(csv_s3_path, str(csv_local_path))
                logger.info(f"Successfully downloaded {csv_s3_path} to {csv_local_path}")
            except Exception as e:
                raise AirflowException(f"Failed to copy CSV file from S3: {e}")
            
            # Read CSV headers and validate required columns exist
            csv_columns = self._read_csv_headers(csv_local_path)
            
            # Validate that required columns exist in CSV
            self._validate_csv_columns_exist(csv_columns, coding_system)
            
            # Validate CSV columns against instance resource file
            self._validate_csv_columns(csv_local_path, json_file)
            
            # Compress the CSV file
            compressed_csv_path = self._compress_file(csv_local_path)
            logger.info(f"Compressed CSV file: {compressed_csv_path}") 
    
    def _create_new_coding_system(self, coding_system: CodingSystem, coding_base_path: Path) -> None:
        """
        Create a new coding system.
        
        Args:
            coding_system: CodingSystem object from RDC
            coding_base_path: Base path for the coding system in metadatastore
        """
        coding_system_name = coding_system.name
        coding_system_folder = coding_system.metadata_folder or coding_system.name.lower()
        logger.info(f"Creating new coding system: {coding_system_name}")
        
        # Create base folder
        coding_base_path.mkdir(parents=True, exist_ok=True)
        
        # Create revision folder
        revision_folder = coding_base_path / self.revision
        revision_folder.mkdir(parents=True, exist_ok=True)
        logger.info(f"Created revision folder: {revision_folder}")
        
        csv_s3_path = build_s3_path(self.etl_path, "coding_systems", f"{coding_system_name}.csv")
        # Use revision in the filename: [coding_system_folder]_[revision].csv
        csv_local_path = revision_folder / f"{coding_system_folder}_{self.revision}.csv"
        
        try:
            self.fs.get(csv_s3_path, str(csv_local_path))
            logger.info(f"Successfully downloaded {csv_s3_path} to {csv_local_path}")
        except Exception as e:
            raise AirflowException(f"Failed to copy CSV file from S3: {e}")
        
        # Read CSV headers
        csv_columns = self._read_csv_headers(csv_local_path)
        logger.info(f"Found CSV columns: {csv_columns}")
        
        # Validate that required columns exist in CSV
        self._validate_csv_columns_exist(csv_columns, coding_system)
        
        # Create base resource file with attributes based on CSV columns
        self._create_base_resource_file(coding_system, coding_base_path, csv_columns)
        
        # Create instance resource file
        self._create_instance_resource_file(coding_system, revision_folder, csv_columns)
        
        # Validate CSV columns against instance resource file
        self._validate_csv_columns(csv_local_path, revision_folder / f"{coding_system_folder}_{self.revision}.json")
        
        # Compress the CSV file
        compressed_csv_path = self._compress_file(csv_local_path)
        logger.info(f"Compressed CSV file: {compressed_csv_path}") 