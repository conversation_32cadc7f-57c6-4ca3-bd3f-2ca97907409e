from datetime import date, datetime

import re
import logging
from os import path
from pathlib import Path
from typing import Dict, Tu<PERSON>, Optional, List
from inspect import getattr_static
from urllib.parse import urlsplit

logger = logging.getLogger(__name__)

Y_M_D = '%Y-%m-%d'
DATA_SPEC = "data_specification.xlsx"


def dict2list(di: Dict):
    return [v for v in di.values()]

def merge_dict(source, dest):
    if not source:
        return
    if type(source) is not dict or type(dest) is not dict:
        raise Exception(f'failed to merge {source} and {dest} - not both are dict')

    for key in source:
        if key not in dest or type(source[key]) is not dict:
            dest[key] = source[key]
        else:
            merge_dict(source[key], dest[key])


def find_prev_revision_in_fs(fs, source_path: str, revision: str) -> Optional[str]:
    logger.info(f'looking for the previous revision after {revision} at {source_path}')

    if not fs.exists(source_path):
        return None

    scheme, _, local_file_path, _, _ = urlsplit(source_path)
    if scheme == 'file':  # workaround to enable testing without S3
        sub_dirs = [e.name for e in Path(local_file_path).iterdir() if e.is_dir()]
    else:  # default to previous behaviour
        sub_dirs = list(
            map(lambda p: path.split(p['Key'])[1],
                filter(lambda p: p['StorageClass'] == 'DIRECTORY',
                       fs.ls(source_path, detail=True))))
    logger.info(f'sub directories found at {source_path}\n{sub_dirs}')
    most_recent_revision = find_prev_revision(revision, sub_dirs)
    logger.info(f'most recent revision is {most_recent_revision}')
    return most_recent_revision


def find_prev_revision(new_revision, all_revisions):
    alpha_prefix_match = re.search("^([A-Z,a-z]+)", new_revision)

    def filter_func(r):
        beta_prefix_match = re.search("^([A-Z,a-z]+)", r)

        if alpha_prefix_match:
            if beta_prefix_match:
                return beta_prefix_match.group(1) == alpha_prefix_match.group(1) and r < new_revision
            else:
                False
        else:
            return r < new_revision

    prev_revisions = list(filter(filter_func, all_revisions))

    return max(prev_revisions) if prev_revisions else None


def get_pipeline_name(client, dataset, is_k8s = False):
    """
    :param client: client name
    :param dataset: dataset name
    :return: airflow pipeline name
    """

    if is_k8s:
        return '{}-{}-Data-Ingestion-Pipeline_k8s'.format(client, dataset)
    else:
        return '{}-{}-Data-Ingestion-Pipeline'.format(client, dataset)


def get_info_from_etl_bucket(domain, upload_bucket) -> Tuple[str, str, str]:
    """
    Derive pipeline information from etl bucket

    :param domain: s3 bucket domain
    :param upload_bucket: downstream pipeline etl path
    :return: client name, dataset name, and revision
    """
    return get_info_from_bucket(domain, 'etl', upload_bucket)


def get_info_from_upload_bucket(domain, upload_bucket):
    """
    Derive pipeline information from upload bucket

    :param domain: s3 bucket domain
    :param upload_bucket: downstream pipeline upload path
    :return: client name, dataset name, and revision
    """

    # format of the upload buckets should ALWAYS be s3://<client>.aetion.com/upload/<dataset>/<revision>
    # if client and dataset are equal, then it is a generic connector.

    return get_info_from_bucket(domain, 'upload', upload_bucket)


def get_info_from_bucket(domain, subdir, bucket_path):
    """
    Derive pipeline information from a bucket path of the following form:
    s3://<client>.aetion.com/[etl|upload]/<dataset>/<revision>
    Logic migrated to ../../adip/adip_common/dags_configuration/classes/data_ingestion_pipeline_config.py

    :param subdir: whether an upload/ or etl/ path is being passed in
    :param domain: s3 bucket domain
    :param bucket_path: downstream pipeline upload/ or etl/ path
    :return: client name, dataset name, and revision
    """

    # this function could actually be made somewhat more generic by just validating that subdir is either "etl"
    # or "upload"
    matches = re.search(f'(s3://)?(.*?).{domain}/{subdir}/(.*?)/(.*)', bucket_path)
    if not matches:
        raise UnconventionalPathException(domain, subdir, bucket_path)
    client = matches.group(2)
    dataset = matches.group(3)
    revision = matches.group(4)
    return client, dataset, revision


def extract_gdr_from_spec(spec) -> (datetime.date, datetime.date):
    """
    Extracts GDR start & end from a DBC spec.
    :param spec: A file-like object opened in read binary mode pointing to an .xlsx workbook having a sheet named 'Data Range' with
    IS8601 formatted dates in cells C4 (GDR start) and C5 (GDR end)
    :return: a (date, date) tuple with GDR start & end dates.
    """

    # TODO: Move this to the top of the file when the image is rebuilt
    from openpyxl import load_workbook

    wb = load_workbook(filename=spec, read_only=True)
    sheet_ranges = wb['Data Range']
    return sheet_ranges['C4'].value.date(), sheet_ranges['C5'].value.date()


class UnconventionalPathException(Exception):
    """
    Used to signal that an s3 path is not in the expected s3://<client>.aetion.com/[etl|upload]/<dataset>/<revision> form
    """
    def __init__(self, domain: str, subdir: str, path: str):
        super().__init__(f'Failed to match {subdir} bucket for {domain} to path {path}')


def lazy(until: List[str] = []):
    """
    Class decorator for lazy initialization

    When a class is decorated with @lazy, it will wait to do it's __init__()
    until an attribute is accessed (that is not 'args', 'kwargs', or 'proxy').
    The attribute to wait on can be specified in an 'until' argument. If no
    attributes are specified, the object will initialize on the first attribute
    access.

    :param until: A list of attributes that we should wait for access before
        calling __init__.
    :type until: Container[str]
    :return: function that can be called with a class to return a decorated
        class.
    :rtype: Callable[[type],type]

    Example 1: decorate a class
    >>> from tools.task_funcs import lazy
    >>> from time import sleep
    >>> @lazy
    >>> class LongInitClass:
    ...     def __init__(self):
    ...         print("init called!")
    ...         sleep(5)
    ...         print("init done!")
    ...     def foo(self):
    ...         print("foo called!")
    ...
    >>> lic = LongInitClass() # __init__ doesn't happen here
    >>> lic.foo() # It happens on access to .foo
    init called!
    init done!
    foo called!
    >>>

    Example 2: decorate a class you can't modify
    >>> from tools.task_funcs import lazy
    >>> from airflow.operators.dummy_operators import DummyOperator
    >>> LazyDummyOperator = lazy()(DummyOperator) # Create a new class
    >>> # or
    >>> LazyDummyOperator = lazy(until=['execute'])(DummyOperator)
    >>> ...
    >>> lazy_task = LazyDummyOperator(
    ...     task_id="start"
    ...     dag = dag
    ... )
    >>>

    Example 3: lazy() will work unusually with subclassing, lazy must be on the
    leaf class in the hierarchy
    >>> from tools.task_funcs import lazy
    >>> @lazy()
    ... class A:
    ...     def __init__(self):
    ...         print("init called!")
    ...     def foo(self):
    ...         print("foo called!")
    ...     def bar(self):
    ...         print("bar called!")
    ...
    >>> class B(A):
    ...     def foo(self):
    ...         print("b's foo called!")
    ...
    >>> b = B()
    >>> b.foo()
    b's foo called!
    >>> b.bar()
    init called!
    bar called!
    >>>
    """

    def until_wrapper(cls: type):
        """
        Wrapper for the class call

        :param cls: The class to decorate
        :type cls: type
        :return: The decorated class
        :rtype: type
        """

        def _init(self, *args, **kwargs):
            self.args = args
            self.kwargs = kwargs
            self.proxy = None

        def _getattr(self, key):
            if self.proxy is None:
                if key in until or len(until) == 0:
                    self.proxy = cls(*self.args, **self.kwargs)
            if hasattr(self.proxy, key):
                return getattr(self.proxy, key)
            if hasattr(cls, key):
                return getattr(cls, key)
            raise AttributeError(f"No attribute {key} on {self.proxy}")

        class_dict = {
            attr: getattr(cls, attr)
            for attr in dir(cls)
            if isinstance(getattr_static(cls, attr), staticmethod)
        }  # Copy any static methods from the parent class
        class_dict.update(
            {
                "__init__": _init,
                "__getattr__": _getattr,
                "__doc__": cls.__doc__,
                "__class__": cls,
            }
        )
        newtype = type(f"Lazy{cls}", tuple(), class_dict)
        return newtype

    return until_wrapper

