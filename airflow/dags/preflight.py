#!/usr/bin/env python3

"""
Checks that this airflow instance can connect to all the things we expect for
a data ingestion pipeline.

We check:
    * pinging google's DNS servers (check if we're connected to the internet)
    * pinging the server configured in the `databricks_default` connection
      * using the `databricks_default` connection, run a simple command
        * using the `databricks_default` connection, run a complex command that
          will cause databricks to access s3
    * we can access the s3 bucket specified in the variable `artifacts_path`
    * we can clone the repository specified in the `git_repo` airflow variable
    * we can clone the repository specified in the `git_meta_repo` airflow variable
Finally, we have a task that fails with the message "Failure message delivery"
to check that airflow's failure message delivery works (email or slack, usually)

Postgres and Redis connectivity is checked as part of Airflow's setup, and we
won't even be able to parse this dag unless those two are working. They are
not tested in this dag.
"""

# STD
import logging
import os
import tempfile
import urllib.request
from urllib.parse import urlunsplit

# Airflow
import pendulum
from airflow.exceptions import AirflowException, AirflowFailException
from airflow.hooks.base import BaseHook
from airflow.models import Variable
from airflow.models.dag import DAG
from airflow.operators.bash import BashOperator
from airflow.operators.python import PythonOperator
from airflow.operators.python import ShortCircuitOperator
from airflow.providers.slack.hooks.slack import SlackHook

from aetion.adip.config.isolated_python_deps_feature import PREFLIGHT_ISOLATED_PYTHON_DEPS_FEATURE_KEY, IsolatedPythonDepsFeature
from aetion.adip.config.spark_monitoring import SparkMonitoringFeature
from operators.celeborn import CelebornFeature
from operators.kubernetes import SparkKubernetesOperatorExt

from paramiko.client import SSHClient, AutoAddPolicy
from paramiko.rsakey import RSAKey
from paramiko.ssh_exception import AuthenticationException

from operators.kubernetes_callbacks import handle_post_execute
from tools.dag_funcs import skip, resolve_in_cluster_param
from tools.config import (
    databricks_libraries_from_artifacts,
    resolve_databricks_libraries,
)
from hooks.git_hook import GitHook
from operators.aws import walk_all, fs_factory_default
from operators.slack import create_success_slack_notification, create_task_slack_alert
# Custom
from operators.databricks import DatabricksSubmitRunOperatorExt
from pipelines.helpers import get_dag_default_args
from airflow.providers.cncf.kubernetes.pod_generator import make_safe_label_value
from aetion.adip.constants import (
    PREFLIGHT_SPARK_MONITORING_FEATURE_KEY,
    SPARK_MONITORING_FEATURE_KEY
)

from tools.log4j2_feature import LOG4J2_CONFIG_KEY, LOG4J2_ENABLE_KEY, Log4j2Feature
from aetion.adip.pipelines.preflight.celeborn import PreflightCelebornTaskContext

ENABLE_K8S_CHECKS = Variable.get('enable_preflight_k8s_checks', 'false')
ADIP_INSTANCE_ID = Variable.get('adip_instance_id', 'no-adip-id-likely-to-fail')
K8S_SERVICE_ACCOUNT = Variable.get('preflight_k8s_service_account', f"spark-operator-{ADIP_INSTANCE_ID}")

STALE_PENDING_EXECUTORS_THRESHOLD=Variable.get('preflight_stale_pending_executors_threshold', default_var=15)
""" Default number of seconds to wait for pending executors before failing the job."""
DEFAULT_ADIP_SPARK_IMAGE = '************.dkr.ecr.us-east-1.amazonaws.com/adip-spark:2476559'
PREFLIGHT_ADIP_SPARK_IMAGE = Variable.get('preflight_adip_spark_image', default_var=DEFAULT_ADIP_SPARK_IMAGE)
DEFAULT_DATABRICKS_SPARK_VERSION = '14.3.x-scala2.12'
""" Default Spark version to use in Databricks jobs."""

# log4j2_feature feature
GLOBAL_LOG4J2_FEATURE = Log4j2Feature(
    settings=Variable.get(LOG4J2_CONFIG_KEY, default_var=None),
    enabled=Variable.get(LOG4J2_ENABLE_KEY, default_var=True)
)

logging.info(f"Global log4j2_feature feature is {'enabled' if GLOBAL_LOG4J2_FEATURE.enabled else 'disabled'}")

PREFLIGHT_LOG4J2_FEATURE = Log4j2Feature(
                    settings = Variable.get("preflight_log4j2_config", default_var=None),
                    enabled = Variable.get("preflight_log4j2_enable", default_var=True)
                    )

PREFLIGHT_LOG4J2_FEATURE = PREFLIGHT_LOG4J2_FEATURE.combine(GLOBAL_LOG4J2_FEATURE)
logging.info(f"Preflight log4j2_feature feature is {'enabled' if PREFLIGHT_LOG4J2_FEATURE.enabled else 'disabled'}")

GLOBAL_SPARK_MONITORING_FEATURE = SparkMonitoringFeature(**Variable.get(SPARK_MONITORING_FEATURE_KEY, deserialize_json=True, default_var={}))
PREFLIGHT_SPARK_MONITORING_FEATURE = SparkMonitoringFeature(**Variable.get(PREFLIGHT_SPARK_MONITORING_FEATURE_KEY, deserialize_json=True, default_var={}))
EFFECTIVE_SPARK_MONITORING_FEATURE = GLOBAL_SPARK_MONITORING_FEATURE.combine(PREFLIGHT_SPARK_MONITORING_FEATURE)
logging.info(f"Preflight spark monitoring is {'enabled' if EFFECTIVE_SPARK_MONITORING_FEATURE.enabled else 'disabled'}")

EFFECTIVE_ISOLATED_PYPTHON_DEPS_FEATURE = IsolatedPythonDepsFeature(**Variable.get(PREFLIGHT_ISOLATED_PYTHON_DEPS_FEATURE_KEY, deserialize_json=True, default_var={}))
logging.info(f"Preflight isolated python deps feature is {'enabled' if EFFECTIVE_ISOLATED_PYPTHON_DEPS_FEATURE.enabled else 'disabled'}")

environment = os.getenv("DDS_ENVIRONMENT", "prod")

def check_connectivity(dag: DAG, task_id: str, hostname: str):
    """
    Create an operator that "pings" an url (makes an HTTP(s) request or attempts to establish an SSH connection),
    operator fails with exception if it cannot ping.

    :param dag: The dag this operator is a part of
    :type dag: airflow.models.dag.DAG
    :param task_id: The id of the task in the dag (no spaces)
    :type task_id: str
    :param hostname: A url that we want to ping
    :type hostname: str
    :return: A python operator that pings the endpoint, and fails if the ping
             fails.
    :treturn: airflow.models.baseoperator.BaseOperator
    """

    # Oh my gosh I just want to ping why do I have to reimplement this?
    # (It's because debian-slim images don't have ping, dig, or any other
    # networking tools installed, all we have it getent, which only does
    # dns lookups, doesn't check that the host is up).
    # also, ping requires root privileges, which we better not take for granted
    # also, we talk with databricks & Gitea via HTTPS, so that's what we actually need to test
    # (we currently don't have an implementation for SSH git access)
    def call(endpoint):
        schemes = {
            'ssh': check_ssh,
            'http': check_http,
            'https': check_http,
        }

        if not endpoint:
            skip("Skipping access_internet check since variable 'preflight_example_http_endpoint' is not set")

        # try to be lenient unto what we are passed
        url = urllib.parse.urlparse(endpoint)
        if url.scheme in schemes.keys():
            schemes[url.scheme](url)
        elif not url.scheme:
            logging.warning(f"No scheme in {endpoint}. Will attempt https.")
            check_http(urllib.parse.urlparse(f"https://{endpoint}"))
        else:
            logging.warning(f"Don't know how to test for {url.scheme}."
                            f"Marking the check as success to see how far we can get.")

    def check_ssh(url):
        logging.info(f"Will try to connect to {url} (without credentials)")
        try:
            client = SSHClient()
            # it's either this or emitting a warning that will cause work
            client.set_missing_host_key_policy(AutoAddPolicy)
            # use a random key and expect it to fail
            client.connect(hostname=str(url.hostname), port=url.port or 22, pkey=RSAKey.generate(1024), timeout=3)
        except AuthenticationException:
            logging.info("Expected auth failure")
        except Exception as e:
            raise AirflowFailException(f"Could not connect to {url} because {e.__cause__}") from e

    def check_http(url):
        test_url = urlunsplit([url.scheme, url.netloc.split('@')[-1], '', '', ''])
        logging.info(f"Will try a simple HTTP 1.1 GET to {test_url}")
        try:
            request = urllib.request.Request(test_url)
            with urllib.request.urlopen(request) as r:
                logging.info(f"Got {r.status}/{r.reason} from server & the following headers: {r.getheaders()}.")
        except urllib.error.HTTPError as e:
            logging.warning(f"caught HTTPError {e}")
        except Exception as e:
            raise AirflowFailException(
                f"Failed to get a response from {test_url} while performing "
                f"an http request because of {e}."
            ) from e

    return PythonOperator(
        dag=dag,
        task_id=task_id,
        python_callable=call,
        trigger_rule="none_failed",
        op_args=[hostname]
    )


def nonempty(dag: DAG, task_id: str, obj: str, err: Exception):
    """
    Create an operator that checks that one or more strings are not empty.
    Operator fails if any string is empty.

    :param dag: The dag this operator is a part of
    :type dag: airflow.models.dag.DAG
    :param task_id: The id of the task in the dag (no spaces)
    :type task_id: str
    :param err: exception to throw
    :type err: Exception
    :param obj: string to check. May be templated.
    :type obj: str
    """

    def call(arg):
        if not arg:
            raise err

    return PythonOperator(
        dag=dag,
        task_id=task_id,
        python_callable=call,
        trigger_rule="none_failed",
        op_args=[obj]
    )


def basic_databricks(dag: DAG, task_id: str):
    """
    Create an operator that submits a run to the databricks run api. If it fails
    with a "missing notebook" message, it means airflow successfully authenticated
    with databricks.
    :param dag: The dag this operator is a part of
    :type dag: airflow.models.dag.DAG
    :param task_id: The id of the task in the dag (no spaces)
    :type task_id: str
    :param arr: an array of strings to check. May be templated.
    :type arr: list[str]
    """

    def call():
        json = {
            "name": "airflow-test-simple",
            "tasks": [
                {
                    "task_key": "airflow_simple_task",
                    "new_cluster": {
                        'spark_version': DEFAULT_DATABRICKS_SPARK_VERSION,
                        'spark_env_vars': {
                            'JNAME': 'zulu17-ca-amd64'
                        },
                        'node_type_id': 'c5.xlarge',
                        'aws_attributes': {'availability': 'ON_DEMAND'},
                        'num_workers': 1,
                    },
                    "notebook_task": {
                        "notebook_path": "/Missing Notebook",
                    }
                }
            ]
        }
        json["tasks"][0]["new_cluster"].update(
            Variable.get(
                "preflight_aws_attributes",
                default_var={},
                deserialize_json=True
            )
        )
        op = DatabricksSubmitRunOperatorExt(
            dag=dag,
            task_id="simple_databricks_job",
            do_xcom_push=False,
            json=json
        )
        try:
            op.execute({})
        except AirflowException as e:
            if "Unable to access the notebook \"/Missing Notebook\"" not in str(e):
                # If we get any other error, it may have been a problem
                # between airflow and databricks.
                raise AirflowFailException(
                    "While trying to submit a job to the databricks jobs api, "
                    "we got an unexpected error"
                ) from e

    return PythonOperator(
        dag=dag,
        task_id=task_id,
        python_callable=call,
        trigger_rule="none_failed"
    )


def clone(dag: DAG, task_id: str, repo_url: str, branch: str):
    """
    Create an operator that clones a repository.

    :param dag: The dag this operator is a part of
    :type dag: airflow.models.dag.DAG
    :param task_id: The id of the task in the dag (no spaces)
    :type task_id: str
    :param repo_url: The url for the git repository
    :type repo_url: str
    :param branch: The branch to check out after cloning
    :type branch: str
    """

    def call(repo_url, branch):
        # uhm, this looks sorta fishy...
        gh = GitHook(repo_url, os.getenv('GIT_PRIVATE_KEY', None))
        with tempfile.TemporaryDirectory() as wd:
            gh.clone(wd, branch)

    return PythonOperator(
        dag=dag,
        task_id=task_id,
        python_callable=call,
        trigger_rule="none_failed",
        op_args=[repo_url, branch]
    )


def complex_databricks(dag: DAG, task_id: str):
    """
    Create an operator that submits a complex databricks job
    Try accessing an s3 bucket through databricks, along with the libraries
    we need to run the pipeline.
    :param dag: The dag the DatabricksSubmitRunOperator should belong to
    :type dag: airflow.models.dag.DAG
    :param task_id: The task id of the operator that gets returned
    :type task_id: str
    :return: An airflow operator that runs a databricks job:
    :treturn: DatabricksSubmitRunOperatorExt
    """

    def call():
        # Set the variable preflight_aws_attributes to a value like
        # preflight_attributes = {
        #     "aws_attributes": {
        #         "availability": "SPOT_WITH_FALLBACK",
        #         "ebs_volume_count": "5",
        #         "ebs_volume_size": "100",
        #         "ebs_volume_type": "GENERAL_PURPOSE_SSD",
        #         "first_on_demand": "1",
        #         "instance_profile_arn": "your-arn",
        #         "spot_bid_price_percent": "110",
        #         "zone_id": "auto" # should be 'auto' for E2, or a zone for legacy databricks
        #     }
        # }
        # Make sure the "instance_profile_arn" is an arn as shown in the
        # databricks instance profile list (found in the databricks admin page)
        json = {
            "name": "airflow-test-complex",
            "tasks": [
                {
                    "task_key": "airflow_complex_task",
                    "new_cluster": {
                        'spark_version': DEFAULT_DATABRICKS_SPARK_VERSION,
                        'spark_env_vars': {
                            'JNAME': 'zulu17-ca-amd64'
                        },
                        'node_type_id': 'c5.xlarge',
                        'aws_attributes': {
                            'availability': 'ON_DEMAND',
                            'ebs_volume_count': 1,
                            'ebs_volume_size': 32,
                            'zone_id': 'auto'
                        },
                        'num_workers': 1,
                        'custom_tags': [
                            {'key': key, 'value': value}
                            for key, value in dict(
                                Task='airflow-test-complex',
                                Client='aetion',
                                Environment='data',
                                Dataset='preflight',
                                Revision='19700101',
                            ).items()
                        ]
                    },
                    "spark_python_task": {
                        "python_file": f"{ARTIFACTS_PATH}/generic_runner.py",
                        "parameters": [
                            "unarchiver.unarchiver",
                            os.path.join(ARTIFACTS_PATH, "coding_systems.csv"),
                            os.path.join(ARTIFACTS_PATH, "preflight_tmp"),
                            ""
                        ]
                    },
                    "libraries": resolve_databricks_libraries(
                        databricks_libraries_from_artifacts(ARTIFACTS),
                        artifacts_path=ARTIFACTS_PATH, spark_version=DEFAULT_DATABRICKS_SPARK_VERSION
                    )
                }
            ]
        }
        json["tasks"][0]["new_cluster"].update(
            Variable.get(
                "preflight_aws_attributes",
                default_var={},
                deserialize_json=True
            )
        )
        op = DatabricksSubmitRunOperatorExt(
            dag=dag,
            task_id="complex_databricks_job_run",
            do_xcom_push=False,
            json=json
        )
        logging.info(f"json: {op.json}")
        op.execute({})

    return PythonOperator(
        dag=dag,
        task_id=task_id,
        python_callable=call,
        trigger_rule="none_failed"
    )


def fail(dag: DAG, task_id: str, msg: str):
    """
    Returns a task that always raises an exception.
    :param dag: The dag the task should belong to
    :type dag: airflow.models.dag.DAG
    :param task_id: The name of the task
    :type task_id: str
    :param msg: The exception message we should raise
    :type msg: str
    """

    def call(message):
        raise AirflowFailException(message)

    return PythonOperator(
        dag=dag, task_id=task_id,
        python_callable=call,
        op_args=[msg]
    )


def walk_s3(dag: DAG, task_id: str, path: str):
    """
    Returns a task that walks an s3 location to check for read (list) permissions
    :param dag: The dag the task should belong to
    :type dag: airflow.models.dag.DAG
    :param task_id: The name of the task
    :type task_id: str
    :param path: The s3 path we should walk
    :type path: str
    """

    def call(p_path):
        fs = fs_factory_default()
        walk_all(p_path, fs)

    return PythonOperator(
        dag=dag, task_id=task_id,
        python_callable=call,
        trigger_rule="none_failed",
        op_args=[path]
    )


def slack_configured():
    """
    Checks if airflow has configured the default slack connection
    """
    t = SlackHook().test_connection()
    return "isn't defined" not in t[1]


# Fetch configuration from Airflow variables and environment variables
AIRFLOW_BASE_URL = Variable.get("base_url")

# slack
SLACK_TOKEN = os.environ.get('SLACK_TOKEN', None)
NOTIFICATION_SLACK_CHANNEL = Variable.get("slack_channel")
# if unset, slack alerts for failures will not be sent
PREFLIGHT_FAILURE_SLACK_CHANNEL = Variable.get(
    "preflight_failure_slack_channel",
    default_var=None
)

# artifacts
ARTIFACTS_PATH = Variable.get("artifacts_path")
ARTIFACTS = Variable.get(
    "artifacts",
    deserialize_json=True
)

failure_alert = create_task_slack_alert(
    PREFLIGHT_FAILURE_SLACK_CHANNEL,
    AIRFLOW_BASE_URL,
    None
)

# Create preflight DAG
with DAG(
    dag_id="preflight_checks",
    default_args=get_dag_default_args(failure_alert if PREFLIGHT_FAILURE_SLACK_CHANNEL != None else None),
    # Preflight can be run occasionally to check we still have access to everything, see
    # https://airflow.apache.org/docs/apache-airflow/stable/dag-run.html#cron-presets
    schedule=Variable.get("preflight_schedule", "@daily") or None,
    catchup=False,
    user_defined_macros={
        'make_safe_label_value': make_safe_label_value
    },

) as dag:

    basic_check = check_connectivity(
        dag,
        "access_internet",
        Variable.get("preflight_example_http_endpoint", default_var=None)
    )

    # Check databricks
    databricks_set = nonempty(
        dag,
        "databricks_connection",
        BaseHook.get_connection('databricks_default'),
        Exception("The databricks_default connection must be set.")
    )
    artifacts_set = nonempty(
        dag,
        "artifacts_path_set",
        "{{ var.value.artifacts_path }}",
        Exception(
            "The artifacts_path variable must be set to an s3 location that "
            "contains the 'spark-connector', 'unarchiver', 'spark-validator', "
            "and 'adip_utilities' artifacts."
        )
    )
    databricks_complex = complex_databricks(dag, "check_databricks_s3_connection")
    ping_databricks = check_connectivity(
        dag,
        "databricks_host_visible",
        "{{ conn.get('databricks_default').host }}"
    )
    run_databricks_simple = basic_databricks(dag, "check_databricks_execute")
    basic_check >> databricks_set >> ping_databricks >> run_databricks_simple
    [run_databricks_simple, artifacts_set] >> databricks_complex

    # Check our git repos
    git_private_key = BashOperator(
        dag=dag, task_id="git_private_key",
        bash_command="echo $(echo $GIT_PRIVATE_KEY | wc -c)",
        do_xcom_push=True
    )
    git_private_key_nonempty = nonempty(
        dag,
        "git_private_key_envvar_set",
        "{{ ti.xcom_pull('git_private_key') }}",
        Exception(
            "The 'GIT_PRIVATE_KEY' environment variable might need to be set "
            "if the git_repo and git_meta_repo repositories require authentication. "
            "If you do not require authentication, mark this task success."
        )
    )
    git_private_key >> git_private_key_nonempty
    repo_set = nonempty(
        dag,
        "git_repo_host_variable_set",
        "{{ var.value.git_repo }}",
        Exception(
            "The 'git_repo' variable must be set to a git repository with the "
            "'connector' project."
        )
    )
    repo_branch_set = nonempty(
        dag,
        "git_branch_variable_set",
        "{{ var.value.git_branch }}",
        Exception(
            "The 'git_branch' variable must be set to a branch that exists in "
            "the 'connector' repository."
        )
    )
    ping_repo_host = check_connectivity(
        dag,
        "access_connector_repo_host",
        "{{ var.value.git_repo }}"
    )
    clone_repo = clone(
        dag=dag, task_id="clone_connector_repo",
        repo_url="{{ var.value.git_repo }}",
        branch="{{ var.value.git_branch }}"
    )
    git_private_key_nonempty >> repo_set >> ping_repo_host >> clone_repo
    repo_branch_set >> clone_repo
    meta_repo_set = nonempty(
        dag,
        "metadatastore_host_variable_set",
        "{{ var.value.git_meta_repo }}",
        Exception(
            "The 'git_meta_repo' variable must be set to a git repository "
            "with the 'metadatastore' project."
        )
    )
    ping_meta_repo_host = check_connectivity(
        dag,
        "access_metadatastore_repo_host",
        "{{ var.value.git_meta_repo }}"
    )
    clone_meta_repo = clone(
        dag=dag, task_id="clone_metadatastore_repo",
        repo_url="{{ var.value.git_meta_repo }}",
        branch="{{ var.value.get('git_default_branch','master') }}"
    )
    git_private_key_nonempty >> meta_repo_set >> ping_meta_repo_host >> clone_meta_repo

    # Check we can connect & authenticate to s3
    check_s3 = walk_s3(
        dag=dag, task_id="check_s3_connectivity",
        path="{{ var.value.artifacts_path }}"
    )
    basic_check >> check_s3

    send_test_notification = create_success_slack_notification(
        "Preflight DAG - Testing Slack Notifications",
        slack_channel=NOTIFICATION_SLACK_CHANNEL,
        base_url=AIRFLOW_BASE_URL,
        alert_user=None
    )

    test_notifications_short = ShortCircuitOperator(
        dag=dag,
        task_id='test_notifications_shortcircuit',
        python_callable=slack_configured
    )

    test_notifications = PythonOperator(
        task_id='test_notifications',
        python_callable=send_test_notification,
        dag=dag
    )
    test_notifications_short >> test_notifications

    if ENABLE_K8S_CHECKS == 'true':
        k8s_artifacts_path = ARTIFACTS_PATH.replace('s3:', 's3a:')

        test_k8s_spark_job_via_scala = SparkKubernetesOperatorExt(
            task_id="test_k8s_spark_scala_job",
            namespace="spark-operator",
            application_file="resources/k8s_preflight.yaml",
            **resolve_in_cluster_param(),
            deferrable=True,
            delete_on_termination=False,
            log_events_on_failure=False,
            log_pod_spec_on_failure=False,
            dag=dag,
            poll_interval=10,
            logging_interval=30,
            get_logs=False,
            startup_timeout_seconds=7*60,
            post_execute=handle_post_execute,
            params={
                'adip_spark_image': PREFLIGHT_ADIP_SPARK_IMAGE,
                'tailscale_tag': "tag:k8s-adip-dev" if environment != 'prod' else "tag:k8s-adip-prod",
                "specType": "Scala",
                "appName": "preflight-scala",
                "k8s_namespace": "spark-operator",
                "serviceAccount": K8S_SERVICE_ACCOUNT,
                "pyFiles": [],
                "mainClass": "com.aetion.spark.pipeline.Preflight",
                "mainApplicationFile": f"{k8s_artifacts_path}/spark-connector-assembly-1.3-SNAPSHOT.jar",
                "additionalJars": [],
                "arguments": [],
                "stale_pending_executors_threshold": STALE_PENDING_EXECUTORS_THRESHOLD,
                "log4j2_feature": PREFLIGHT_LOG4J2_FEATURE,
                "celeborn_feature": CelebornFeature(enabled=False),
                "spark_monitoring_feature": EFFECTIVE_SPARK_MONITORING_FEATURE,
                "python_path_env": None,
                "spark_conf": {**PREFLIGHT_LOG4J2_FEATURE.effective_spark_conf, **EFFECTIVE_SPARK_MONITORING_FEATURE.effective_spark_conf},
                "labels": {},
                "driver": {"cores": 1, "memory": "700m"},
                "executor": {"cores": 1, "memory": "700m", "instances": 1},
            }
        )

        check_s3 >> test_k8s_spark_job_via_scala


        spark_conf = {**PREFLIGHT_LOG4J2_FEATURE.effective_spark_conf, **EFFECTIVE_SPARK_MONITORING_FEATURE.effective_spark_conf}
        EFFECTIVE_ISOLATED_PYPTHON_DEPS_FEATURE.adapt_spark_conf(spark_conf)
        image = EFFECTIVE_ISOLATED_PYPTHON_DEPS_FEATURE.resolve_docker_image(PREFLIGHT_ADIP_SPARK_IMAGE)
        test_k8s_spark_job_via_python = SparkKubernetesOperatorExt(
            task_id="test_k8s_pyspark_job",
            namespace="spark-operator",
            application_file="resources/k8s_preflight.yaml",
            **resolve_in_cluster_param(),
            dag=dag,
            poll_interval=10,
            logging_interval=30,
            get_logs=False,
            startup_timeout_seconds=5*60,
            execution_timeout=pendulum.duration(minutes=10),
            deferrable=True,
            log_events_on_failure=False,
            log_pod_spec_on_failure=False,
            delete_on_termination=False,
            post_execute=handle_post_execute,
            params={
                'adip_spark_image': image,
                'tailscale_tag': "tag:k8s-adip-dev" if environment != 'prod' else "tag:k8s-adip-prod",
                "specType": "Python",
                "appName": "preflight-python",
                "k8s_namespace": "spark-operator",
                "serviceAccount": K8S_SERVICE_ACCOUNT,
                "pyFiles": [f"{k8s_artifacts_path}/unarchiver-0.0.1-py3.10.egg"],
                "mainApplicationFile": f"{k8s_artifacts_path}/generic_runner.py",
                "additionalJars": [],
                "arguments": [
                    "unarchiver.unarchiver",
                     os.path.join(k8s_artifacts_path, "coding_systems.csv"),
                     os.path.join(k8s_artifacts_path, "preflight_tmp"),
                     ""
                 ],
                "stale_pending_executors_threshold": STALE_PENDING_EXECUTORS_THRESHOLD,
                "log4j2_feature": PREFLIGHT_LOG4J2_FEATURE,
                "celeborn_feature": CelebornFeature(enabled=False),
                "spark_monitoring_feature": EFFECTIVE_SPARK_MONITORING_FEATURE,
                "python_path_env": EFFECTIVE_ISOLATED_PYPTHON_DEPS_FEATURE.generate_python_path_env(
                    f"{k8s_artifacts_path}/generic_runner.py",
                    ["unarchiver.unarchiver"],
                    image
                ),
                "spark_conf": spark_conf,
                "labels": {},
                "driver": {"cores": 1, "memory": "700m"},
                "executor": {"cores": 1, "memory": "700m", "instances": 1},
            }
        )

        check_s3 >> test_k8s_spark_job_via_python


        celeborn_task = PreflightCelebornTaskContext(
            dag=dag,
            environment=environment,
            k8s_service_account=K8S_SERVICE_ACCOUNT,
            k8s_artifacts_path=k8s_artifacts_path,
            adip_spark_image=PREFLIGHT_ADIP_SPARK_IMAGE,
            log4j2_feature=PREFLIGHT_LOG4J2_FEATURE,
            spark_monitoring_feature=EFFECTIVE_SPARK_MONITORING_FEATURE,
            stale_pending_executors_threshold=STALE_PENDING_EXECUTORS_THRESHOLD,
        ).add_to_dag_if_celeborn_enabled_or_skip()

        check_s3 >> celeborn_task

